import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/cubit/check_in_cubit.dart';
import 'package:embark/check_in/views/check_in_page.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/services.dart';
import 'package:flutter/material.dart';
import 'package:embark/starter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:embark/check_in/check_in_type.dart';

/// The root page that renders the [EmbarkScaffold] and the tabs.
class CheckInView extends StatelessWidget {
  const CheckInView({
    super.key,
    this.checkInType = CheckInType.other,
  });

  final CheckInType checkInType;

  @override
  Widget build(BuildContext context) {
    final biometricRepository = getIt.get<BiometricRepository>();
    final imageCompression = getIt.get<ImageCompression>();
    final connectivityStream = getIt.get<Stream<List<ConnectivityResult>>>();
    final locationRepo = getIt.get<LocationRepository>();
    final cameraUtil = getIt.get<CameraUtil>();
    final deviceSetupRepository = getIt.get<DeviceSetupRepository>();
    final sharedPreferencesService = getIt.get<SharedPreferencesService>();
    return BlocProvider<CheckinCubit>(
      create: (context) => CheckinCubit(
        biometricRepository: biometricRepository,
        imageCompression: imageCompression,
        connectivityStream: connectivityStream,
        locationRepository: locationRepo,
        cameraUtil: cameraUtil,
        deviceSetupRepository: deviceSetupRepository,
        sharedPreferencesService: sharedPreferencesService,
        checkInType: checkInType,
        timestampRepository: getIt.get(),
        faceDetectionUtil: getIt.get(),
        permissionHandler: getIt.get(),
        appRepository: getIt.get(instanceName: 'AppRepository'),
        clientSettingsRepository: getIt.get(),
      )..setUp(),
      child: CheckInPage(
        checkInType: checkInType,
      ),
    );
  }
}
