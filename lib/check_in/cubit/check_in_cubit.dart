import 'dart:async';
import 'dart:convert';

import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/check_in_type.dart';
import 'package:embark/check_in/cubit/check_in_state.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_permission_handler.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:permission_handler/permission_handler.dart';

class CheckinCubit extends Cubit<CheckinState> with SubscriptionWatcher {
  CheckinCubit({
    required this.biometricRepository,
    required this.imageCompression,
    required this.locationRepository,
    required this.connectivityStream,
    required this.cameraUtil,
    required this.deviceSetupRepository,
    required this.sharedPreferencesService,
    required this.faceDetectionUtil,
    required this.checkInType,
    required CheckinTimestampRepository timestampRepository,
    required this.permissionHandler,
    required this.appRepository,
    required this.clientSettingsRepository,
  })  : _timestampRepository = timestampRepository,
        super(const CheckinState(status: CheckInStatus.initial));

  final BiometricRepository biometricRepository;
  final ImageCompression imageCompression;
  final LocationRepository locationRepository;
  final Stream<List<ConnectivityResult>> connectivityStream;
  final FaceDetectionUtil faceDetectionUtil;
  bool isUploading = false;
  final CameraUtil cameraUtil;
  final DeviceSetupRepository deviceSetupRepository;
  // ignore: deprecated_member_use_from_same_package
  final PermissionHandler permissionHandler;
  final AppRepository appRepository;
  final ClientSettingsRepository clientSettingsRepository;
  List<String> deniedPermissions = [];
  bool _canProcessImageForLiveness = true;
  bool hasUsersFace = false;

  SharedPreferencesService sharedPreferencesService;
  List<ConnectivityResult>? connectivityResult;
  final CheckinTimestampRepository _timestampRepository;
  CheckInType checkInType;
  Timer? timer;

  Future<void> setUp() async {
    await _checkLocation();
    subscriptions.add(
      connectivityStream.listen((event) {
        connectivityResult = event;
      }),
    );
  }

  Future<void> _initializeCamera() async {
    if (!(faceDetectionUtil.cameraController?.value.isInitialized ?? false)) {
      await faceDetectionUtil
          .initializeCamera(resolutionPreset: ResolutionPreset.low, fps: 30)
          .catchError((Object e) {
        if (e is CameraException) {
          const CheckinState(status: CheckInStatus.error);
        }
      });
    } else {
      await faceDetectionUtil.cameraController
          ?.lockCaptureOrientation(DeviceOrientation.portraitUp);
    }
  }

  Future<void> _checkPermissions(Size size) async {
    deniedPermissions = await cameraUtil.listDeniedPermissions();
    if (deniedPermissions.isNotEmpty &&
        deviceSetupRepository.lastKnownDeviceType != ResolvedDeviceType.mdm) {
      emit(
        CheckinState(
          status: CheckInStatus.initial,
          deniedPermissions: deniedPermissions,
        ),
      );
      emit(
        CheckinState(
          status: CheckInStatus.rejectedPermissions,
          deniedPermissions: deniedPermissions,
        ),
      );
      return;
    }
    // if no permissions blocking then continue check in
    await beginFacialDetection(size);
  }

  Future<void> _checkLocation() async {
    final locationPermission =
        await permissionHandler.grantStatus(Permission.location);
    if (locationPermission != PermissionStatus.granted &&
        deviceSetupRepository.lastKnownDeviceType != ResolvedDeviceType.mdm) {
      emit(
        CheckinState(
          status: CheckInStatus.rejectedLocation,
          deniedPermissions: deniedPermissions,
        ),
      );
    }
  }

  Future<bool> _hasCameraPermission() async {
    deniedPermissions = await cameraUtil.listDeniedPermissions();
    final l10n = await appRepository.loadL10n();
    if (deniedPermissions.contains(l10n.camera) &&
        deviceSetupRepository.lastKnownDeviceType != ResolvedDeviceType.mdm) {
      emit(const CheckinState(status: CheckInStatus.initial));
      return false;
    }
    return true;
  }

  Future<void> continueCheckin(Size size) async {
    await _initializeCamera();
    await _checkPermissions(size);
  }

  Future<void> _faceDetection() async {
    // block conncurrent calls to upload
    if (isUploading || hasUsersFace) {
      return;
    }
    hasUsersFace = true;
    // take user image for posting
    final photo = await faceDetectionUtil.captureAndCompressImage(0);
    if (photo != null) {
      await userLive(photo);
    }
  }

  Future<void> beginFacialDetection(Size screenSize) async {
    if (!await _hasCameraPermission()) return;
    await _initializeCamera();
    final scale = cameraUtil.calculateScale(
      screenSize,
      faceDetectionUtil.cameraController,
    );
    emit(
      CheckinState(
        status: CheckInStatus.cameraOpen,
        captureButtonPressed: true,
        screenSize: scale,
        cameraController: faceDetectionUtil.cameraController,
      ),
    );

    /// if the user face not detected in 15 seconds, submit with isAlive false
    unawaited(
      Future.delayed(
        const Duration(seconds: 15),
        () async {
          // wait for the timer to finish
          if (!_canProcessImageForLiveness) return;
          _canProcessImageForLiveness = false;
          while (timer?.isActive ?? false) {
            await Future<void>.delayed(const Duration(milliseconds: 500));
          }

          final photo = await faceDetectionUtil.captureAndCompressImage(1);
          if (photo == null) return;
          await userNotLive(photo);
        },
      ),
    );

    // Initial delay before starting face detection
    await Future<void>.delayed(const Duration(seconds: 2));

    try {
      timer?.cancel();

      bool isTakingPhoto = false;
      timer = Timer.periodic(const Duration(milliseconds: 500), (t) async {
        /// camera must complete writing image data before attemting to take
        /// another image. We need to block excessive attempts in order to
        /// prevent camera crashes.
        /// Looks like reallistically this has a hard limit of a photo a second
        if (!_canProcessImageForLiveness) {
          timer?.cancel();
          return;
        }

        if (isTakingPhoto) return;

        isTakingPhoto = true;
        await faceDetectionUtil.cameraController?.setFlashMode(FlashMode.off);
        // faceDetectionUtil = await faceDetectionUtilInit();
        final picture = await faceDetectionUtil.findValidFacesForEnrollment(0);
        isTakingPhoto = false;

        if (picture != null) {
          await userLive(picture);
          _canProcessImageForLiveness = false;
        }
      });
    } catch (e) {
      timer?.cancel();
      _canProcessImageForLiveness = false;
    }
    return;
  }

  Future<void> processImagesStream(CameraImage image) async {
    if (_canProcessImageForLiveness) {
      return;
    }

    final faceFound = await faceDetectionUtil.analyzeCameraImageForFace(image);
    if (faceFound) {
      _canProcessImageForLiveness = true;
      await _faceDetection();
    }
  }

  Future<void> userLive(XFile photo) async {
    if (!state.captureButtonPressed) return;
    emit(
      CheckinState(
        status: CheckInStatus.imageCaptured,
        screenSize: state.screenSize,
        cameraController: faceDetectionUtil.cameraController,
        validImage: photo,
        isAlive: true,
      ),
    );
    if (isUploading) return;

    isUploading = true;

    await upload(photo, isAlive: true);
  }

  Future<void> userNotLive(XFile photo) async {
    if (!state.captureButtonPressed) return;
    emit(
      CheckinState(
        status: CheckInStatus.imageCaptured,
        cameraController: faceDetectionUtil.cameraController,
        screenSize: state.screenSize,
        validImage: photo,
        isAlive: false,
      ),
    );
    if (isUploading) return;
    isUploading = true;

    await upload(
      photo,
      isAlive: false,
    );
  }

  Future<PostSelfReportAnswerRequestWithPhoto> buildSelfReportWithPhoto(
    XFile image,
  ) async {
    final data = await image.readAsBytes();
    geo.Position? lastLocation;

    final locationPermission =
        await permissionHandler.grantStatus(Permission.location);
    if (locationPermission == PermissionStatus.granted &&
        await locationRepository.serviceEnabled()) {
      lastLocation = await locationRepository.requestDeviceLocation();
    }
    final compressed = await imageCompression.compress(
      data,
      quality: 80,
    );

    return PostSelfReportAnswerRequestWithPhoto(
      accuracy: lastLocation?.accuracy ?? 0,
      satellites: null,
      cellStrength: null,
      answers: [],
      fingerprintConfirmed: null,
      hdop: null,
      heading: lastLocation?.heading,
      speed: lastLocation?.speed,
      photo: base64Encode(compressed),
      locationAcquired: lastLocation != null,
      locationServicesEnabled: locationPermission == PermissionStatus.granted,
      fixType: LocationFixType.gps.value,
      latitude: lastLocation?.latitude ?? 0.0,
      longitude: lastLocation?.longitude ?? 0.0,
      useWifi: connectivityResult?.lastOrNull == ConnectivityResult.wifi
          ? true
          : false,
      isAlive: state.isAlive,
      resetPin: sharedPreferencesService.getBool(
        SharedPreferencesKeys.resettingPinUnderWay.key,
        false,
      ),
    );
  }

  Future<void> upload(XFile? photo, {required bool isAlive}) async {
    if (checkInType == CheckInType.selfReport) {
      await Future.delayed(const Duration(seconds: 3), () {
        emit(
          CheckinState(
            status: CheckInStatus.checkInSuccess,
            cameraController: faceDetectionUtil.cameraController,
            screenSize: state.screenSize,
            validImage: photo,
            isAlive: isAlive,
          ),
        );
      });
      return;
    }
    try {
      final data = await photo!.readAsBytes();
      geo.Position? lastLocation;

      final locationPermission =
          await permissionHandler.grantStatus(Permission.location);
      if (locationPermission == PermissionStatus.granted &&
          await locationRepository.serviceEnabled()) {
        lastLocation = await locationRepository.requestDeviceLocation();
      }
      final compressed = await imageCompression.compress(
        data,
        quality: 80,
      );

      final bool isPinBeingReset = checkInType == CheckInType.pinReset;
      unawaited(
        Future(() async {
          await biometricRepository.postCheckInPhoto(
            CheckInRequest(
              accuracy: lastLocation?.accuracy ?? 0,
              photo: base64Encode(compressed),
              locationAcquired: lastLocation != null,
              locationServicesEnabled:
                  locationPermission == PermissionStatus.granted,
              fixType: LocationFixType.gps.value,
              latitude: lastLocation?.latitude ?? 0.0,
              longitude: lastLocation?.longitude ?? 0.0,
              useWifi: connectivityResult?.lastOrNull == ConnectivityResult.wifi
                  ? true
                  : false,
              isAlive: isAlive,
              resetPin: isPinBeingReset,
            ),
          );
          await clientSettingsRepository.reloadClientSettings(
            after: const Duration(seconds: 1),
          );
        }),
      );

      final delay =
          isPinBeingReset ? Duration.zero : const Duration(seconds: 3);
      await Future.delayed(delay, () async {
        isUploading = false;
        emit(
          CheckinState(
            status: CheckInStatus.checkInSuccess,
            validImage: photo,
          ),
        );
      });
      if (checkInType != CheckInType.selfReport) {
        await _timestampRepository.saveAdHocCheckIn();
      }

      return;
    } catch (e) {
      isUploading = false;
      throw Exception('Api post error $e');
    }
  }

  Future<void> backPressed() async {
    isUploading = false;

    if (state.status == CheckInStatus.cameraOpen) {
      await faceDetectionUtil.cameraController?.stopImageStream();
      await faceDetectionUtil.resetCamera();
    }

    emit(
      CheckinState(
        status: CheckInStatus.initial,
        deniedPermissions: const [],
        validImage: null,
        screenSize: state.screenSize,
        captureButtonPressed: false,
      ),
    );
  }

  @override
  Future<void> close() async {
    await faceDetectionUtil.dispose();
    timer?.cancel();

    await super.close();
  }

  @visibleForTesting
  void stopImageProcessing() {
    _canProcessImageForLiveness = false;
  }
}
