import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:embark/location/location.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:flutter/foundation.dart';

class DeviceInfoRepository {
  final SmartLinkAppSettingsHttpClient _appSettingsHttpClient;
  final LocationRepository _locationRepository;
  final BatteryRepository _batteryRepository;
  final AppRepository _appRepository;
  final InternetConnectivityRepository _internetConnectivityRepository;
  final PushNotificationsRepository _pushNotificationsRepository;
  final DeviceInfoPlugin _deviceInfoPlugin;
  final BiPackageInfo _biPackageInfo;
  final BiLogger _biLogger;

  DeviceInfoRepository(
    this._appSettingsHttpClient,
    this._locationRepository,
    this._batteryRepository,
    this._appRepository,
    this._internetConnectivityRepository,
    this._pushNotificationsRepository,
    this._deviceInfoPlugin,
    this._biPackageInfo,
    this._biLogger,
  );

  Future<BaseDeviceInfo> getDeviceInfo() async =>
      defaultTargetPlatform == TargetPlatform.android
          ? await _deviceInfoPlugin.androidInfo
          : await _deviceInfoPlugin.iosInfo;

  Future<String> getDeviceModel() async {
    final deviceInfo = await getDeviceInfo();

    if (deviceInfo is AndroidDeviceInfo) {
      return deviceInfo.model;
    } else if (deviceInfo is IosDeviceInfo) {
      return deviceInfo.utsname.machine;
    }

    return 'Unknown';
  }

  Future<String> getOsVersion() async {
    final deviceInfo = await getDeviceInfo();

    if (deviceInfo is AndroidDeviceInfo) {
      final version = deviceInfo.version.release;
      return version;
    } else if (deviceInfo is IosDeviceInfo) {
      return deviceInfo.systemVersion;
    }

    return 'Unknown';
  }

  String _getPlatform() => defaultTargetPlatform == TargetPlatform.android
      ? 'Android'
      : defaultTargetPlatform == TargetPlatform.iOS
          ? 'iOS'
          : 'Unknown';

  Future<PositionFix> getPositionFix() async {
    final locationPermission =
        await _locationRepository.requestLocationPermission();
    final isLocationPermissionEnabled =
        locationPermission == LocationPermissionStatus.enabled;
    final Position position;
    switch (locationPermission) {
      case LocationPermissionStatus.disabled:
      case LocationPermissionStatus.denied:
        return PositionFix(
          fixTypeId: LocationFixType.gps.value,
          locationServicesEnabled: await _locationRepository.serviceEnabled() &&
              isLocationPermissionEnabled,
          positionAcquired: false,
        );
      case LocationPermissionStatus.enabled:
        final geoPosition = await _locationRepository.getLastKnownLocation();

        position = Position(
          latitude: geoPosition?.latitude,
          longitude: geoPosition?.longitude,
          accuracy: geoPosition?.accuracy,
          heading: geoPosition?.heading,
          speed: geoPosition?.speed,
          timestamp: geoPosition?.timestamp.toIso8601String(),
          hdop: 0,
          cellStrength: null,
          satellites: null,
        );

        if (geoPosition == null) {
          await _biLogger.info(
            message: 'Failed to get position fix',
            debugProps: {
              'locationPermission': locationPermission.toString(),
              'locationServicesEnabled':
                  await _locationRepository.serviceEnabled() &&
                      isLocationPermissionEnabled,
            },
          );
        }
        return PositionFix(
          fixTypeId: LocationFixType.gps.value,
          locationServicesEnabled: await _locationRepository.serviceEnabled() &&
              isLocationPermissionEnabled,
          position: position,
          positionAcquired: geoPosition != null,
        );
    }
  }

  Future<DeviceInformationRequest> _getDeviceInformationRequest() async {
    final reachability = await _internetConnectivityRepository.reachability;

    final request = DeviceInformationRequest(
      appVersion: _biPackageInfo.package.version,
      batteryCharging: await _batteryRepository.isBatteryCharging(),
      batteryLevel: await _batteryRepository.getBatteryLevel(),
      culturalCode: _appRepository.getStoredLocale().toString(),
      model: await getDeviceModel(),
      osVersion: await getOsVersion(),
      platform: _getPlatform(),
      positionFix: await getPositionFix(),
      pushNotificationsEnabled:
          _pushNotificationsRepository.arePushNotificationsEnabled(),
      usingMobileData: reachability.lastOrNull == ConnectivityResult.mobile,
      usingWiFi: reachability.lastOrNull == ConnectivityResult.wifi,
    );

    return request;
  }

  Future<void> postDeviceInfo() async {
    try {
      final request = await _getDeviceInformationRequest();
      await _appSettingsHttpClient.postDeviceInformation(request);
    } catch (e) {
      await _biLogger.error(
        message: 'Failed to post device info',
        error: e,
      );
    }
  }
}
