import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class InternetConnectivityRepository extends Closable {
  final Connectivity _checker;

  final List<MultiStreamController<List<ConnectivityResult>>> _controllers = [];

  late Stream<List<ConnectivityResult>> connectionStream;

  StreamSubscription<List<ConnectivityResult>>? _internetStreamSubscription;

  List<ConnectivityResult>? _latestStatus;

  InternetConnectivityRepository(this._checker) {
    _internetStreamSubscription =
        _checker.onConnectivityChanged.listen(_updateStatus);

    connectionStream = Stream<List<ConnectivityResult>>.multi((controller) {
      _controllers.add(controller);

      // seed the first value.
      if (_latestStatus != null) {
        controller.add(_latestStatus!);
      }

      controller.onCancel = () {
        unawaited(controller.close());
        _controllers.remove(controller);
      };
    });
  }

  Future<List<ConnectivityResult>> get reachability async {
    _updateStatus(await _checker.checkConnectivity());

    return _latestStatus!;
  }

  List<ConnectivityResult>? get status => _latestStatus;

  void _updateStatus(List<ConnectivityResult> newStatus) {
    final originalStatus = _latestStatus;
    _latestStatus = newStatus;

    if (originalStatus != _latestStatus) {
      for (final controller in _controllers) {
        controller.add(_latestStatus!);
      }
    }
  }

  @override
  FutureOr<void> close() async {
    for (final controller in _controllers) {
      unawaited(controller.close());
    }
    _controllers.clear();
    await _internetStreamSubscription?.cancel();
    _internetStreamSubscription = null;
  }

  @override
  bool get isClosed => _internetStreamSubscription == null;
}
