import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:widgets/widgets.dart';
import 'package:embark/starter.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/community_refferal/views/views.dart';

class CommunityReferralSummary extends StatelessWidget {
  final ItemSavedDelegate<CommunityReferral?>? onCommunityReferralSaved;
  final dynamic Function()? onCommunityReferralLoaded;
  final BiPositiveButtonPurpose editCommunityReferralSubmitButtonPurpose;
  final void Function(List<CommunityReferral>) onSummaryLoaded;

  const CommunityReferralSummary({
    super.key,
    this.onCommunityReferralLoaded,
    required this.editCommunityReferralSubmitButtonPurpose,
    this.onCommunityReferralSaved,
    required this.onSummaryLoaded,
  });

  String _translateStatusName(BuildContext context, String statusId) {
    switch (statusId) {
      case '1':
        return context.l10n.pending;
      case '2':
        return context.l10n.ongoing;
      case '3':
        return context.l10n.utilized;
      case '4':
        return context.l10n.notUtilized;
      default:
        return context.l10n.unknown;
    }
  }

  @override
  Widget build(BuildContext context) {
    final communityReferralRepository =
        getIt.get<CommunityReferralRepository>();
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return BiEditableListView<CommunityReferral>(
      onItemsLoaded: onCommunityReferralLoaded,
      load: () async {
        final response =
            await communityReferralRepository.fetchCommunityReferrals();
        onSummaryLoaded(response.map((p) => p.data).toList());
        return response;
      },
      canDelete: (item) => false,
      canAdd: () => false,
      canEdit: (item) => true,
      titleBuilder: (item) => item.name,
      subtitleBuilder: (item) => Text(
        _translateStatusName(context, item.referralStatus),
      ),
      onItemSaved: onCommunityReferralSaved,
      editItemTitle: context.l10n.communityReferral,
      editFormSaveButtonPurpose: editCommunityReferralSubmitButtonPurpose,
      onEditItemInit: (item) =>
          communityReferralRepository.fetchReferralStatuses(),
      editItemFormFields: (context, initSnapshotData, item) {
        return CommunityReferralFormFields(
          referralStatusResponse: initSnapshotData as GetReferralStatusResponse,
          communityReferral: item,
        );
      },
      internetConnectionStream: connectionStatus,
    );
  }
}
