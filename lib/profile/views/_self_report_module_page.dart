import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/cubit/_self_report_state.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:widgets/widgets.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';

class ProfileSelfReportModulePage extends StatelessWidget {
  const ProfileSelfReportModulePage({
    super.key,
    required this.childWidget,
    required this.title,
  });

  final Widget childWidget;
  final String title;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return EmbarkScaffold(
      title: title,
      body: SafeArea(
        // ignore: deprecated_member_use
        child: <PERSON><PERSON><PERSON>mbarkBlocProvider<SelfReportCubit, SelfReportState>(
          create: (_) => getIt.get<SelfReportCubit>(),
          init: (cubit) => cubit.init(
            l10n: l10n,
            // US25312/DE14345: profile page so don't load the questions.
            loadQuestions: false,
          ),
          internetConnectivityStream: connectionStatus,
          child: childWidget,
        ),
      ),
      automaticallyImplyLeading: false,
    );
  }
}
