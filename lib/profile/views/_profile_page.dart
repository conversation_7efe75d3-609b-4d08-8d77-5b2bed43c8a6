import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/client_settings/views/_plugin_requirement.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:embark/profile/views/_profile_view.dart';
import 'package:flutter/material.dart';
import 'package:embark/profile/cubit/cubit.dart';
import 'package:embark/starter.dart';
import 'package:widgets/widgets.dart';

class ProfilePage extends StatelessWidget {
  final BiLogger logger;
  const ProfilePage({
    super.key,
    required this.logger,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return EmbarkScaffold(
      title: l10n.profile,
      body: PluginRequirement(
        pluginType: PluginType.myInfo,
        child: SafeArea(
          // ignore: deprecated_member_use
          child: BiEmbarkBlocProvider<ProfileCubit, ProfileState>(
            create: (_) => getIt.get<ProfileCubit>(),
            init: (cubit) => cubit.init(l10n: l10n),
            internetConnectivityStream: connectionStatus,
            child: ProfileView(
              logger: logger,
            ),
          ),
        ),
      ),
    );
  }
}
