import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/permissions/cubit/permissions_cubit.dart';
import 'package:embark/permissions/views/permissions_view.dart';
import 'package:flutter/material.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:embark/starter.dart';
import 'package:widgets/widgets.dart';

class PermissionsPage extends StatelessWidget {
  const PermissionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return Scaffold(
      body: SafeArea(
        // ignore: deprecated_member_use
        child: BiEmbarkBlocProvider<PermissionsCubit, PermissionsState>(
          create: (BuildContext context) => getIt.get<PermissionsCubit>(),
          internetConnectivityStream: connectionStatus,
          child: PermissionsView(
            l10n: AppLocalizations.of(context),
          ),
        ),
      ),
    );
  }
}
