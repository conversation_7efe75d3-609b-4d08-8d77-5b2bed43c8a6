import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/call_in_progress/views/_call_in_progress_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:widgets/widgets.dart';
import 'package:embark/starter.dart';
import 'package:embark/call_in_progress/cubit/call_in_progress_cubit.dart';
import 'package:embark/call_in_progress/cubit/call_in_progress_state.dart';

class CallInProgressPage extends StatelessWidget {
  final String phoneNumber;
  final bool isOutBound;

  /// this is null if this call type is incoming
  final String? toIdentifier;

  const CallInProgressPage({
    super.key,
    required this.phoneNumber,
    required this.isOutBound,
    required this.toIdentifier,
  });

  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return BiEmbarkBlocProvider<CallInProgressCubit, CallInProgressState>(
      create: (_) => getIt.get<CallInProgressCubit>()
        ..init(phoneNumber, isOutBound, toIdentifier),
      internetConnectivityStream: connectionStatus,
      child: CallInProgressView(
        phoneNumber: phoneNumber,
      ),
    );
  }
}
