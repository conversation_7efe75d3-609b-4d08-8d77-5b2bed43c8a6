import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/open_text_change_request/cubit/_open_text_change_request_cubit.dart';
import 'package:embark/open_text_change_request/cubit/_open_text_change_request_state.dart';
import 'package:embark/open_text_change_request/views/_bi_open_text_form_fields.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:widgets/widgets.dart';

class OpenTextChangeRequestForm extends StatelessWidget {
  /// A callback used when the underlying open text form has valid changes.
  final void Function()? onReady;

  const OpenTextChangeRequestForm({
    super.key,
    this.onReady,
  });

  @override
  Widget build(BuildContext context) {
    final questionForDisplay =
        BlocProvider.of<SelfReportCubit>(context).state.currentState.question;
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    // ignore: deprecated_member_use
    return BiEmbarkBlocProvider<OpenTextChangeRequestCubit,
        OpenTextChangeRequestState>(
      create: (_) => getIt.get<OpenTextChangeRequestCubit>(),
      init: (cubit) => cubit.init(questionToDisplay: questionForDisplay),
      internetConnectivityStream: connectionStatus,
      child:
          BlocConsumer<OpenTextChangeRequestCubit, OpenTextChangeRequestState>(
        listener: (context, state) {
          if (state.status == OpenFormStatus.loaded) {
            onReady?.call();
          }
        },
        builder: (context, state) {
          if (state.status != OpenFormStatus.loaded) {
            return const BiFormOpenTextLoading();
          }

          return BiEmbarkOpenTextFormFields(
            initialValues: state.currentRequest,
          );
        },
      ),
    );
  }
}
