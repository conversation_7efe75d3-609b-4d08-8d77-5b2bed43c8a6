import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/transportation_change_request/views/views.dart';
import 'package:embark/transportation_change_request/models/models.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/starter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:widgets/widgets.dart';

/// Renders a transportation summary list
class TransportationSummary extends StatelessWidget {
  /// If provided, this callback is called whenever a user taps "OK" on the
  /// "delete confirmation" dialog. The value provided is the value ot be deleted.
  final ItemDeletedDelegate<Transportation>? onTransportationDeleted;

  /// If provided, this callback is called whenever a user taps "Submit" on the
  /// "add/update" transporation page. The value provided is the value that after
  /// it has had all modifications applied to it.
  final ItemSavedDelegate<Transportation?>? onTransportationSaved;

  /// If provided, triggers after the list of transportation methods have loaded.
  final dynamic Function()? onTransportationLoaded;

  /// The button type to show when creating/editing a transportation item
  final BiPositiveButtonPurpose editTransportationSubmitButtonPurpose;

  final void Function(List<Transportation>) onSummaryLoaded;

  const TransportationSummary({
    super.key,
    this.onTransportationDeleted,
    this.onTransportationSaved,
    this.onTransportationLoaded,
    required this.editTransportationSubmitButtonPurpose,
    required this.onSummaryLoaded,
  });

  String _translateTypeName(int typeId, AppLocalizations l10n) {
    switch (TransportationType.fromInt(typeId)) {
      case TransportationType.personalVehicle:
        return l10n.personalVehicle;
      case TransportationType.publicTransportation:
        return l10n.publicTransportation;
      case TransportationType.personalContact:
        return l10n.personalContact;
      case TransportationType.taxi:
        return l10n.taxi;
      case TransportationType.bike:
        return l10n.bicycle;
      case TransportationType.walk:
        return l10n.walk;
      case TransportationType.other:
        return l10n.otherSpecify;
    }
  }

  @override
  Widget build(BuildContext context) {
    final transportationRepo = getIt.get<TransportationRepository>();
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return BiEditableListView<Transportation>(
      onItemsLoaded: onTransportationLoaded,
      load: () async {
        final response = await transportationRepo.fetchTransporation();
        onSummaryLoaded(response.map((t) => t.data).toList());
        return response;
      },
      addButtonText: context.l10n.addTransportation,
      deleteConfirmationText: context.l10n.deleteTransporationConfirm,
      canDelete: (item) => item.isPrimary != true,
      canAdd: () => true,
      canEdit: (item) => true,
      titleBuilder: (item) => item.isPrimary == true
          ? '${_translateTypeName(item.typeId, context.l10n)} - ${context.l10n.primary}'
          : _translateTypeName(item.typeId, context.l10n),
      subtitleBuilder: (item) {
        final StringBuffer subTitleBuffer = StringBuffer();
        if (item.color != null) {
          subTitleBuffer.write(item.color);
        }
        if (item.make != null) {
          if (subTitleBuffer.isNotEmpty) {
            subTitleBuffer.write(' ');
          }
          subTitleBuffer.write(item.make);
        }
        if (item.model != null) {
          if (subTitleBuffer.isNotEmpty) {
            subTitleBuffer.write(' ');
          }
          subTitleBuffer.write(item.model);
        }
        if (item.modelYear != null) {
          if (subTitleBuffer.isNotEmpty) {
            subTitleBuffer.write(' ');
          }
          subTitleBuffer.write(item.modelYear);
        }
        return Text(subTitleBuffer.toString());
      },
      onItemDeleted: onTransportationDeleted,
      editItemTitle: context.l10n.transportation,
      editFormSaveButtonPurpose: editTransportationSubmitButtonPurpose,
      onEditItemInit: (item) => transportationRepo.fetchTransporationRules(),
      editItemFormFields: (context, initSnapshotData, item) {
        return BlocProvider<SelfReportCubit>(
          create: (context) => getIt.get<SelfReportCubit>(),
          child: TransportationFormFields(
            rules: initSnapshotData as GetTransportationRulesResponse,
            transportation: item,
          ),
        );
      },
      onItemSaved: onTransportationSaved,
      internetConnectionStream: connectionStatus,
    );
  }
}
