import 'dart:async';
import 'dart:convert';

import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:camera/camera.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/facial_enrollment/bloc/_facial_enrollment_state.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:geolocator/geolocator.dart' as geo;

class FacialEnrollmentCubit extends Cubit<FacialEnrollmentState>
    with SubscriptionWatcher, AuthStep {
  FacialEnrollmentCubit({
    required this.biometricRepository,
    required this.locationRepository,
    required this.permissionHandler,
    required this.appRepository,
    required this.faceDetector,
    required this.faceDetectionUtil,
    required this.compression,
    required this.clientSettingsRepository,
    required AuthManager authManager,
    required this.logger,
    Duration uploadWaitTime = const Duration(seconds: 3),
    int maxDetectionAttempts = 120,
    Future<List<CameraDescription>> Function() getAvailableCameras = availableCameras,
  })  : _maxDetectionAttempts = maxDetectionAttempts, _uploadWaitTime = uploadWaitTime,
        _authManager = authManager,
        super(
          const FacialEnrollmentState.initial(),
        ) {
    _authManager.attachAuthStep(this);
    cameraUtil = CameraUtil(
      permissionHandler: permissionHandler,
      appRepository: appRepository,
    );
    _getAvailableCameras = getAvailableCameras;
  }

  final BiometricRepository biometricRepository;
  final LocationRepository locationRepository;
  // ignore: deprecated_member_use_from_same_package
  final PermissionHandler permissionHandler;
  final AppRepository appRepository;
  final FaceDetector faceDetector;

  final FaceDetectionUtil faceDetectionUtil;
  final ImageCompression compression;
  Timer? timer;
  List<XFile> validImages = [];
  late CameraUtil cameraUtil;
  final AuthManager _authManager;
  final ClientSettingsRepository clientSettingsRepository;
  final BiLogger logger;
  List<String> deniedPermissions = [];
  Future<List<CameraDescription>> Function()? _getAvailableCameras;
  Future<void> setUp() async {
    await _warnLocationCheck();
  }

  Future<void> _warnLocationCheck() async {
    final permissions = await permissionHandler
        .requestLocationPermissions(await appRepository.loadL10n());
    if (permissions.contains('Location')) {
      emit(
        state.copyWith(
          status: FacialEnrollmentStatus.warnLocation,
          deniedPermissions: permissions,
        ),
      );
    }
  }

  Future<bool> _hasCameraPermission() async {
    deniedPermissions = await permissionHandler
        .requestCameraPermissions(await appRepository.loadL10n());

    if (deniedPermissions.contains('Camera')) {
      emit(state.copyWith(status: FacialEnrollmentStatus.initial));
      return false;
    }
    return true;
  }

  Future<void> _checkPermissions(Size size) async {
    deniedPermissions = await permissionHandler
        .requestCameraPermissions(await appRepository.loadL10n());
    if (deniedPermissions.isNotEmpty) {
      emit(
        state.copyWith(
          status: FacialEnrollmentStatus.initial,
          deniedPermissions: deniedPermissions,
        ),
      );
      emit(
        state.copyWith(
          status: FacialEnrollmentStatus.rejectedPermissions,
          deniedPermissions: deniedPermissions,
        ),
      );
      return;
    } else {
      // if no permissions blocking then continue check in
      await launchCamera(size);
    }
  }

  Future<void> continueEnrollment(Size size) async {
    await _checkPermissions(size);
  }

  Future<void> _initializeCamera() async {
    await faceDetectionUtil
        .initializeCamera(resolutionPreset: ResolutionPreset.max)
        .catchError((Object e) {
      if (e is CameraException) {
        state.copyWith(status: FacialEnrollmentStatus.error, exception: e);
        logger.error(error: e, message: 'Enrollment: Error initializing camera');
      }
    });
  }

  Future<void> launchCamera(Size screenSize) async {
    imageCache.clear();
    if (!await _hasCameraPermission()) return;
    await _initializeCamera();
    emit(
      state.copyWith(
        status: FacialEnrollmentStatus.cameraOpen,
        screenSize: cameraUtil.calculateScale(
          screenSize,
          faceDetectionUtil.cameraController,
        ),
        deniedPermissions: deniedPermissions,
        cameraController: faceDetectionUtil.cameraController,
      ),
    );
  }

  Future<void> beginFacialDetection(
    Size size,
  ) async {
    if (!await _hasCameraPermission()) return;

    emit(
      state.copyWith(
        status: FacialEnrollmentStatus.cameraOpen,
        validImages: validImages,
        screenSize: cameraUtil.calculateScale(
          size,
          faceDetectionUtil.cameraController,
        ),
        cameraController: faceDetectionUtil.cameraController,
        imageCount: validImages.length,
        captureButtonPressed: true,
        deniedPermissions: state.deniedPermissions,
      ),
    );
    try {
      timer?.cancel();
      int tryCount = 0;
      // set up facial detection

      bool isTakingPhoto = false;
      unawaited(memoizedLocation());
      timer = Timer.periodic(const Duration(milliseconds: 500), (t) async {
        /// camera must complete writing image data before attemting to take
        /// another image. We need to block excessive attempts in order to
        /// prevent camera crashes.
        /// Looks like reallistically this has a hard limit of a photo a second
        tryCount += 1;

        if (tryCount == _maxDetectionAttempts) {
          timer?.cancel();

          emit(
            state.copyWith(
              validImages: validImages,
              status: FacialEnrollmentStatus.error,
              cameraController: faceDetectionUtil.cameraController,
              deniedPermissions: state.deniedPermissions,
            ),
          );
          logger.error(
            error: 'Enrollment: Facial enrollment timed out',
          );
          return;
        }
        if (isTakingPhoto) return;

        if (validImages.length == 5) {
          timer?.cancel();

          await upload(size);
          return;
        }

        isTakingPhoto = true;
        await faceDetectionUtil.cameraController?.setFlashMode(FlashMode.off);
        // faceDetectionUtil = await faceDetectionUtilInit();
        final picture = await faceDetectionUtil
            .findValidFacesForEnrollment(validImages.length);
        isTakingPhoto = false;
        if (picture != null) {
          state.validImages?.add(picture);
        }
        if (timer?.isActive ?? false) {
          emit(
            state.copyWith(
              status: FacialEnrollmentStatus.cameraOpen,
              cameraController: faceDetectionUtil.cameraController,
              screenSize: cameraUtil.calculateScale(
                size,
                faceDetectionUtil.cameraController,
              ),
              validImages: validImages,
              imageCount: validImages.length,
              captureButtonPressed: true,
            ),
          );
        }
      });
    } catch (e) {
      emit(
        state.copyWith(
          status: FacialEnrollmentStatus.error,
          validImages: validImages,
          deniedPermissions: state.deniedPermissions,
        ),
      );
      logger.error(error: e, message: 'Enrollment: Error taking picture');
    }
  }

  void resetToInitial() {
    imageCache.clear();
    timer?.cancel();
    validImages = [];
    _lastLocation = null;
    emit(
      state.copyWith(
        status: FacialEnrollmentStatus.initial,
        validImages: [],
        deniedPermissions: state.deniedPermissions,
      ),
    );
  }

  @visibleForTesting
  Future<geo.Position?> memoizedLocation() async {
    Future<geo.Position?> calculate() async {
      if (deniedPermissions.isEmpty &&
          await locationRepository.serviceEnabled()) {
        return await locationRepository.requestDeviceLocation();
      }
      return null;
    }

    return _lastLocation ??= calculate();
  }

  Future<geo.Position?>? _lastLocation;
  final Duration _uploadWaitTime;
  final int _maxDetectionAttempts;

  @visibleForTesting
  Future<void> upload(Size scale) async {
    try {
      final photos = <String>[];
      geo.Position? lastLocation;
      final wait = Future<dynamic>.delayed(_uploadWaitTime);

      /// denied permissions should only contain location at this point if any

      lastLocation = await memoizedLocation();
      for (final XFile photo in state.validImages ?? []) {
        final compressed =
            await compression.compress(await photo.readAsBytes(), quality: 80);
        photos.add(
          base64Encode(compressed),
        );
      }

      final upload = biometricRepository.postFacialEnrollmentPhotos(
        FacialEnrollmentRequest(
          locationAcquired: lastLocation != null,
          accuracy: lastLocation?.accuracy.toInt(),
          locationServicesEnabled: deniedPermissions.isEmpty,
          photos: photos,
          fixType: LocationFixType.gps.value,
          latitude: lastLocation?.latitude ?? 0.0,
          longitude: lastLocation?.longitude ?? 0.0,
        ),
      );
      await Future.wait(
        [
          wait,
          upload,
        ],
      );
      emit(
        state.copyWith(
          status: FacialEnrollmentStatus.enrollmentSuccess,
          validImages: state.validImages,
          deniedPermissions: state.deniedPermissions,
          captureButtonPressed: true,
        ),
      );
      await Future.delayed(_uploadWaitTime, () async {
        final settings = await clientSettingsRepository.reloadClientSettings();
        emit(
          state.copyWith(
            status: FacialEnrollmentStatus.enrollmentComplete,
            validImages: state.validImages,
            deniedPermissions: state.deniedPermissions,
            captureButtonPressed: true,
          ),
        );
        imageCache.clear();
        if (settings?.enrolledFacial == true) {
          await _authManager.runAuthSteps();
        } else {
          resetToInitial();
        }
      });

      return;
    } catch (e) {
      Exception exception;
      if (e is Exception) {
        exception = e;
      } else {
        exception = Exception(e.toString());
      }
      emit(
        state.copyWith(
          status: FacialEnrollmentStatus.error,
          exception: exception,
          validImages: [],
        ),
      );
      logger.error(error: e, message: 'Enrollment: Error uploading photos');
    }
  }

  Future<void> flipCamera(Size size) async {
    final lensDirection =
        faceDetectionUtil.cameraController?.description.lensDirection;
    CameraDescription newDescription;
    final availableCams = await _getAvailableCameras!();

    if (lensDirection == CameraLensDirection.front) {
      newDescription = availableCams.firstWhere(
        (description) => description.lensDirection == CameraLensDirection.back,
      );
    } else {
      newDescription = availableCams.firstWhere(
        (description) => description.lensDirection == CameraLensDirection.front,
      );
    }

    await faceDetectionUtil.cameraController?.pausePreview();
    faceDetectionUtil.cameraController = CameraController(
      CameraDescription(
        lensDirection: newDescription.lensDirection,
        name: newDescription.name,
        sensorOrientation: 0,
      ),
      ResolutionPreset.high,
      enableAudio: false,
    );
    await faceDetectionUtil.cameraController?.initialize();
    emit(
      state.copyWith(
        status: state.status,
        validImages: validImages,
        screenSize: cameraUtil.calculateScale(
          size,
          faceDetectionUtil.cameraController,
        ),
        cameraController: faceDetectionUtil.cameraController,
        imageCount: validImages.length,
        captureButtonPressed: state.captureButtonPressed,
        deniedPermissions: state.deniedPermissions,
      ),
    );
    if (state.captureButtonPressed) {
      await beginFacialDetection(size);
    }
  }

  @override
  Future<void> close() async {
    await super.close();
    await faceDetectionUtil.dispose();
    _authManager.removeAuthStep(this);
  }

  @override
  Future<AuthenticationState?> nextStep(
    AuthenticationStatusBase status,
    GetClientSettingsResponse? clientSettingsResponse,
  ) async {
    switch (state.status) {
      case FacialEnrollmentStatus.enrollmentSuccess:
      case FacialEnrollmentStatus.cameraOpen:
        return const EmbarkAuthenticationState.enrollment();
      default:
        return null;
    }
  }
}
