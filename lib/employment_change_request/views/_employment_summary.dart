import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/device_setup/extensions/_build_context_extensions.dart';
import 'package:embark/employment_change_request/views/views.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/resources/utils/url_launcher_util.dart';
import 'package:embark/routing/navigator_extensions.dart';
import 'package:embark/starter.dart';
import 'package:flutter/cupertino.dart';
import 'package:widgets/widgets.dart';

class EmploymentSummary extends StatelessWidget {
  final ItemDeletedDelegate<Employment>? onEmploymentDeleted;
  final ItemSavedDelegate<Employment?>? onEmploymentSaved;
  final dynamic Function()? onEmploymentLoaded;
  final BiPositiveButtonPurpose editEmploymentSubmitButtonPurpose;
  final void Function(List<Employment>) onSummaryLoaded;

  const EmploymentSummary({
    super.key,
    this.onEmploymentDeleted,
    this.onEmploymentSaved,
    this.onEmploymentLoaded,
    required this.editEmploymentSubmitButtonPurpose,
    required this.onSummaryLoaded,
  });

  @override
  Widget build(BuildContext context) {
    final employmentRepository = getIt.get<EmploymentRepository>();
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();
    final isByod = context.isByod;

    return BiEditableListView<Employment>(
      onItemsLoaded: onEmploymentLoaded,
      load: () async {
        final response = await employmentRepository.fetchEmployment();
        onSummaryLoaded(response.map((e) => e.data).toList());
        return response;
      },
      addButtonText: context.l10n.addEmployment,
      deleteConfirmationText: context.l10n.deleteEmploymentConfirm,
      canDelete: (item) => false,
      canAdd: () => true,
      canEdit: (item) => (item.address?.country != null &&
              [
                item.address?.address1,
                item.address?.addressTypeId,
                item.address?.city,
                item.address?.countryCode,
                item.address?.postalCode,
                item.address?.state,
                item.address?.timeZoneId,
              ].any((i) => i == null))
          ? false
          : true,
      titleBuilder: (item) => item.name,
      subtitleBuilder: (item) => (item.phone == null || item.phone!.isEmpty)
          ? null
          : BiPhoneView(
              phoneNumber: item.phone!.toFormattedPhoneNumberWithSpaces(),
              onTap: () {
                final l10n = context.l10n;
                if (isByod) {
                  UrlLauncherUtil.launchUrlUtil(
                    'tel://${item.phone!}',
                    context.l10n,
                  ).then((error) {
                    if (context.mounted && error == l10n.badLink) {
                      context.showBannerMessage(
                        ErrorBannerMessage(text: l10n.genericError),
                      );
                    }
                  });
                } else {
                  context.startVoipCall(item.phone);
                }
              },
            ),
      onItemDeleted: onEmploymentDeleted,
      onItemSaved: onEmploymentSaved,
      editItemTitle: context.l10n.employment,
      editFormSaveButtonPurpose: editEmploymentSubmitButtonPurpose,
      onEditItemInit: (item) => employmentRepository.fetchEmploymentRules(),
      editItemFormFields: (context, initSnapshotData, item) {
        return EmploymentFormFields(
          rules: initSnapshotData as GetEmploymentRulesResponse,
          employment: item,
        );
      },
      internetConnectionStream: connectionStatus,
    );
  }
}
