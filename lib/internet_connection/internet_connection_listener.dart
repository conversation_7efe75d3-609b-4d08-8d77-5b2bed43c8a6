import 'dart:async';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_font_awesome_pro/bi_flutter_font_awesome_pro.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:widgets/widgets.dart';

class InternetConnectionListener extends StatefulWidget {
  final Widget child;

  const InternetConnectionListener({
    super.key,
    required this.child,
  });

  @override
  State<StatefulWidget> createState() => _InternetConnectionListener();
}

/// Used to render a connectivity dialog when internet is not present.
class _InternetConnectionListener extends State<InternetConnectionListener> {
  bool? isConnected;

  @override
  Widget build(BuildContext context) {
    final stream = getIt.get<Stream<List<ConnectivityResult>>>();

    // use a new navigator here so that way the internet connection dialog can be dismissed
    // independently of any other page. The root navigator is still used for everything in the
    // app, it is just this nested one that is used for the internet dialog.

    // The key here is, we need a new navigator that does NOT use the same navigator
    // key as the root one, so that way any 'pops' of this particular dialog only
    // happen at the lowest level, and not at the root page level.
    return Navigator(
      onGenerateRoute: (settings) {
        return MaterialPageRoute<dynamic>(
          builder: (context) {
            return BiStreamListener(
              stream: stream,
              listener: (status) async {
                final wasDisconnected = isConnected == false;

                setState(() {
                  // keep track of if the dialog has been shown or not.
                  isConnected = status.lastOrNull != ConnectivityResult.none;
                });

                if (isConnected == false) {
                  // show the dialog
                  await _showConnectivityDialog(context);
                } else if (wasDisconnected && isConnected == true) {
                  // dismiss the dialog
                  Navigator.of(context).pop();
                }
              },
              child: widget.child,
            );
          },
          settings: settings,
        );
      },
    );
  }

  Future<void> _showConnectivityDialog(BuildContext context) async {
    return await showDialog<void>(
      barrierDismissible: false,
      context: context,
      builder: (context) => AlertDialog(
        icon: const Center(
          child: BiFaIcon(
            icon: FontAwesomeIcons.solidCloudExclamation,
            iconColorGradient: BiColors.blueButtonGradient,
          ),
        ),
        content: Text(
          context.l10n.noInternetConnection,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
