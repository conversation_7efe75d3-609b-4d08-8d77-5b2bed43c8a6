import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_html/bi_flutter_html.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/terms_and_conditions/cubit/terms_and_conditions_cubit.dart';
import 'package:flutter/material.dart';
import 'package:embark/starter.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class _LoadingWidget extends StatelessWidget {
  const _LoadingWidget({
    required this.l10n,
  });

  final AppLocalizations l10n;

  @override
  Widget build(BuildContext context) {
    return EmbarkScaffold(
      title: l10n.termsAndConditions,
      body: Padding(
        padding: const EdgeInsets.all(0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: BiLoadingView(
                child: Container(
                  // color doesn't matter, just needed to get the shimmer
                  // to show.
                  color: Colors.red,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TermsAndConditionsPage extends StatelessWidget {
  final bool showAcceptButton;

  const TermsAndConditionsPage({
    super.key,
    required this.showAcceptButton, // Default to not showing the button
  });

  @override
  Widget build(BuildContext context) {
    final TermsAndConditionsCubit cubit = getIt.get<TermsAndConditionsCubit>();
    final htmlLoader = getIt.get<L10nAssetLoader>();
    final AppRepository appRepo =
        getIt.get<AppRepository>(instanceName: 'AppRepository');
    final l10n = AppLocalizations.of(context);

    return PopScope(
      canPop: !showAcceptButton,
      child: BlocProvider(
        create: (_) => cubit,
        child: Scaffold(
          appBar: BiAppBar(
            titleString: l10n.termsAndConditions,
            automaticallyImplyLeading: !showAcceptButton,
          ),
          persistentFooterButtons: !showAcceptButton
              ? null
              : [
                  Container(
                    width: double.infinity,
                    color: Colors.white,
                    padding: const EdgeInsets.only(bottom: 8, top: 8),
                    child: BiPositiveButton(
                      purpose: BiPositiveButtonPurpose.continueWorkflow,
                      onTap: cubit.onAcceptTapped,
                      text: l10n.accept,
                    ),
                  ),
                ],
          body: FutureBuilder<String?>(
            future: htmlLoader.loadTermsAndConditions(
              context: context,
              locale: l10n.localeName,
            ),
            builder: (context, snapshot) {
              if (snapshot.connectionState != ConnectionState.done) {
                // Adjusted for brevity. Replace _LoadingWidget with your actual loading widget.
                return _LoadingWidget(l10n: l10n);
              }

              final html = snapshot.data ?? '';
              if (html.isEmpty) {
                // Handle the case where html is empty or loading failed
                appRepo.bannerMessageSink
                    .add(ErrorBannerMessage(text: l10n.notImplemented));
              }

              return SingleChildScrollView(
                key: const Key('termsScrollView'),
                controller: ScrollController(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Html(data: html),
                    // Conditionally display the accept button
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
