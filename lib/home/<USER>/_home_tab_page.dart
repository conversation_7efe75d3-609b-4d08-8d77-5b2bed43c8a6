import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/home/<USER>/home_state.dart';
import 'package:embark/dashboard/cubit/video_conference_task/video_conference_task_cubit.dart';
import 'package:embark/dashboard/cubit/video_conference_task/video_conference_task_state.dart';
import 'package:embark/home/<USER>/_home_tab_view.dart';
import 'package:flutter/material.dart';
import 'package:embark/starter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:widgets/widgets.dart';

class HomeTabPage extends StatelessWidget {
  const HomeTabPage({
    super.key,
    required bool initialLoad,
  }) : _initialLoad = initialLoad;
  final bool _initialLoad;
  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    // ignore: deprecated_member_use
    return BiEmbarkBlocProvider<HomeCubit, HomeState>(
      create: (context) => getIt.get<HomeCubit>(),
      init: (cubit) => cubit.load(reloadClientSettings: !_initialLoad),
      internetConnectivityStream: connectionStatus,
      child: BlocListener<VideoConferenceTaskCubit, VideoConferenceTaskState>(
        listenWhen: (prev, curr) =>
            prev.status != curr.status &&
            curr.status == VideoConferenceTaskStatus.hungUp,
        listener: (context, state) async {
          await BlocProvider.of<HomeCubit>(context).reloadTasks();
        },
        child: const _TodoCountListener(
          child: HomeTabView(),
        ),
      ),
    );
  }
}

class _TodoCountListener extends StatelessWidget {
  final Widget child;

  const _TodoCountListener({required this.child});

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeCubit, HomeState>(
      listenWhen: (previous, current) =>
          previous.todoTasks != current.todoTasks,
      listener: (context, state) {
        context.read<DashboardCubit>().updateTodoCount(state.todoTasks.length);
      },
      child: child,
    );
  }
}
