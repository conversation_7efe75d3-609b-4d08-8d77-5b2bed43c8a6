import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/documents/cubit/_document_state.dart';
import 'package:embark/documents/cubit/cubit.dart';
import 'package:embark/documents/views/_add_documents.dart';
import 'package:embark/documents/views/_documents_bloc_listener.dart';
import 'package:embark/documents/views/_documents_group_list_view.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:widgets/widgets.dart';

class DocumentsPage extends StatelessWidget {
  const DocumentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DocumentsCubit>(
      create: (context) => DocumentsCubit(
        appRepository: getIt.get<AppRepository>(instanceName: 'AppRepository'),
        documentsRepository: getIt.get(),
        logger: getIt.get(),
      )
        ..getAvailableMediaTypes()
        ..loadDocuments()
        ..initPushNotificationsListener(),
      child: BiEmbarkInternetStreamListener(
        internetConnectivityStream:
            getIt.get<Stream<List<ConnectivityResult>>>(),
        listener: (BuildContext context, {current, required next}) {
          if (current == ConnectivityResult.none && next != current) {
            context.read<DocumentsCubit>().loadDocuments();
          }
        },
        child: const DocumentBlocListener(
          child: Scaffold(
            body: DocumentsGroupListView(),
            floatingActionButton: _Fab(),
          ),
        ),
      ),
    );
  }
}

class _Fab extends StatelessWidget {
  const _Fab();

  @override
  Widget build(BuildContext context) {
    final items = context.selectDocumentGroupsState((state) {
      if (state.mediaTypesState case MediaTypesStateLoaded(data: final data)) {
        return data;
      }
      return <SmartMediaTypes>[];
    });
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }
    return FloatingActionButton(
      onPressed: () =>
          goToAddDocumentScreenSelector(context, mediaTypes: items),
      child: const Icon(
        Icons.add_rounded,
        size: 40,
      ),
    );
  }
}
