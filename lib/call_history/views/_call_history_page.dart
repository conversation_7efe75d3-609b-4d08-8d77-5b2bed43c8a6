import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/call_history/cubit/call_history_cubit.dart';
import 'package:embark/call_history/cubit/call_history_state.dart';
import 'package:embark/call_history/views/_call_history_view.dart';
import 'package:embark/starter.dart';
import 'package:flutter/cupertino.dart';
import 'package:widgets/widgets.dart';

class CallHistoryPage extends StatelessWidget {
  const CallHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    // ignore: deprecated_member_use
    return BiEmbarkBlocProvider<CallHistoryCubit, CallHistoryState>(
      create: (_) => getIt.get<CallHistoryCubit>()..load(),
      internetConnectivityStream: connectionStatus,
      child: const CallHistoryView(),
    );
  }
}
