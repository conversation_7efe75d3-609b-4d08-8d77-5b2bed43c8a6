import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart'; // Assuming SetupVoIPCallResponse is defined here
import 'package:equatable/equatable.dart';

enum DashboardStatus {
  initial,
  loaded,
}

class DashboardState extends Equatable {
  final DashboardStatus status;
  final int todoCount;
  final Map<PluginType, PluginResponse> plugins;
  final GetClientSettingsResponse? clientSettings;
  final PluginType? tabToBeShown;
  final DashboardCheckIn dashboardCheckIn;

  const DashboardState({
    this.status = DashboardStatus.initial,
    this.todoCount = 0,
    this.plugins = const {},
    this.clientSettings,
    this.tabToBeShown,
    this.dashboardCheckIn = const DashboardCheckIn.initial(),
  });

  @override
  List<Object?> get props => [
        todoCount,
        plugins,
        status,
        clientSettings,
        dashboardCheckIn,
        tabToBeShown,
      ];

  DashboardState copyWith({
    int? todoCount,
    Map<PluginType, PluginResponse>? plugins,
    GetClientSettingsResponse? clientSettings,
    DashboardStatus? status,
    PluginType? tabToBeShown,
    DashboardCheckIn? dashboardCheckIn,
  }) =>
      DashboardState(
        todoCount: todoCount ?? this.todoCount,
        plugins: plugins ?? this.plugins,
        status: status ?? this.status,
        dashboardCheckIn: dashboardCheckIn ?? this.dashboardCheckIn,
        clientSettings: clientSettings ?? this.clientSettings,
        tabToBeShown: tabToBeShown,
      );
}

class DashboardCheckIn extends Equatable {
  const DashboardCheckIn({
    required this.viewing,
    this.nextAllowedCheckin,
  });
  final bool viewing;
  final DateTime? nextAllowedCheckin;
  const DashboardCheckIn.initial()
      : viewing = false,
        nextAllowedCheckin = null;

  DashboardCheckIn copyWith({bool? viewing, DateTime? nextAllowedCheckin}) =>
      DashboardCheckIn(
        viewing: viewing ?? this.viewing,
        nextAllowedCheckin: nextAllowedCheckin ?? this.nextAllowedCheckin,
      );

  @override
  List<Object?> get props => [viewing, nextAllowedCheckin];
}
