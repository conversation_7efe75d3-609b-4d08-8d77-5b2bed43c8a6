import 'dart:async';

import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/client_settings/client_settings.dart';
import 'package:embark/dashboard/cubit/_dashboard_state.dart';
import 'package:embark/repositories/_tracking/_tracking_repository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/repositories/smart_band/_smart_band_background_repository.dart';
import 'package:embark/repositories/smart_band/_smart_band_ui_repository.dart';
import 'package:embark/services/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:helpers/helpers.dart';
import 'package:mutex/mutex.dart';

extension DashboardContextHelper on BuildContext {
  R selectDashboardState<R>(R Function(DashboardState state) find) =>
      select<DashboardCubit, R>((value) => find(value.state));
}

// ignore: deprecated_member_use, deprecated_member_use_from_same_package
class DashboardCubit extends BiEmbarkCubit<DashboardState> {
  final AppRepository _appRepository;
  final EmbarkAuthenticationRepository _embarkAuthenticationRepository;
  final ClientSettingsRepository _clientSettingsRepo;
  final AppSettingsRepository _languageRepository;
  final SmartBandPreferences _smartBandPreferences;
  final BiLogger _logger;
  StreamSubscription<GetClientSettingsResponse>? _clientSettingsSubscription;
  String? twilioToken;
  String? twilioIdentity;
  final CheckinTimestampRepository _timestampRepository;
  final DateTime Function() getNow;
  final SmartBandUiRepository _smartBandRepository;
  final FlutterSecureStorage _flutterSecureStorage;
  final TrackingRepository _trackingRepository;
  final FlutterBackgroundService _serviceInstance;

  DashboardCubit(
    this._appRepository,
    this._embarkAuthenticationRepository,
    this._clientSettingsRepo,
    this._languageRepository,
    this._smartBandPreferences,
    this._logger,
    this._timestampRepository,
    this._smartBandRepository,
    this._flutterSecureStorage,
    this._trackingRepository,
    this._serviceInstance, {
    this.getNow = _timeFactory,
  }) : super(const DashboardState()) {
    subscriptions.add(
      _appRepository.appLifecycleStateStream.listen(
        (lifeCycleState) async {
          if (lifeCycleState == AppLifecycleState.resumed) {
            resubscribeToClientSettings();
          } else if (lifeCycleState == AppLifecycleState.paused) {
            _unsubscribeFromClientSettings();
          }
        },
      ),
    );
  }

  StreamSubscription<GetClientSettingsResponse> _listenOnClientSettings() {
    return _clientSettingsRepo.clientSettings.listen(
      (newSettings) {
        _onClientSettingsChanged(newSettings);
      },
      cancelOnError: false,
      onError: (Object e) => _onServerError(e),
    );
  }

  static DateTime _timeFactory() => DateTime.now();

  void resubscribeToClientSettings() {
    _unsubscribeFromClientSettings();

    _clientSettingsSubscription = _listenOnClientSettings();

    subscriptions.add(_clientSettingsSubscription!);
  }

  void _unsubscribeFromClientSettings() {
    if (_clientSettingsSubscription != null) {
      _clientSettingsSubscription?.cancel();
      subscriptions.remove(_clientSettingsSubscription);
    }
  }

  Future<void> init() async {
    await subscribeToClientSettings();

    /*
    This is a quick hack so we can search for errors by username.
     */
    await _logger.setUser(
      await _flutterSecureStorage.read(key: 'LOGIN_WIDGET_USERNAME') ??
          'unknown_user',
    );
  }

  void updateTodoCount(int count) {
    emit(state.copyWith(todoCount: count));
  }

  Future<void> subscribeToClientSettings() async {
    resubscribeToClientSettings();
  }

  void _onServerError(Object error) {
    // show the client a server error message.
    _logger.error(error: error);
  }

  Future<void> _onClientSettingsChanged(
    GetClientSettingsResponse clientSettings,
  ) async {
    final plugins = await _checkTrackingPlugin(clientSettings.pluginMap);

    await _languageRepository.storeAndSetAppLocaleIfChanged(
      clientSettings.preferredLanguage.languageCodeId,
    );

    emit(
      state.copyWith(
        plugins: plugins,
        status: DashboardStatus.loaded,
        clientSettings: clientSettings,
      ),
    );

    await _evaluatePlugins(clientSettings, plugins);
  }

  Future<void> _evaluatePlugins(
    GetClientSettingsResponse clientSettings,
    Map<PluginType, PluginResponse> plugins,
  ) async {
    await tryStartSmartBandService();

    // evaluate tracking plugin
    await _trackingRepository.startOrStop(
      plugins[PluginType.tracking],
    );
  }

  void reloadClientSettings() {
    _clientSettingsRepo.reloadClientSettings();
  }

  Future<void> checkIn() async {
    assert(
      state.clientSettings?.enrolledFacial ?? false,
      'enrolled facial should be enabled to check in',
    );
    emit(
      state.copyWith(
        dashboardCheckIn: const DashboardCheckIn.initial(),
      ),
    );
    final storedTimestamps = _timestampRepository.getAdHocCheckIns();

    final nextAvailableCheckinTime =
        await _nextAllowedCheckin(storedTimestamps);
    final DashboardCheckIn checkIn;
    final allowCheckin = nextAvailableCheckinTime == null;

    if (allowCheckin || _isCheckInDue()) {
      checkIn = state.dashboardCheckIn.copyWith(viewing: true);
    } else {
      checkIn = state.dashboardCheckIn.copyWith(
        nextAllowedCheckin: nextAvailableCheckinTime,
      );
    }
    emit(state.copyWith(dashboardCheckIn: checkIn));
  }

  /// The user should only be able to check in 2 times per hour.
  /// For example, if the client checks in at 11:03 and 11:57, they cannot check-in again until 12:04.
  /// If greater than 0, the user will be able to check in, and the attempt will be stored in
  /// SharedPreferences as JSON.

  Future<DateTime?> _nextAllowedCheckin(List<DateTime> storedTimestamps) async {
    // Filter timestamps within the last hour
    final lastHourTimestamps = _filterToLastHour(storedTimestamps);

    // If the first checkin was more than 1 hour ago then we reset the timer
    if (storedTimestamps.length != lastHourTimestamps.length) {
      await _timestampRepository.clear();
      return null;
    }

    // Check if the user can perform another check-in
    if (lastHourTimestamps.length < 2) {
      return null;
    }

    // User has already performed 2 check-ins in the last hour
    return lastHourTimestamps.first.add(const Duration(hours: 1));
  }

  List<DateTime> _filterToLastHour(List<DateTime> storedTimestamps) {
    final newAttempt = getNow();

    final lastHourTimestamps = storedTimestamps.where((time) {
      return newAttempt.difference(time).inHours < 1;
    }).toList();
    return lastHourTimestamps;
  }

  bool _isCheckInDue() =>
      state.clientSettings?.pluginMap[PluginType.checkInFacial] != null &&
      (state.clientSettings?.pluginMap[PluginType.checkInFacial]
              ?.isActionable ??
          false);

  Future<void> logout() async {
    await _embarkAuthenticationRepository.logOut();
  }

  void navigateToTab(PluginType tabType) {
    emit(state.copyWith(tabToBeShown: tabType));
  }

  void resetTabToBeShown() {
    emit(state.copyWith(tabToBeShown: null));
  }

  /// SmartBand Service
  /// Mutex is used to prevent multiple calls to startSmartBandService() and
  /// ensure permission request is not slammed
  final _sbMutex = Mutex();
  Future<void> tryStartSmartBandService() async {
    await _sbMutex.acquire();
    try {
      // Starts up the bluetooth services. This connects to the bluetooth backend
      // and launches it if necessary. This does NOT connect to a device unless
      // the device is already paired and not currently connected. This call also
      // has built in checks to ensure that it doesn't run multiple times in the event
      // client settings gets updated multiple times (which is the case most of the time)
      // so we don't need any additional checks here for that.
      //
      final sn = state.clientSettings?.smartBandPlugin?.serialNumber;

      // Lets update backend's knowledge of serial number
      await _updateSbSnSharedPreferences(sn);

      if (sn != null) {
        unawaited(
          _smartBandRepository.ensureConnected(
            serialNumber: sn,
            scanTimeout: const Duration(seconds: 23),
          ),
        );
      } else {
        await _smartBandRepository.disconnect();
      }
    } finally {
      _sbMutex.release();
    }
  }

  Future<void> _updateSbSnSharedPreferences(String? sn) async {
    await _smartBandPreferences.setSbSerial(sn);
    _serviceInstance
        .invoke(SharedPreferencesKeyUpdateIsolateMethod.serial.value, {
      'serial': sn,
    });
  }

  Future<Map<PluginType, PluginResponse>> _checkTrackingPlugin(
    Map<PluginType, PluginResponse> pluginMap,
  ) async {
    final trackingPlugin = pluginMap[PluginType.tracking];
    if (trackingPlugin == null) return pluginMap;

    final trackingStatus =
        await _trackingRepository.deviceConfigurationStatus(trackingPlugin);

    // If tracking is ready, no further modifications are needed
    if (trackingStatus == TrackingConfigurationStatus.ready) return pluginMap;

    pluginMap[PluginType.tracking] = trackingPlugin.copyWith(icon: '_action');

    return pluginMap;
  }
}
