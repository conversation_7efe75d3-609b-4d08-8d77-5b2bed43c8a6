import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/auth_steps/showing_dashboard_step.dart';
import 'package:embark/authentication/embark_auth_step_provider.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/dashboard/views/_dashboard_listener.dart';
import 'package:embark/dashboard/views/video_conference_task/_video_conference_task_provider.dart';
import 'package:flutter/material.dart';
import 'package:embark/starter.dart';
import 'package:widgets/widgets.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:embark/dashboard/views/_dashboard_view.dart';

/// The root page that renders the [EmbarkScaffold] and the tabs.
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    // ignore: deprecated_member_use
    return AuthStepProvider(
      authStep: getIt.get<ShowingDashboardStep>(),
      child: BiEmbarkBlocProvider<DashboardCubit, DashboardState>(
        create: (context) => getIt.get<DashboardCubit>(),
        init: (cubit) async {
          await cubit.init();
        },
        internetConnectivityStream: connectionStatus,
        child: const VideoConferenceTaskProvider(
          child: DashboardListener(child: DashboardView()),
        ),
      ),
    );
  }
}
