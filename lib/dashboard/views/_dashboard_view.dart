import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/calendar/calendar.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/dashboard/views/views.dart';
import 'package:embark/device_setup/device_setup.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/push_notifications/models/models.dart';
import 'package:embark/routing/_embark_routes.dart';
import 'package:embark/routing/navigator_extensions.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:widgets/widgets.dart';

class DashboardView extends StatefulWidget {
  const DashboardView({super.key});

  @override
  State<DashboardView> createState() => _DashboardViewState();
}

class _DashboardViewState extends State<DashboardView> {
  GlobalKey<DashboardTabsState> dashBoardTabsKey =
      GlobalKey(debugLabel: 'dashboard_tabs_key');
  late AppLifecycleListener appLifecycleListener;
  @override
  void initState() {
    super.initState();
    appLifecycleListener = AppLifecycleListener(
      onResume: () async {
        final cubit = BlocProvider.of<DashboardCubit>(context);
        await cubit.tryStartSmartBandService();
      },
    );
  }

  @override
  void dispose() {
    appLifecycleListener.dispose();
    super.dispose();
  }

  VisibilityInfo? _visibilityInfo;
  @override
  Widget build(BuildContext context) {
    return BlocListener<PushNotificationListenerCubit,
        PushNotificationListenerState>(
      listener: (context, state) {
        if (state.newPushPayLoad != null) {
          final PushPayload pushPayload = state.newPushPayLoad!;
          if (pushPayload.actionType == PushNotificationType.message) {
            showSnackBarWithAction(pushPayload, PluginType.conversation);
          } else if (pushPayload.actionType == PushNotificationType.media) {
            showSnackBarWithAction(pushPayload, PluginType.myDocuments);
          } else if (pushPayload.actionType == PushNotificationType.reminder) {
            showSnackBarWithAction(pushPayload, PluginType.calendar);
          } else if (pushPayload.actionType == PushNotificationType.calendar) {
            showSnackBarWithAction(pushPayload, PluginType.calendar);
          }
        } else if (state.savedPushPayLoad != null) {
          final PushPayload pushPayload = state.savedPushPayLoad!;
          if (pushPayload.actionType == PushNotificationType.message) {
            navigateToTab(PluginType.conversation);
          } else if (pushPayload.actionType == PushNotificationType.media) {
            navigateToTab(PluginType.myDocuments);
          } else if (pushPayload.actionType == PushNotificationType.reminder) {
            navigateToTab(PluginType.calendar);
          } else if (pushPayload.actionType == PushNotificationType.calendar) {
            navigateToTab(PluginType.calendar);
          }
        }
      },
      child: BlocConsumer<DashboardCubit, DashboardState>(
        listener: (context, state) {
          final cubit = BlocProvider.of<DashboardCubit>(context);
          if (state.tabToBeShown != null) {
            cubit.resetTabToBeShown();
            navigateToTab(
              state.tabToBeShown!,
              defaultCalendarType: CalendarType.list,
            );
          }
        },
        builder: (context, state) {
          // these are common items to all tabs
          const drawer = _Drawer();
          final dashboard = DashboardTabs(
            plugins: state.plugins,
            drawer: drawer,
            key: dashBoardTabsKey,
          );

          return _buildDashboard(context, state, dashboard);
        },
      ),
    );
  }

  Widget _buildDashboard(
    BuildContext context,
    DashboardState state,
    Widget dashboard,
  ) {
    return VisibilityDetector(
      key: const Key('dashboardVisibilityDetector'),
      onVisibilityChanged: (visibilityInfo) async {
        final previousInfo = _visibilityInfo;
        _visibilityInfo = visibilityInfo;
        if (previousInfo == null) {
          return;
        }
        // if the app is visible, reload client settings
        if (context.mounted && visibilityInfo.visibleFraction > 0.0) {
          final cubit = BlocProvider.of<DashboardCubit>(context);
          cubit.reloadClientSettings();
          // TODO: Once the backend is ready, add a check for an SB service plan here
          await cubit.tryStartSmartBandService();
        }
      },
      child: switch (state.status) {
        DashboardStatus.initial => const Center(),
        _ => dashboard
      },
    );
  }

  void showSnackBarWithAction(PushPayload pushPayload, PluginType targetTab) {
    final PushNotificationListenerCubit pnCubit = BlocProvider.of(context);
    context.showBannerMessage(
      BannerMessage(
        text: pushPayload.snackBarMessage,
        bannerPosition: BannerPosition.bottom,
        snackBarAction: SnackBarAction(
          label: context.l10n.view,
          textColor: Colors.white,
          onPressed: () {
            pnCubit.savePushPayloadIfTokenExpired(pushPayload);
            context.clearBannerMessages(atPosition: BannerPosition.bottom);
            navigateToTab(targetTab);
          },
        ),
        dismissAfter: const Duration(seconds: 4),
      ),
    );
  }

  void navigateToTab(PluginType tab, {CalendarType? defaultCalendarType}) {
    if (dashBoardTabsKey.currentState?.mounted == true) {
      switch (tab) {
        case PluginType.calendar:
          dashBoardTabsKey.currentState!
              .navigateToCalendar(defaultCalendarType: defaultCalendarType);
        case PluginType.conversation:
          dashBoardTabsKey.currentState!.navigateToMessages();
        case PluginType.myDocuments:
          dashBoardTabsKey.currentState!.navigateToDocuments();
        default:
      }
      if (Navigator.canPop(context)) {
        Navigator.of(context).popUntil(
          (route) =>
              route.settings.name == EmbarkRoutes.dashboardPage.settings.name,
        );
      }
    }
  }
}

class _Drawer extends StatelessWidget {
  const _Drawer();

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<DashboardCubit>(context, listen: true);
    final plugins = cubit.state.plugins;
    final enrolledFacial = cubit.state.clientSettings?.enrolledFacial ?? false;
    return BiEmbarkDrawer(
      settingsTapped: () async {
        await context.showSettings(context.read<SmartBandSettingsCubit>());
      },
      onCheckInTapped: !enrolledFacial
          ? null
          : () {
              return cubit.checkIn();
            },
      selfReportTapped: !plugins.containsKey(PluginType.selfReport)
          ? null
          : () async {
              await context.showSelfReport();
            },
      notificationHistoryTapped: () {
        context.showNotificationHistory();
      },
      profileTapped: !plugins.containsKey(PluginType.myInfo)
          ? null
          : () async {
              await context.showProfile();
            },
      callHistoryTapped:
          !context.isByod && plugins.containsKey(PluginType.phone)
              ? context.showCallHistory
              : null,
      // US20934 - comment out for MVP
      // submitFeedbackTapped: () async {
      //   embarkRouter.pop();
      //   cubit.showNotImplemented(l10n);
      // },
      logoutTapped: context.authType == AuthorizationType.autoFingerprint
          ? null
          : () async {
              context.popPage();
              await cubit.logout();
            },
    );
  }
}
