import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/calendar/calendar.dart';
import 'package:embark/dashboard/views/tabs/_calendar_view_action.dart';
import 'package:embark/documents/views/_documents_page.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:embark/embark_scaffold/views/dashboard_scaffold.dart';
import 'package:embark/home/<USER>/_home_tab_page.dart';
import 'package:embark/messages/views/messages_page.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/resources/views/resources_page.dart';
import 'package:flutter/material.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';

import 'package:embark/dashboard/views/tabs/tabs.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:widgets/widgets.dart';

class DashboardTabs extends StatefulWidget {
  final Map<PluginType, PluginResponse> plugins;
  final Widget drawer;
  final List<Widget>? actions;

  const DashboardTabs({
    super.key,
    required this.plugins,
    required this.drawer,
    this.actions,
  });

  @override
  State<StatefulWidget> createState() => DashboardTabsState();
}

class DashboardTabsState extends State<DashboardTabs> {
  static const int _defaultTabId = -1;

  late TabConfig _currentTab;
  late PushNotificationListenerCubit pushNotificationListenerCubit;
  GlobalKey<BiTabViewState> tabViewKey = GlobalKey();
  int startingDashBoardId = _defaultTabId;
  CalendarType? defaultCalendarType;
  ValueKey<int> calendarPageKey = const ValueKey(0);
  bool _initialLoad = true;

  @override
  void initState() {
    super.initState();
    // determine the starting tab configuration
    pushNotificationListenerCubit =
        BlocProvider.of<PushNotificationListenerCubit>(context);

    startingDashBoardId = pushNotificationListenerCubit.startingDashBoardTabId;
    _currentTab = _allPossibleTabs.firstWhere(
      (t) => t.id == _defaultTabId,
    );
  }

  /// The list of tabs to render and their respective titles.
  ///
  /// The order of this list is the order in which the tabs are rendered from left
  /// to right.
  List<TabConfig> get _allPossibleTabs => [
        TabConfig(
          id: PluginType.calendar.value,
          tab: const CalendarTab(),
          body: CalendarPage(
            defaultCalendarType: defaultCalendarType,
            key: calendarPageKey,
          ),
          actions: const [CalendarViewAction()],
        ),
        TabConfig(
          id: PluginType.resources.value,
          tab: const ResourcesTab(),
          body: const ResourcesPage(),
        ),
        TabConfig(
          // null here lets us treat this home tab as the default selection.
          id: _defaultTabId,
          tab: const HomeTab(),
          body: HomeTabPage(initialLoad: _initialLoad),
        ),
        TabConfig(
          id: PluginType.myDocuments.value,
          tab: const DocumentsTab(),
          body: const DocumentsPage(),
        ),
        TabConfig(
          id: PluginType.conversation.value,
          tab: const MessagesTab(),
          body: const MessagesPage(),
          actions: const [TranslationSwitchAction()],
        ),
      ];

  @override
  Widget build(BuildContext context) {
    final tabView = BiTabView(
      key: tabViewKey,
      visibleTabIds: widget.plugins.keys.map((k) => k.value).toSet(),
      tabs: _allPossibleTabs,
      defaultTabId: _defaultTabId,
      onTabChanged: (newTab) {
        setState(() {
          if (newTab.id == PluginType.calendar.value) {
            /// user clicked on calendar tab no need to save default value of calendar type
            defaultCalendarType = null;
          }
          _initialLoad = false;
          _currentTab = newTab;
          startingDashBoardId = _defaultTabId;
          pushNotificationListenerCubit.startingDashBoardTabId = -1;
        });
      },
      startingTabId: startingDashBoardId,
    );

    final Widget scaffold = _currentTab.id == _defaultTabId
        ? DashboardScaffold(
            body: tabView,
            drawer: widget.drawer,
            actions: const [],
          )
        : EmbarkScaffold(
            title: _determineTitle(context),
            body: tabView,
            drawer: widget.drawer,
            actions: _currentTab.actions,
          );

    return scaffold;
  }

  String _determineTitle(BuildContext context) {
    // convert the plugin id to the title for the scaffold.
    // US 22608: Reminder that each US for ToDo List will be responsible for
    // updating ToDo count appropriately
    if (startingDashBoardId != _defaultTabId) {
      _currentTab = _allPossibleTabs.firstWhere(
        (t) => t.id == startingDashBoardId,
      );
    }
    var newTitle = '';
    final pluginType = PluginType.values.firstWhere(
      (e) => e.value == _currentTab.id,
      orElse: () => PluginType.unknown,
    );

    switch (pluginType) {
      case PluginType.calendar:
        newTitle = context.l10n.calendar;
        break;

      case PluginType.conversation:
        newTitle = context.l10n.messages;
        break;

      case PluginType.resources:
        newTitle = context.l10n.resources;
        break;

      case PluginType.myDocuments:
        newTitle = context.l10n.documents;
        break;

      default:
        break;
    }
    return newTitle;
  }

  void navigateToCalendar({CalendarType? defaultCalendarType}) {
    setState(() {
      this.defaultCalendarType = defaultCalendarType;
      if (tabViewKey.currentState?.mounted == true) {
        updateSelectedTab(0);
      }
    });
  }

  void navigateToMessages() {
    if (tabViewKey.currentState?.mounted == true) {
      updateSelectedTab(4);
    }
  }

  void navigateToDocuments() {
    if (tabViewKey.currentState?.mounted == true) {
      updateSelectedTab(3);
    }
  }

  void updateSelectedTab(int index) {
    setState(() {
      _currentTab = _allPossibleTabs[index];
      tabViewKey.currentState!.updateSelectedTab(_allPossibleTabs[index]);
    });
  }
}
