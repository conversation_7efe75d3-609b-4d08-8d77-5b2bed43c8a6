import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/resources/cubit/resources_cubit.dart';
import 'package:embark/resources/views/resources_tab_view.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:widgets/widgets.dart';

class ResourcesPage extends StatelessWidget {
  const ResourcesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    // ignore: deprecated_member_use
    return BiEmbarkBlocProvider<ResourcesCubit, ResourcesState>(
      create: (context) => ResourcesCubit(resourcesRepository: getIt.get()),
      init: (cubit) => cubit.load(),
      internetConnectivityStream: connectionStatus,
      child: const ResourcesTabView(),
    );
  }
}
