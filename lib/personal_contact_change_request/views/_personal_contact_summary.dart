import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/device_setup/device_setup.dart';
import 'package:embark/extensions/extensions.dart';
import 'package:embark/personal_contact_change_request/views/views.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/resources/utils/url_launcher_util.dart';
import 'package:embark/routing/navigator_extensions.dart';
import 'package:embark/starter.dart';
import 'package:flutter/cupertino.dart';
import 'package:widgets/widgets.dart';

class PersonalContactSummary extends StatelessWidget {
  final ItemDeletedDelegate<PersonalContact>? onPersonalContactDeleted;
  final ItemSavedDelegate<PersonalContact?>? onPersonalContactSaved;
  final dynamic Function()? onPersonalContactsLoaded;
  final BiPositiveButtonPurpose editPersonalContactSubmitButtonPurpose;
  final void Function(List<PersonalContact>) onSummaryLoaded;

  const PersonalContactSummary({
    super.key,
    this.onPersonalContactDeleted,
    this.onPersonalContactSaved,
    this.onPersonalContactsLoaded,
    required this.editPersonalContactSubmitButtonPurpose,
    required this.onSummaryLoaded,
  });

  @override
  Widget build(BuildContext context) {
    final personalContactRepository = getIt.get<PersonalContactsRepository>();
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();
    final isByod = context.isByod;

    return BiEditableListView<PersonalContact>(
      onItemsLoaded: onPersonalContactsLoaded,
      load: () async {
        final response =
            await personalContactRepository.fetchPersonalContacts();
        onSummaryLoaded(response.map((p) => p.data).toList());
        return response;
      },
      addButtonText: context.l10n.addPersonalContact,
      deleteConfirmationText: context.l10n.deletePeronalContactConfirm,
      canDelete: (item) => true,
      canAdd: () => true,
      canEdit: (item) => true,
      titleBuilder: (item) => item.relationship ?? context.l10n.unknown,
      subtitleBuilder: (item) {
        final sb = StringBuffer();
        sb.write('${item.firstName}, ${item.lastName}');
        final shouldWrap = sb.toString().length > 30;

        return Column(
          children: [
            shouldWrap
                ? Flexible(
                    child: Text(
                      sb.toString(),
                    ),
                  )
                : Text(
                    sb.toString(),
                  ),
            BiPhoneView(
              phoneNumber: item.contactInfo?.resolvePrimaryNumber() ?? '',
              onTap: () {
                final l10n = context.l10n;
                if (isByod) {
                  UrlLauncherUtil.launchUrlUtil(
                    'tel://${item.contactInfo?.resolvePrimaryNumber() ?? ''}',
                    context.l10n,
                  ).then((error) {
                    if (context.mounted && error == l10n.badLink) {
                      context.showBannerMessage(
                        ErrorBannerMessage(text: l10n.genericError),
                      );
                    }
                  });
                } else {
                  context.startVoipCall(
                    item.contactInfo?.resolvePrimaryNumber() ?? '',
                  );
                }
              },
            ),
          ],
        );
      },
      onItemDeleted: onPersonalContactDeleted,
      onItemSaved: onPersonalContactSaved,
      editItemTitle: context.l10n.personalContact,
      editFormSaveButtonPurpose: editPersonalContactSubmitButtonPurpose,
      onEditItemInit: (item) =>
          personalContactRepository.getPersonalContactRules(),
      editItemFormFields: (context, initSnapshotData, item) {
        return PersonalContactFormFields(
          rules: initSnapshotData as GetPersonalContactRulesResponse,
          personalContact: item,
        );
      },
      internetConnectionStream: connectionStatus,
    );
  }
}
