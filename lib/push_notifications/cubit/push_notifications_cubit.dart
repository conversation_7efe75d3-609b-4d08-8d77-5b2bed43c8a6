import 'dart:async';

import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/push_notifications_services/_push_notifications_token_service.dart';
import 'package:embark/starter.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:helpers/helpers.dart';

part 'push_notifications_state.dart';

// ignore: deprecated_member_use, deprecated_member_use_from_same_package
class PushNotificationsCubit extends BiEmbarkCubit<PushNotificationsState> {
  final PushNotificationsRepository _pushNotificationsRepository;
  final PushNotificationsTokenSerivce _pnNotificationTokenService;

  PushNotificationsCubit(
    this._pushNotificationsRepository,
    this._pnNotificationTokenService,
    this._logger,
  ) : super(PushNotificationsInitialState());

  final BiLogger _logger;

  Future<void> init() async {
    await _pushNotificationsRepository.initializeForegroundNotifications();
  }

  Future<String?> requestFirebasePN() async {
    return await _pnNotificationTokenService.getFirebaseMessagingToken();
  }

  Future<bool> registerPN(String? tokenHandle) async {
    final platform = defaultTargetPlatform == TargetPlatform.android
        ? BiPushPlatforms.fcm.value
        : BiPushPlatforms.apns.value;

    final request = NotificationRegistrationRequest(
      platform: platform,
      handle: tokenHandle as String,
      appType: PushNotificationAppId.smartlink,
    );
    try {
      final response =
          await _pushNotificationsRepository.registerForNotifications(request);
      if (response.httpStatusCode != 200) {
        await _logger.warn(
          message:
              'In PushNotificationsCubit: _registerPN: Failed to register for push notifications. User may not receive push notifications if previous token registration is expired. httpStatusCode: ${response.httpStatusCode}, ${response.apiMessage}',
        );
      }
      return response.httpStatusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<void>? _singleRequestAllowedAtATime;
  @visibleForTesting
  Future<void> requestAndRegisterPushNotifications() {
    return _singleRequestAllowedAtATime ??=
        _requestAndRegisterPushNotifications()
            .whenComplete(() => _singleRequestAllowedAtATime = null);
  }

  Future<void> _requestAndRegisterPushNotifications() async {
    final connectivityResult =
        await getIt.get<InternetConnectivityRepository>().reachability;
    if (connectivityResult.lastOrNull != ConnectivityResult.none) {
      final tokenHandle = await requestFirebasePN();

      // register/update the token
      if (tokenHandle != null && tokenHandle.isNotEmpty) {
        await _logger.trace(
          message: 'Registering push notification token',
          debugProps: {
            'token': tokenHandle,
          },
        );
        await registerPN(tokenHandle);
      } else {
        await _logger.warn(
          message:
              'Failed to get a valid push notification token handle.  User may not receive push notifications if previous token registration is expired.',
        );
      }
    } else {
      await _logger.info(
        message:
            'Failed to get a valid push notification token handle because the device is offline.  User will not receive push notifications offline.',
      );
    }
  }

  Future<void> initializeForegroundNotifications() async {
    await requestAndRegisterPushNotifications();
    await _requestPushNotificationsPermissions();
  }

  Future<void>? _requestNotifications;

  Future<void> _requestPushNotificationsPermissions() async {
    _requestNotifications ??= _pushNotificationsRepository
        .initializeForegroundNotifications()
        .whenComplete(() => _requestNotifications = null);
    return _requestNotifications;
  }
}
