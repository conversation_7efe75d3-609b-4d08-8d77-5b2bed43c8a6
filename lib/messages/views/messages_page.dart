import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/messages/cubit/messages_cubit.dart';
import 'package:embark/messages/views/messages_view.dart';
import 'package:flutter/material.dart';
import 'package:embark/starter.dart';
import 'package:widgets/widgets.dart';

class MessagesPage extends StatelessWidget {
  const MessagesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    // ignore: deprecated_member_use
    return BiEmbarkBlocProvider<MessagesCubit, MessagesState>(
      create: (_) => getIt.get<MessagesCubit>(),
      init: (cubit) async => await cubit.init(l10n: context.l10n),
      internetConnectivityStream: connectionStatus,
      child: const MessagesView(),
    );
  }
}
