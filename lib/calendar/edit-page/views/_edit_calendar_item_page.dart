import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/calendar/edit-page/cubit/edit_calendar_item_cubit.dart';
import 'package:embark/calendar/edit-page/cubit/edit_calendar_item_state.dart';
import 'package:embark/calendar/edit-page/views/_edit_calendar_item_page_content.dart';
import 'package:embark/client_settings/views/views.dart';
import 'package:flutter/material.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:embark/starter.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:widgets/widgets.dart';

class EditCalendarItemPage extends StatelessWidget {
  final CalendarItemInfo? event;
  final DateTime? calendarDateTimeTapped;
  final bool? seriesChangeRequest;

  bool get _isChangeRequest => event?.id != null;

  const EditCalendarItemPage({
    super.key,
    required this.event,
    required this.calendarDateTimeTapped,
    this.seriesChangeRequest,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return PluginRequirement(
      pluginType: PluginType.calendar,
      child: SafeArea(
        child: EmbarkScaffold(
          // ignore: deprecated_member_use
          body: BiEmbarkBlocProvider<EditCalendarItemCubit,
              EditCalendarItemState>(
            create: (_) => getIt.get<EditCalendarItemCubit>(),
            init: (cubit) => cubit.fetchCalendarItemRules(
              event?.id,
              calendarDateTimeTapped,
              event,
            ),
            internetConnectivityStream: connectionStatus,
            child: EditCalendarItemPageContent(
              event: event,
              seriesChangeRequest: seriesChangeRequest,
              l10n: l10n,
            ),
          ),
          title: _isChangeRequest
              ? l10n.appointmentChange
              : l10n.requestAppointment,
        ),
      ),
    );
  }
}
