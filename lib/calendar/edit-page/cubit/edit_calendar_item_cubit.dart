import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:embark/extensions/_date_time_extensions.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:flutter/widgets.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';

import 'package:embark/calendar/edit-page/cubit/edit_calendar_item_state.dart';
import 'package:helpers/helpers.dart';

// ignore: deprecated_member_use, deprecated_member_use_from_same_package
class EditCalendarItemCubit extends BiEmbarkCubit<EditCalendarItemState> {
  final CalendarRepository _calendarRepository;
  final Sink<BannerMessage> _bannerMessages;
  final BiLogger _logger;

  EditCalendarItemCubit(
    this._calendarRepository,
    this._bannerMessages,
    this._logger,
  ) : super(
          const EditCalendarItemInitialState(),
        );

  Future<void> fetchCalendarItemRules(
    int? id,
    DateTime? calendarDateTimeTapped,
    CalendarItemInfo? calendarItemInfo,
  ) async {
    await Future(() async {
      final item =
          await _calendarRepository.getCalendarRules(calendarItemId: id);

      final Map<FieldKey, CalendarRuleInfo> fields = {};
      late FieldKey fieldKey;

      // load up the cache
      item.traverse(
        logger: _logger,
        visitor: (fieldId, parentNode, node) {
          try {
            fieldKey = _extractFieldKey(parentNode, fieldId);
          } catch (e) {
            _logger.info(
              message:
                  '_extractFieldKey: parentNode = $parentNode, fieldId = $fieldId',
            );
          }

          try {
            fields[fieldKey] = tryApplyDefaultValue(
              field: CalendarRuleInfo.fromMap(node),
            );
          } catch (e) {
            _logger.info(
              message:
                  'tryApplyDefaultValue: $CalendarRuleInfo.fromMap(node).toString()',
            );
          }

          try {
            if (CalendarRuleInfo.fromMap(node).codeId ==
                CalendarRuleInfoCodeId.startDate.value) {
              //if id is null then it's a new and if we have a calendarDateTimeTapped set the value
              if (id == null && calendarDateTimeTapped != null) {
                fields[fieldKey] = CalendarRuleInfo.fromMap(node).copyWithValue(
                  calendarDateTimeTapped.toApiDateTimeString(),
                );
              } else {
                //If it's a single instance of a series ...
                if (calendarItemInfo != null &&
                    calendarItemInfo.startDate != null &&
                    id != null) {
                  fields[fieldKey] =
                      CalendarRuleInfo.fromMap(node).copyWithValue(
                    calendarItemInfo.startDate!.toApiDateTimeString(),
                  );
                }
              }
            }
          } catch (e) {
            _logger.info(
              message: 'copyWithValue: node = $node',
            );
          }

          try {
            // //set the defaults as specified in US17689
            //Set duration to 1 hour, duration is in minutes it seems ..
            if (CalendarRuleInfo.fromMap(node).codeId ==
                    CalendarRuleInfoCodeId.duration.value &&
                id == null &&
                calendarDateTimeTapped != null) {
              fields[fieldKey] = CalendarRuleInfo.fromMap(node)
                  .copyWithValue(Duration.minutesPerHour.toString());
            }
          } catch (e) {
            _logger.info(
              message: 'copyWithValue: node = $node',
            );
          }
        },
      );

      emit(
        EditCalendarItemLoadedState(
          item: item,
          calendarItemId: id,
          fields: Map.unmodifiable(fields),
          saving: false,
        ),
      );
    });
  }

  /// Attempts to create a [FieldKey] based on the parent node
  /// for the given field. Specifically, if the parent type is recurrence,
  /// we know that the recurrence Id can be assigned to the field. Otherwise,
  /// the field will have no recurrence and will be deemed a parentless field.
  FieldKey _extractFieldKey(
    Map<String, dynamic>? parent,
    CalendarRuleInfoCodeId fieldId,
  ) {
    late FieldKey fieldKey;

    try {
      CalendarAppointmentRecurrence? recurrence;
      if (parent?['type'] == 'Recurrence') {
        recurrence = CalendarAppointmentRecurrence.parse(parent!['value']);
      }

      fieldKey = FieldKey(
        parentRecurrenceId: recurrence,
        fieldId: fieldId,
      );
    } catch (e) {
      _logger.info(
        message: '_extractFieldKey: parent = $parent, fieldId: $fieldId',
      );
    }
    return fieldKey;
  }

  Future<void> saveCalendarItem({
    required Map<FieldKey, CalendarRuleInfo> changes,
    required AppLocalizations l10n,
  }) async {
    assert(state is EditCalendarItemLoadedState);

    final editState = state as EditCalendarItemLoadedState;
    emit(editState.copyWith(saving: true));

    ApiResponse? response;

    try {
      // apply the changes by converting to a map, traversing, and patching.
      final changedValues =
          changes.map((key, value) => MapEntry(key, value.value));

      final CalendarRuleInfo patchedCalendarItem = patchCalendarItem(
        editState.item,
        changes: changedValues,
      );
      response =
          await _calendarRepository.createCalendarRequest(patchedCalendarItem);

      if (response.isOk() == false) {
        await _logger.trace(message: response.toString());
        throw response;
      }

      emit(
        EditCalendarItemApiCallSuccessState(
          apiResponse: response,
        ),
      );

      await Future<void>.delayed(const Duration(seconds: 3));

      emit(const EditCalendarItemSubmittedState());
    } catch (e) {
      await _logger.info(
        message: 'apiResponse: response = $response',
      );
      // some failure, so just raise a generic error
      emit(editState.copyWith(saving: false));
      _bannerMessages.add(
        ErrorBannerMessage(
          text: l10n.submitRequestFailed,
        ),
      );
    }
  }

  CalendarRuleInfo patchCalendarItem(
    CalendarRuleInfo calendarItem, {
    required Map<FieldKey, String?> changes,
  }) {
    final recurrenceTypeString =
        changes[FieldKey.parentlessField(CalendarRuleInfoCodeId.recurrence)]!;

    final selectedRecurrenceType =
        CalendarAppointmentRecurrence.parse(recurrenceTypeString);

    final updated = calendarItem.patch(
      operation: (fieldId, parent, node) {
        // handle selecting the right recurrence value.
        final fieldKey = _extractFieldKey(parent, fieldId);

        if (!changes.containsKey(fieldKey) ||
            (fieldKey.parentRecurrenceId != null &&
                fieldKey.parentRecurrenceId != selectedRecurrenceType)) {
          // if this isn't a field we changed OR it belongs to a different
          // recurrence type aside from the selected one, skip it.
          return;
        }

        node['value'] = changes[fieldKey];
      },
    );

    // convert back to a type safe object for saving.
    return CalendarRuleInfo.fromMap(updated);
  }

  Future<void> deleteNonSeriesCalendarItem({
    required AppLocalizations l10n,
  }) async {
    assert(state is EditCalendarItemCloseDeleteDialogState);

    final actualState = state as EditCalendarItemCloseDeleteDialogState;

    // can only delete via change request
    assert(actualState.isChangeRequest);

    try {
      // try to submit the delete change request.
      final deleteResponse = await _calendarRepository
          .deleteNonSeriesCalendarItem(actualState.calendarItemId!);

      if (deleteResponse.httpStatusCode != 200) {
        await _logger.trace(message: deleteResponse.toString());
        // some failure
        throw deleteResponse;
      }

      // tell the UI to navigate back to the previous page
      emit(
        EditCalendarItemApiCallSuccessState(
          apiResponse: deleteResponse,
        ),
      );

      await Future<void>.delayed(const Duration(seconds: 3));

      emit(const EditCalendarItemSubmittedState());
    } catch (e) {
      // some failure, so just raise a generic error
      emit(actualState.copyWith(saving: false));
      _bannerMessages.add(
        ErrorBannerMessage(
          text: l10n.submitRequestFailed,
        ),
      );
    }
  }

  Future<void> deleteSeriesCalendarItem({
    required AppLocalizations l10n,
    required String date,
  }) async {
    assert(state is EditCalendarItemCloseDeleteDialogState);

    final actualState = state as EditCalendarItemCloseDeleteDialogState;

    // can only delete via change request
    assert(actualState.isChangeRequest);

    try {
      // try to submit the delete change request.
      final deleteResponse = await _calendarRepository.deleteSeriesCalendarItem(
        actualState.calendarItemId!,
        date,
      );

      if (deleteResponse.httpStatusCode != 200) {
        await _logger.trace(message: deleteResponse.toString());
        // some failure
        throw deleteResponse;
      }

      // tell the UI to navigate back to the previous page
      emit(
        EditCalendarItemApiCallSuccessState(
          apiResponse: deleteResponse,
        ),
      );
      await Future<void>.delayed(const Duration(seconds: 3));

      emit(const EditCalendarItemSubmittedState());
    } catch (e) {
      // some failure, so just raise a generic error
      emit(actualState.copyWith(saving: false));
      _bannerMessages.add(
        ErrorBannerMessage(
          text: l10n.submitRequestFailed,
        ),
      );
    }
  }

  Future<void> requestCalendarItemException({
    DateTime? date,
    required int id,
    required Map<FieldKey, CalendarRuleInfo> changes,
    required AppLocalizations l10n,
  }) async {
    // can only delete loaded state
    assert(state is EditCalendarItemLoadedState);

    final actualState = state as EditCalendarItemLoadedState;

    // can only delete via change request
    assert(actualState.isChangeRequest);

    emit(actualState.copyWith(saving: true));

    final changedValues =
        changes.map((key, value) => MapEntry(key, value.value));

    final CalendarRuleInfo patchedCalendarItem = patchCalendarItem(
      actualState.item,
      changes: changedValues,
    );

    await _logger.trace(message: patchedCalendarItem.toJson());
    try {
      // try to submit the change request.
      final exceptionResponse =
          await _calendarRepository.createExceptionRequest(
        date: date,
        id: actualState.calendarItemId!,
        calendarRuleInfo: patchedCalendarItem,
      );

      if (exceptionResponse.httpStatusCode != 200) {
        await _logger.trace(message: exceptionResponse.toString());
        // some failure
        throw exceptionResponse;
      }

      emit(
        EditCalendarItemApiCallSuccessState(
          apiResponse: exceptionResponse,
        ),
      );
      await Future<void>.delayed(const Duration(seconds: 3));

      emit(const EditCalendarItemSubmittedState());
    } catch (e) {
      // some failure, so just raise a generic error
      emit(actualState.copyWith(saving: false));
      _bannerMessages.add(
        ErrorBannerMessage(
          text: l10n.submitRequestFailed,
        ),
      );
    }
  }

  @visibleForTesting
  CalendarRuleInfo tryApplyDefaultValue({required CalendarRuleInfo field}) {
    // already have a value, so leave it alone
    if (field.value != null) return field;

    // not a drop-down, so cann't infer default value.
    if (field.availableValues == null) return field;

    final defaultValue = field.availableValues!.isNotEmpty
        ? field.availableValues!.first.value
        : null;

    return field.copyWithValue(defaultValue);
  }

  Future<void> closeDialogForDeleteCalendarItem(
    int recurrenceTypeId,
    String date,
    int? calendarItemId,
  ) async {
    // can only delete loaded state
    assert(state is EditCalendarItemLoadedState);

    final actualState = state as EditCalendarItemLoadedState;

    // can only delete via change request
    assert(actualState.isChangeRequest);

    emit(
      EditCalendarItemCloseDeleteDialogState(
        recurrenceId: recurrenceTypeId,
        date: date,
        calendarItemId: calendarItemId,
        saving: true,
      ),
    );
  }
}
