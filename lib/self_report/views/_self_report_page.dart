import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/client_settings/views/views.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/cubit/_self_report_state.dart';
import 'package:embark/self_report/views/_self_report_view.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:widgets/widgets.dart';

class SelfReportPage extends StatelessWidget {
  const SelfReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    return PluginRequirement(
      pluginType: PluginType.selfReport,
      child: Safe<PERSON><PERSON>(
        // ignore: deprecated_member_use
        child: BiEmbarkBlocProvider<SelfReportCubit, SelfReportState>(
          create: (_) => getIt.get<SelfReportCubit>(),
          init: (cubit) => cubit.init(l10n: l10n),
          internetConnectivityStream: connectionStatus,
          child: const PopScope(
            canPop: false,
            child: SelfReportView(),
          ),
        ),
      ),
    );
  }
}
