import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/notification_history/cubit/notification_history_cubit.dart';
import 'package:embark/notification_history/views/notification_history_view.dart';
import 'package:flutter/material.dart';
import 'package:embark/starter.dart';
import 'package:widgets/widgets.dart';

class NotificationHistoryPage extends StatelessWidget {
  const NotificationHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();

    // ignore: deprecated_member_use
    return BiEmbarkBlocProvider<NotificationHistoryCubit,
        NotificationHistoryState>(
      create: (_) => getIt.get<NotificationHistoryCubit>(),
      internetConnectivityStream: connectionStatus,
      child: const NotificationHistoryView(),
    );
  }
}
