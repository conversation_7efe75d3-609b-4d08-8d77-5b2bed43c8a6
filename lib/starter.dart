// ignore_for_file: deprecated_member_use_from_same_package

import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_app_insights_logging/bi_flutter_app_insights_logging.dart';
import 'package:bi_flutter_background_location/bi_flutter_background_location.dart';
import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_login_widget/services/secure_storage_service.dart';
import 'package:bi_flutter_mdm_platform_api/platform/_bi_mdm_platform_api.dart';
import 'package:bi_flutter_notifications/plugin/_bi_flutter_notifications.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:camera/camera.dart';
import 'package:clock/clock.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:embark/address_change_request/cubit/cubit.dart';
import 'package:embark/api/api.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/authentication/auth_steps/default_auth_step.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/authentication/auth_steps/get_auth_settings.dart';
import 'package:embark/authentication/auth_steps/post_authentication_auth_step.dart';
import 'package:embark/authentication/auth_steps/showing_dashboard_step.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/calendar/edit-page/cubit/edit_calendar_item_cubit.dart';
import 'package:embark/calendar/tab-page/cubit/calendar_cubit.dart';
import 'package:embark/call_history/cubit/call_history_cubit.dart';
import 'package:embark/call_in_progress/cubit/call_in_progress_cubit.dart';
import 'package:embark/call_maker/cubit/call_maker_cubit.dart';
import 'package:embark/check_in/blink_detector.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/check_in/face_detector_service.dart';
import 'package:embark/contact_info_change_request/cubit/cubit.dart';
import 'package:embark/dashboard/cubit/_dashboard_cubit.dart';
import 'package:embark/databases/databases.dart';
import 'package:embark/device_setup/cubit/cubit.dart';
import 'package:embark/documents/cubit/_documents_repository.dart';
import 'package:embark/documents/cubit/add_document/add_document_cubit.dart';
import 'package:embark/documents/cubit/add_document/document_camera_cubit.dart';
import 'package:embark/extensions/_stream_to_listener.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/language/cubit/language_cubit.dart';
import 'package:embark/location/_location_services.dart';
import 'package:embark/login/cubit/login_cubit.dart';
import 'package:embark/mdm/_embark_platform_observer.dart';
import 'package:embark/messages/cubit/messages_cubit.dart';
import 'package:embark/navigation_observers/navigation_observers.dart';
import 'package:embark/notification_history/cubit/notification_history_cubit.dart';
import 'package:embark/open_text_change_request/cubit/cubit.dart';
import 'package:embark/permissions/cubit/permissions_cubit.dart';
import 'package:embark/pin/reset/cubit/pin_reset_cubit.dart';
import 'package:embark/profile/cubit/_profile_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notifications/cubit/push_notifications_cubit.dart';
import 'package:embark/repositories/_app_version_repository.dart';
import 'package:embark/repositories/_background_isolate_starter_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/_disk_storage_repository.dart';
import 'package:embark/repositories/_profile_repository.dart';
import 'package:embark/repositories/_tracking/_android_tracking_repository.dart';
import 'package:embark/repositories/_tracking/_background_network.dart';
import 'package:embark/repositories/_tracking/_ios_tracking_repository.dart';
import 'package:embark/repositories/_tracking/_tracking_config_repository.dart';
import 'package:embark/repositories/_tracking/_tracking_repository.dart';
import 'package:embark/repositories/_voip_call_repository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/resources/cubit/resources_cubit.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/services/_wake_lock_service.dart';
import 'package:embark/services/background_jobs/auth/auth.dart';
import 'package:embark/services/services.dart';
import 'package:embark/settings/cubit/settings_cubit.dart';
import 'package:embark/terms_and_conditions/cubit/terms_and_conditions_cubit.dart';
import 'package:embark/video_conference/cubit/video_conference_cubit.dart';
import 'package:file/file.dart';
import 'package:file/local.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:twilio_programmable_video/twilio_programmable_video.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';

final getIt = GetIt.instance;

/// Used to define factory method overrides when registering [getIt] dependencies.
class DependencyOverrides {
  final BiTokenManager Function()? tokenManagerOverride;
  final InternetConnectivityRepository Function()?
      internetConnectivityRepositoryOverride;

  DependencyOverrides({
    required this.tokenManagerOverride,
    required this.internetConnectivityRepositoryOverride,
  });
}

abstract class Starter {
  Future<void> initializeDependencyInjection({
    DependencyOverrides? overrides,
    required bool
        isForeground, // TODO: make seperate foreground/background starter classes instead of using this
  });

  Future<void> resetDependencyInjection();

  Future<void> intializeCubits();

  Future<void> initializeIndependentDependencies();

  Future<void> initializeDomainDependentDependencies({
    DependencyOverrides? overrides,
  });

  String get appVersion;
}

class StarterImplementation implements Starter {
  StarterImplementation({
    required this.appSettings,
  });

  static Future<StarterImplementation> standard() async {
    return StarterImplementation(
      appSettings: await AppSettings.load(),
    );
  }

  Locale get locale => WidgetsBinding.instance.platformDispatcher.locale;
  bool initialized = false;
  final AppSettings appSettings;

  // TODO:  retrieve from BiPackageInfo.version once it's available
  static const _appVersion = '5016';

  @override
  String get appVersion => _appVersion;

  @override
  Future<void> initializeDependencyInjection({
    required bool isForeground,
    DependencyOverrides? overrides,
  }) async {
    if (initialized) {
      return;
    }

    debugPrint('Starter.initializeDependencyInjection');
    await initializeIndependentDependencies();

    final platformApi = BiMdmPlatformApi();
    if (isForeground) {
      getIt.registerLazySingleton(() => FlutterBackgroundService());
      getIt.registerSingleton<BiMdmPlatformApi>(
        EmbarkMdmPlatformObserver(
          preferencesService: getIt.get(),
          original: platformApi,
        ),
      );
      getIt.registerLazySingleton(
        () => BackgroundIsolateStarterRepository(
          backgroundService: getIt.get(),
        ),
      );
    } else {
      getIt.registerSingleton<BiMdmPlatformApi>(
        EmbarkBackgroundIsolateMdmPlatformApi(
          preferencesService: getIt.get(),
          original: platformApi,
        ),
      );
    }

    // needs logging interfaces, so just place this after that has been registered.
    _registerBluetoothInterfaces(isForeground);

    await initializeDomainDependentDependencies(
      overrides: overrides,
    );
    if (isForeground) {
      await intializeCubits();
    }
    getIt.get<Dio>().setup(getIt.get(), getIt.get());
    if (isForeground) {
      await _initializeForegroundSpecificDependencies();
    }
    initialized = true;
  }

  @override
  Future<void> resetDependencyInjection() async {
    debugPrint('Starter.resetDependencyInjection');
    await getIt.reset(dispose: true);
  }

  @override
  Future<void> intializeCubits() async {
    debugPrint('Starter.intializeCubits');

    final AppRepository appRepository =
        getIt.get<AppRepository>(instanceName: 'AppRepository');

    _registerAppRepositoryInterfaces(appRepository);

    getIt.registerFactory(
      () => CalendarCubit(
        getIt.get<CalendarRepository>(),
        appRepository,
        getIt.get<ClientSettingsRepository>(),
        getIt.get<BiTokenManager>(),
      ),
    );

    getIt.registerFactory(
      () => EditCalendarItemCubit(
        getIt.get<CalendarRepository>(),
        appRepository.bannerMessageSink,
        getIt.get<BiLogger>(),
      ),
    );

    getIt.registerFactory(
      () => ProfileCubit(
        getIt.get<ContactInfoRepository>(),
        getIt.get<ProfileRepository>(),
        appRepository.bannerMessageSink,
        getIt.get<BiLogger>(),
      ),
    );

    getIt.registerFactory<Sink<BannerMessage>>(
      () => appRepository.bannerMessageSink,
    );

    getIt.registerFactory(
      () => HomeCubit(
        clientSettingsRepository: getIt.get<ClientSettingsRepository>(),
        appRepository: getIt.get<AppRepository>(instanceName: 'AppRepository'),
        calendarRepository: getIt.get(),
        trackingSetup: getIt.get(),
        locationRepository: getIt.get(),
        smartBandUiRepository: getIt.get(),
      ),
    );
    getIt.registerFactory<AddDocumentCubit>(
      () => AddDocumentCubit(getIt.get(), getIt.get()),
    );
    getIt.registerFactory(
      () => DocumentCameraCubit(getIt.get(), getIt.get(), getIt.get()),
    );

    getIt.registerFactory(
      () => ResourcesCubit(
        resourcesRepository: getIt.get<CommunityReferralRepository>(),
      ),
    );

    getIt.registerFactory(
      () => LanguageCubit(
        getIt.get<AppSettingsRepository>(),
        getIt.get<AuthManager>(),
      ),
    );

    getIt.registerFactory(
      () => SettingsCubit(
        getIt.get<AppSettingsRepository>(),
        appRepository,
        getIt.get(),
        getIt.get(),
      ),
    );

    getIt.registerFactory(
      () => MessagesCubit(
        getIt.get<MessagesRepository>(),
        appRepository,
        appRepository.bannerMessageSink,
        getIt.get<BiLogger>(),
        getIt.get<BiTokenManager>(),
        getIt.get<SharedPreferencesService>(),
        getIt.get<SecureStorageService>(),
      ),
    );
    getIt.registerSingleton(const ImageCompression());

    getIt.registerFactory(
      () => PermissionsCubit(
        appRepository,
        getIt.get<AuthManager>(),
      ),
    );

    getIt.registerFactory(
      () => DashboardCubit(
        appRepository,
        getIt.get<EmbarkAuthenticationRepository>(),
        getIt.get<ClientSettingsRepository>(),
        getIt.get<AppSettingsRepository>(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
      ),
    );

    getIt.registerFactory(
      () => PushNotificationsCubit(
        getIt.get<PushNotificationsRepository>(
          instanceName: 'PushNotificationsRepository',
        ),
        PushNotificationsTokenSerivce(
          getIt.get(),
          getIt.get(),
        ),
        getIt.get<BiLogger>(),
      )..init(),
    );
    getIt.registerLazySingleton(
      () => AuthHandler.prod(
        postAuthenticationAuthStep: PostAuthenticationAuthStep(
          pushNotificationsCubit: getIt.get(),
          deviceInfoRepository: getIt.get(),
          clientSettingsRepository: getIt.get(),
          authenticationRepository: getIt.get(),
          deviceSetupRepository: getIt.get(),
          appSettingsRepository: getIt.get(),
          permissionHandler: getIt.get(),
        ),
        getSettings: GetAuthSettingsStep(clientSettingsRepository: getIt.get()),
        mdmAuthHandler: getIt.get<MdmAuthHandler>(),
        defaultAuthStep: DefaultAuthStep(
          clientSettingsRepository: getIt.get(),
        ),
      ),
    );
    final mdmAuthHandler = MdmAuthHandler(getIt.get(), getIt.get());
    getIt.registerSingleton(
      mdmAuthHandler,
    );
    getIt.registerSingleton<MdmShowAuth>(mdmAuthHandler);
    getIt.registerLazySingleton(
      () => EmbarkAuthenticationBloc(
        authenticationRepository: getIt.get<EmbarkAuthenticationRepository>(),
        clientSettingsRepository: getIt.get<ClientSettingsRepository>(),
        authHandler: getIt.get(),
        deviceSetupRepository: getIt.get(),
      ),
    );

    getIt.registerLazySingleton(
      () => AddressRepository(
        getIt.get<SmartLinkAddressHttpClient>(),
        getIt.get<EmbarkKeyValueDatabase>().addressChangeRequestInfoBox,
      ),
    );

    getIt.registerLazySingleton(
      () => CommunityReferralRepository(
        getIt.get<SmartLinkCommunityProviderHttpClient>(),
        getIt.get<ClientRepository>(),
        getIt.get<EmbarkKeyValueDatabase>().communityReferralBox,
      ),
    );

    getIt.registerFactory(
      () => SelfReportCubit(
        getIt.get<BiometricRepository>(),
        getIt.get<BiLogger>(),
        getIt.get<AddressRepository>(),
        getIt.get<ContactInfoRepository>(),
        getIt.get<Sink<BannerMessage>>(),
        getIt.get<TransportationRepository>(),
        getIt.get<EmploymentRepository>(),
        getIt.get<OpenTextRepository>(),
        getIt.get<PersonalContactsRepository>(),
        getIt.get<CommunityReferralRepository>(),
        getIt.get<AppRepository>(instanceName: 'AppRepository'),
        getIt.get<ClientSettingsRepository>(),
      ),
    );

    getIt.registerFactory(
      () => SelfReportSummaryCubit(
        getIt.get<OpenTextRepository>(),
      ),
    );

    getIt.registerFactory(
      () => AddressChangeRequestCubit(
        getIt.get<AddressRepository>(),
      ),
    );

    getIt.registerFactory(
      () => ContactInfoChangeRequestCubit(
        getIt.get<ContactInfoRepository>(),
      ),
    );

    getIt.registerFactory(
      () => OpenTextChangeRequestCubit(
        getIt.get<OpenTextRepository>(),
      ),
    );
    getIt.registerSingleton<CameraControllerFactory>(
      CameraControllerFactory.prod(),
    );

    getIt.registerFactory(
      () => DeviceSetupCubit(
        null,
        deviceTypeRepository: getIt.get<DeviceSetupRepository>(),
        logger: getIt.get(),
      ),
    );

    getIt.registerFactory(
      () => getIt.get<InternetConnectivityRepository>().connectionStream,
    );

    getIt.registerFactory(
      () => NotificationHistoryCubit(
        getIt.get<PushNotificationsRepository>(
          instanceName: 'PushNotificationsRepository',
        ),
      ),
    );

    getIt.registerFactory(
      () => VideoConferenceCubit(
        twilioProgrammableVideoService:
            getIt.get<TwilioProgrammableVideoService>(),
        cameraSourceService: getIt.get<CameraSourceService>(),
        wakelockService: getIt.get<WakelockService>(),
        logger: getIt.get(),
        videoConferenceRepository: getIt.get(),
      ),
    );

    getIt.registerFactory(
      () => CallHistoryCubit(
        voipCallRepository: getIt.get<VoIPCallRepository>(),
      ),
    );

    getIt.registerFactory(
      () => PushNotificationListenerCubit(
        getIt.get(),
        getIt.get<AppRepository>(instanceName: 'AppRepository'),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get<RemoteNotificationsService>(
          instanceName: 'RemoteNotificationsService',
        ),
      ),
    );

    getIt.registerFactory(
      () => CallMakerCubit(
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
        getIt.get(),
      ),
    );

    getIt.registerFactory(
      () => CallInProgressCubit(
        twilioDialer: getIt.get<TwilioDialer>(),
        voIPCallRepository: getIt.get(),
      ),
    );

    getIt.registerFactoryParam<CameraController, CameraDescription,
        ResolutionPreset>(
      (CameraDescription? camera, ResolutionPreset? resoution) {
        if (camera != null && resoution != null) {
          return CameraController(camera, resoution);
        } else {
          return CameraController(
            const CameraDescription(
              lensDirection: CameraLensDirection.front,
              name: '',
              sensorOrientation: 0,
            ),
            ResolutionPreset.high,
          );
        }
      },
    );
    getIt.registerFactory(() => FaceDetectionService(getIt.get()));
    getIt.registerFactory<FaceDetector>(() {
      final options = FaceDetectorOptions(
        enableTracking: true,
        minFaceSize: .7,
        enableLandmarks: true,
        performanceMode: FaceDetectorMode.accurate,
        enableClassification: true,
      );

      return FaceDetector(options: options);
    });

    getIt.registerFactory(
      () => LoginCubit(
        appVersionRepository: getIt.get<AppVersionRepository>(),
        packageInfo: getIt.get<BiPackageInfo>(),
      ),
    );
    getIt.registerFactory(
      () => TermsAndConditionsCubit(
        getIt.get(),
        getIt.get(),
        getIt.get(),
      ),
    );
    getIt.registerFactory(
      () => PinResetCubit(
        getIt.get<AppRepository>(instanceName: 'AppRepository'),
        getIt.get<SharedPreferencesService>(),
        getIt.get(),
        getIt.get<ClientSettingsRepository>(),
      ),
    );
  }

  @override
  Future<void> initializeIndependentDependencies({
    DependencyOverrides? overrides,
  }) async {
    debugPrint('Starter.initializeIndependentDependencies');
    final preferences = await SharedPreferences.getInstance();

    getIt.registerSingleton(SharedPreferencesService(preferences));
    getIt.registerSingleton(SmartBandPreferences(getIt.get()));
    getIt.registerSingleton(EmbarkPermissionPreferences(getIt.get()));

    getIt.registerLazySingleton(() => BiBluetoothApi());
    getIt.registerSingleton(BiFlutterNotifications());
    getIt.registerSingleton(BiFlutterBackgroundLocation());
    getIt.registerLazySingleton(
      () => BiPermissionRequestRepository(
        bluetoothApi: getIt.get(),
      ),
    );

    getIt.registerSingleton(
      UrlLauncherPlatform.instance,
    );
    getIt.registerSingleton(
      Connectivity(),
      instanceName: 'Connectivity',
    );
    getIt.registerFactory(
      () => L10nAssetLoader(logger: getIt.get<BiLogger>()),
    );
    getIt.registerSingleton(const Clock());
    getIt.registerSingleton(appSettings);
    getIt
        .registerSingleton<FeatureFlags>(getIt.get<AppSettings>().featureFlags);
    getIt.registerFactory<BiPackageInfo>(
      () => getIt.get<AppSettings>().packageInfo,
    );

    getIt.registerSingleton(
      const FlutterSecureStorage(
        iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock),
      ),
    );
    getIt.registerLazySingleton<BiLogger>(() {
      final apiKey = getIt.get<AppSettings>().appInsightsKey;

      BiAppInsightsLoggerSingleton.configure(
        // if the api key is null or empty it will just ignore any logs
        // being written by this logger. This allows us to also configure if logs
        // are recorded to app insights in any build as well, because they can
        // incur quite the cost when we have all clients on the app.
        instrumentationKey: apiKey,
      );
      return BiMultiLogger(
        loggers: [
          BiConsoleLogger(),
          BiAppInsightsLoggerSingleton.instance,
          BiFirebaseCrashlyticsLogger(),
        ],
      );
    });
    getIt.registerLazySingleton(
      () => BackgroundJobCredentialService(
        storage: getIt.get(),
      ),
    );

    getIt.registerLazySingleton<Dio>(
      () => Dio(),
    );

    getIt.registerLazySingleton(
      () {
        final dio = Dio();
        final logger = getIt.get<BiLogger>();
        final settings = getIt.get<AppSettings>();
        final internetConnectivityRepo =
            getIt.get<InternetConnectivityRepository>();

        BiDioInterceptorConfiguration.applyForCdn(
          dio: dio,
          logger: logger,
          settings: settings,
          internetConnectivityRepo: internetConnectivityRepo,
        );
        return dio;
      },
      instanceName: 'cdnDio',
    );

    getIt.registerLazySingleton<BiCdnHttpClient>(() {
      // CDN needs its own dio instance so the CDN requests don't recurse
      // and to prevent conflicts.
      final dio = getIt.get<Dio>(instanceName: 'cdnDio');
      final logger = getIt.get<BiLogger>();
      final settings = getIt.get<AppSettings>();

      return BiCdnHttpClient(
        dio: dio,
        logger: logger,
        cdnBaseUrl: settings.cdnHost,
      );
    });

    getIt.registerLazySingleton<BiCdnHttpClient>(
      () {
        // CDN needs its own dio instance so the CDN requests don't recurse
        // and to prevent conflicts.
        final dio = getIt.get<Dio>(instanceName: 'cdnDio');
        final logger = getIt.get<BiLogger>();
        final settings = getIt.get<AppSettings>();

        return BiCdnHttpClient(
          dio: dio,
          logger: logger,
          cdnBaseUrl: settings.secondaryCdnHost,
        );
      },
      instanceName: 'secondaryCdn',
    );

    getIt.registerSingleton<EmbarkKeyValueDatabase>(
      await EmbarkKeyValueDatabase.create(),
    );

    getIt.registerSingleton(const PermissionHandler());

    getIt.registerSingleton(
      LocationServices(),
    );
    getIt.registerSingleton(
      LocationPermissionRepository(
        getIt.get(),
        getIt.get<BiPermissionRequestRepository>().supportedPermissions,
        getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => LocationRepository(
        locationServices: getIt.get<LocationServices>(),
        getLocationPermissionRepository: getIt.get(),
        permissionRequestRepository: getIt.get(),
        logger: getIt.get(),
      ),
    );

    getIt.registerSingleton(
      Battery(),
    );

    getIt.registerSingleton(
      BatteryRepository(
        getIt.get<Battery>(),
      ),
    );

    getIt.registerSingleton(
      DeviceInfoPlugin(),
    );

    getIt.registerFactory(
      () => CameraSourceService(),
    );

    getIt.registerFactory(
      () => TwilioProgrammableVideoService(),
    );

    getIt.registerFactoryAsync(
      () async {
        final sources = await CameraSource.getSources();
        return CameraCapturer(
          sources.firstWhere((source) => source.isFrontFacing),
        );
      },
    );

    getIt.registerSingleton<TwilioDialer>(TwilioDialer());

    getIt.registerFactory(
      () => PushNotificationsTokenSerivce(
        getIt.get(),
        getIt.get(),
      ),
    );

    getIt.registerFactory(() => AudioPlayer());

    getIt.registerSingleton(
      WakelockService(),
    );
  }

  Future<void> _initializeForegroundSpecificDependencies() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      getIt.registerSingleton<TrackingRepository>(
        TrackingRepositoryAndroid(
          getIt.get(),
          getIt.get(),
          getIt.get(),
          getIt.get(),
        ),
      );
    } else {
      // This must not be a lazy init
      final repo = TrackingRepositoryIos(
        trackingSetup: getIt.get(),
        permissionRepository: getIt.get(),
        backgroundNetwork: SetupBackgroundNetworkDependencies(
          logger: getIt.get(),
          settings: getIt.get(),
          secureStorage: getIt.get(),
          appRepository:
              getIt.get<AppRepository>(instanceName: 'AppRepository'),
          backgroundJobCredentialService: getIt.get(),
          backgroundTrackingLocation: getIt.get(),
          trackingSetup: getIt.get(),
          initialLocale: locale,
          backgroundLocationTracking: getIt.get(),
          deviceType: getIt.get<DeviceSetupRepository>(),
          sharedPreferencesService: getIt.get(),
        ),
      );
      getIt.registerSingleton<TrackingRepository>(repo);
    }
  }

  @override
  Future<void> initializeDomainDependentDependencies({
    DependencyOverrides? overrides,
  }) async {
    debugPrint('Starter.initializeDomainDependentDependencies');

    getIt.registerLazySingleton(
      () {
        final settings = getIt.get<AppSettings>();
        return BiEmbarkStsHttpClient(
          dio: getIt.get<Dio>(),
          logger: getIt.get<BiLogger>(),
          clientId: settings.stsClientId,
          clientSecret: settings.stsClientSecret,
          reviewUserEmail: settings.reviewUserUsername,
          sharedPreferences: getIt.get(),
          backgroundJobCredentialService: getIt.get(),
        );
      },
    );
    getIt.registerLazySingleton(
      () => CheckinTimestampRepository(sharedPreferencesService: getIt.get()),
    );
    getIt.registerLazySingleton(
      () =>
          overrides?.tokenManagerOverride?.call() ??
          BiTokenManager(
            getIt.get<FlutterSecureStorage>(),
            getIt.get<BiEmbarkStsHttpClient>(),
            getIt.get<BiLogger>(),
          ),
    );

    getIt.registerLazySingleton(
      () => SmartLinkAppSettingsHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => SmartLinkCalendarHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => SmartLinkClientHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => SmartLinkNotificationsHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => SmartLinkMediaClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => SmartLinkTransportationHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => SmartLinkEmploymentHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerLazySingleton(
      () => SmartLinkBiometricHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );

    getIt.registerLazySingleton<SmartLinkAddressHttpClient>(
      () => SmartLinkAddressHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );

    getIt.registerLazySingleton<SmartLinkFirmwareInfoHttpClient>(
      () => SmartLinkFirmwareInfoHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );

    getIt.registerLazySingleton<SmartLinkContactInfoHttpClient>(
      () => SmartLinkContactInfoHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );

    getIt.registerLazySingleton<SmartLinkEntityContactHttpClient>(
      () => SmartLinkEntityContactHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );

    getIt.registerLazySingleton<SmartLinkCommunityProviderHttpClient>(
      () => SmartLinkCommunityProviderHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );

    getIt.registerLazySingleton<SmartLinkVoIPCallHttpClient>(
      () => SmartLinkVoIPCallHttpClient(
        dio: getIt.get<Dio>(),
        logger: getIt.get<BiLogger>(),
      ),
    );
    getIt.registerSingleton(ShowingDashboardStep());
    getIt.registerSingleton(
      SecureStorageService(),
    );

    getIt.registerSingleton(
      DioDeviceTypeSwitcher(
        appSettings: getIt.get(),
        tokenManager: getIt.get(),
        dio: getIt.get(),
        logger: getIt.get(),
        backgroundJobCredentialService: getIt.get(),
        sharedPreferences: getIt.get(),
      ),
    );

    getIt.registerSingleton(
      DeviceSetupRepository(
        mdmPlatformApi: getIt.get<BiMdmPlatformApi>(),
        sharedPreferences: getIt.get<SharedPreferencesService>(),
        smartLinkClientHttpClient: getIt.get(),
        deviceTypeSwitcher: getIt.get(),
      ),
    );

    getIt.registerLazySingleton<FlutterLocalNotificationsPlugin>(
      () => FlutterLocalNotificationsPlugin(),
    );

    getIt.registerSingleton(
      RemoteNotificationsService(
        getIt.get<FlutterLocalNotificationsPlugin>(),
        getIt.get(),
        getIt.get(),
      ),
      instanceName: 'RemoteNotificationsService',
    );

    getIt.registerSingleton<AppRepository>(
      AppRepository(
        getIt.get<SharedPreferencesService>(),
        getIt.get<RemoteNotificationsService>(
          instanceName: 'RemoteNotificationsService',
        ),
        () => locale,
      ),
      instanceName: 'AppRepository',
      dispose: (p) async {
        await p.close();
      },
    );

    getIt.registerSingleton(
      PushNotificationsRepository(
        getIt.get<SmartLinkNotificationsHttpClient>(),
        getIt.get<RemoteNotificationsService>(
          instanceName: 'RemoteNotificationsService',
        ),
      ),
      instanceName: 'PushNotificationsRepository',
    );

    getIt.registerSingleton<FileSystem>(const LocalFileSystem());
    getIt.registerSingleton(const DocumentLauncher());
    getIt.registerSingleton(DiskStorageRepository(getIt.get()));
    getIt.registerSingleton(
      DocumentsRepository(getIt.get(), getIt.get(), getIt.get()),
    );
    getIt.registerSingleton(
      MessagesRepository(
        getIt.get<SmartLinkClientHttpClient>(),
      ),
    );

    getIt.registerLazySingleton(
      () => CalendarRepository(
        getIt.get<SmartLinkCalendarHttpClient>(),
        CalendarPreferenceService(
          getIt.get<EmbarkKeyValueDatabase>(),
        ),
      ),
    );

    getIt.registerLazySingleton(
      () => TransportationRepository(
        getIt.get<SmartLinkTransportationHttpClient>(),
        getIt.get<EmbarkKeyValueDatabase>().transportationRequestInfoBox,
      ),
    );

    getIt.registerLazySingleton(
      () => EmploymentRepository(
        getIt.get<SmartLinkEmploymentHttpClient>(),
        getIt.get<EmbarkKeyValueDatabase>().employmentRequestInfoBox,
      ),
    );

    getIt.registerLazySingleton(
      () => ContactInfoRepository(
        getIt.get<SmartLinkContactInfoHttpClient>(),
        getIt.get<EmbarkKeyValueDatabase>().contactInfoChangeRequestBox,
      ),
    );

    getIt.registerLazySingleton(
      () => PersonalContactsRepository(
        getIt.get<SmartLinkEntityContactHttpClient>(),
        getIt.get<EmbarkKeyValueDatabase>().personalContactRequestInfoBox,
      ),
    );

    getIt.registerLazySingleton(
      () => OpenTextRepository(
        getIt.get<EmbarkKeyValueDatabase>().openTextChangeRequestBox,
      ),
    );
    getIt.registerLazySingleton(
      () => ClientSettingsRepository(
        clientHttpClient: getIt.get<SmartLinkClientHttpClient>(),
        clientSettingsDatabase:
            getIt.get<EmbarkKeyValueDatabase>().clientSettingsBox,
        calendarRepository: getIt.get(),
        embarkAuthenticationRepository: getIt.get(),
        logger: getIt.get<BiLogger>(),
      ),
    );

    getIt.registerSingleton(
      EmbarkAuthenticationRepository(
        getIt.get<BiTokenManager>(),
        appRepository: getIt.get(instanceName: 'AppRepository'),
        requestLocationPermission: () =>
            getIt.get<LocationRepository>().requestLocationPermission(),
      ),
    );

    getIt.registerSingleton(CurrentRouteObserver());
    getIt.registerSingleton(ArrivedOnDashboardObserver());
    getIt.registerLazySingleton<AuthManager>(
      () => AuthManager(
        authenticationRepository: getIt.get<EmbarkAuthenticationRepository>(),
        authStatus: getIt
            .get<EmbarkAuthenticationBloc>()
            .stream
            .toValueListener(getIt.get<EmbarkAuthenticationBloc>().state),
        authHandler: getIt.get(),
      ),
    );
    getIt.registerLazySingleton(
      () => ClientRepository(
        getIt.get<SmartLinkClientHttpClient>(),
        getIt.get<EmbarkKeyValueDatabase>().clientBox,
      ),
    );

    getIt.registerSingleton(
      ProfileRepository(
        getIt.get<SmartLinkClientHttpClient>(),
      ),
    );

    getIt.registerLazySingleton(
      () => VideoConferenceRepository(
        getIt.get<SmartLinkClientHttpClient>(),
        getIt.get(),
      ),
    );

    getIt.registerSingleton(
      overrides?.internetConnectivityRepositoryOverride?.call() ??
          InternetConnectivityRepository(
            getIt.get<Connectivity>(
              instanceName: 'Connectivity',
            ),
          ),
    );

    getIt.registerLazySingleton(
      () => DeviceInfoRepository(
        getIt.get<SmartLinkAppSettingsHttpClient>(),
        getIt.get<LocationRepository>(),
        getIt.get<BatteryRepository>(),
        getIt.get<AppRepository>(instanceName: 'AppRepository'),
        getIt.get<InternetConnectivityRepository>(),
        getIt.get<PushNotificationsRepository>(
          instanceName: 'PushNotificationsRepository',
        ),
        getIt.get<DeviceInfoPlugin>(),
        getIt.get<BiPackageInfo>(),
        getIt.get<BiLogger>(),
      ),
    );

    getIt.registerSingleton(
      BiometricRepository(
        getIt.get<SmartLinkBiometricHttpClient>(),
        getIt.get(),
      ),
    );
    getIt.registerSingleton(
      AppSettingsRepository(
        getIt.get<SmartLinkAppSettingsHttpClient>(),
        getIt.get<SharedPreferencesService>(),
        getIt.get(instanceName: 'AppRepository'),
        getIt.get<BiLogger>(),
      ),
    );
    getIt.registerFactory(
      () => VoIPCallRepository(getIt.get(), getIt.get()),
    );

    getIt.registerLazySingleton(
      () => PinRepository(
        getIt.get<SmartLinkAppSettingsHttpClient>(),
      ),
    );

    getIt.registerSingleton(
      AppVersionRepository(
        getIt.get<BiCdnHttpClient>(),
      ),
    );
    getIt.registerFactory(
      () => BlinkDetector(
        getIt.get(),
      ),
    );

    getIt.registerFactory(
      () => CameraUtil(
        permissionHandler: getIt.get<PermissionHandler>(),
        appRepository: getIt.get<AppRepository>(instanceName: 'AppRepository'),
      ),
    );

    getIt.registerFactory(
      () => FaceDetectionUtil(
        controllerBuilder: ({required resolutionPreset, int? fps}) =>
            getIt.get<CameraControllerFactory>().buildCamera(
                  direction: CameraLensDirection.front,
                  enableAudio: false,
                  resolutionPreset: resolutionPreset,
                  fps: fps,
                ),
        logger: getIt.get(),
        faceDetectionService: FaceDetectionService(getIt.get<FaceDetector>()),
        compression: getIt.get<ImageCompression>(),
        blinkDetector: getIt.get(),
      ),
    );
    getIt.registerSingleton(
      TrackingSetup(
        getIt.get(),
      ),
    );
    getIt.registerSingleton(
      TrackingConfigRepository(logger: getIt.get(), clientApi: getIt.get()),
    );

    getIt.registerLazySingleton(
      () => SensorEventInfoRepository(
        getIt.get<EmbarkKeyValueDatabase>().sensorEventInfoBox,
        getIt.get<BiometricRepository>(),
        getIt.get(),
      ),
    );
  }
}

extension DioSetup on Dio {
  void setup(
    BiTokenManager tokenManager,
    EmbarkAuthenticationRepository authenticationRepository,
  ) {
    final logger = getIt.get<BiLogger>();
    final cdn = getIt.get<BiCdnHttpClient>();
    final secondaryCdn =
        getIt.get<BiCdnHttpClient>(instanceName: 'secondaryCdn');
    final appRepository = getIt.get<AppRepository>(
      instanceName: 'AppRepository',
    );
    final internetConnectionRepo = getIt.get<InternetConnectivityRepository>();

    final appVersion = getIt.get<BiPackageInfo>().formatForMinVersionCheck;

    BiDioInterceptorConfiguration.apply(
      app: AppId.smartlink,
      appVersion: appVersion,
      primaryCdn: cdn,
      secondaryCdn: secondaryCdn,
      tokenManager: tokenManager,
      dio: this,
      logger: logger,
      appRepository: appRepository,
      internetConnectivityRepo: internetConnectionRepo,
      embarkAuthenticationRepository: authenticationRepository,
      sharedPreferences: getIt.get(),
      deviceSetupRepository: getIt.get<DeviceSetupRepository>(),
    );
  }
}

void _registerAppRepositoryInterfaces(AppRepository repo) {
  getIt.registerSingleton<BannerMessageRepository>(repo);
  getIt.registerSingleton<LocaleStreamRepository>(repo);
}

void _registerBluetoothInterfaces(bool isForeground) {
  if (isForeground) {
    getIt.registerSingleton(
      BluetoothFrontend(
        logger: getIt.get(),
        backgroundService: getIt.get(),
      ),
    );
  }
}
