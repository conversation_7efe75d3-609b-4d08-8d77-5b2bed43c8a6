trigger: none

resources:
  repositories:
    - repository: shared_pipeline
      type: git
      name: BI Software/shared_pipeline
      ref: "integration"

pool:
  vmImage: 'macOS-15'

variables:
- group: 'Common Flutter Variables'
- group: 'Embark Configs - DEV'

jobs:
- template: flutter_package.yml@shared_pipeline
  parameters:
    displayName: 'PR Build'
    flutterVersion: 3.27.1-stable
    skipStaticAnalysis: true
    minimumCodeCoverage: .678