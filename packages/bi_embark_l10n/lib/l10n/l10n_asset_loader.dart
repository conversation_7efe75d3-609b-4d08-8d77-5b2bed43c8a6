import 'package:bi_flutter_common_logging/bi_flutter_common_logging.dart';
import 'package:flutter/material.dart';

class L10nAssetLoader {
  static const String _privacyPolicyFilePrefix = 'privacy_policy';
  static const String _termsAndConditionsFilePrefix = 'terms_and_conditions';

  final BiLogger _logger;

  const L10nAssetLoader({required BiLogger logger}) : _logger = logger;

  Future<String?> _load({
    required BuildContext context,
    required String htmlFilePrefix,
    required String locale,
  }) async {
    final assetPath =
        'packages/bi_embark_l10n/assets/${htmlFilePrefix}_$locale.html';
    try {
      final assetBundle = DefaultAssetBundle.of(context);
      return await assetBundle.loadString(assetPath);
    } on FlutterError catch (e, __) {
      await _logger.trace(
        message:
            'Failed to load asset "$htmlFilePrefix" for language "$locale".',
        debugProps: {
          'error': e,
        },
      );
      // if the asset can't be loaded, return a null value.
    }
    return null;
  }

  Future<String?> loadPrivacyPolicy({
    required BuildContext context,
    required String locale,
  }) =>
      _load(
        context: context,
        htmlFilePrefix: _privacyPolicyFilePrefix,
        locale: locale,
      );

  Future<String?> loadTermsAndConditions({
    required BuildContext context,
    required String locale,
  }) =>
      _load(
        context: context,
        htmlFilePrefix: _termsAndConditionsFilePrefix,
        locale: locale,
      );
}
