import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_widgets/bi_widgets.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:widgets/widgets.dart';

typedef ItemAddedDelegate<T> = dynamic Function(T added);
typedef ItemUpdatedDelegate<T> = dynamic Function(T original, T updated);
typedef ItemDeletedDelegate<T> = dynamic Function(T deleted);
typedef ItemSavedDelegate<T> = Future<T?> Function(T saved);
typedef ItemsLoadedDelegate = dynamic Function();
typedef EditItemInitDelegate<T> = Future<dynamic> Function(T? item);

class BiEditableListView<T> extends StatelessWidget {
  /// This callback is triggered when the delete confirmation dialog has been answered
  /// "yes".
  final ItemDeletedDelegate<T>? onItemDeleted;

  /// A callback that is triggered when a user adds or creates an item and they
  /// click the "Save" button on the pushed "edit" page. This is useful in being
  /// able to save data according to different scenarios.
  ///
  /// For example, in the case of self report, items are not saved immediately,
  /// but only after the "continue" button is pressed. So in that case, there is
  /// no need to do anything on this callback. However, when persisting data to
  /// the API immediately, you might use this callback to immediately send a request
  /// to the API.
  final ItemSavedDelegate<T?>? onItemSaved;

  /// A callback that is triggered after the [load] method has completed and the
  /// list has set its internal state with the new list of data.
  final ItemsLoadedDelegate? onItemsLoaded;

  /// The method to use to load the list of data.
  final Future<List<ListDataChange<T>>> Function() load;

  /// The text to show inside the "add" button at the bottom of the list view.
  final String? addButtonText;

  /// The text to show in the delete confirmation dialog. If empty, a generic
  /// statement will be provided.
  final String? deleteConfirmationText;

  /// The title of the page that gets pushed onto the stack when an existing
  /// item is tapped or a new one is being added.
  final String editItemTitle;

  /// The initialization method to call prior to calling the [editItemFormFields]
  /// callback to show form fields. The results from this method are directly
  /// passed into the [editItemFormFields] for help in building an appropriate
  /// form content.
  final EditItemInitDelegate<T> onEditItemInit;

  /// A function that builds the form fields to inject inside the pushed page
  /// when a new item is to be added or an existing one is tapped on.
  ///
  /// When the user traps an existing [item], the [onTap] method will push
  /// a new page with the content returned from this method.
  ///
  /// The [snapshotData] is data that is loaded during the initialization of the
  /// pushed page as provided by the [onEditItemInit] callback. The results from
  /// thta callback will be provided to the [editItemFormFields] method as the [snapshotData].
  final Widget Function(
    BuildContext context,
    dynamic snapshotData,
    T? item,
  ) editItemFormFields;

  /// The purpose of the save button on the edit item page. If not specified,
  /// the form will default to [PositiveButtonPurpose.submitForm].
  final BiPositiveButtonPurpose? editFormSaveButtonPurpose;

  /// The text to show on the save button on the edit item page. If not specified, the save button will
  /// use text as appropriate based on the [editFormSaveButtonPurpose] value.
  /// In the case of [PositiveButtonPurpose.submitForm], it will default to "Submit".
  /// In the case of [PositiveButtonPurpose.continueWorkflow], it will default to "Continue".
  ///
  /// Specifying the [editFormSaveButtonText] will override any default behavior.
  final String? editFormSaveButtonText;

  /// Builds the title of a list item by providing the current [item]
  /// being rendered.
  final String Function(T item) titleBuilder;

  /// Builds the sub title of a list item by providing the current [item]
  /// being rendered.
  final Widget? Function(T item) subtitleBuilder;

  /// Indicates if the list item should show the delete button for the given [item].
  /// If this evaluates to false, no button will be shown. If true, then the button
  /// will be shown.
  final bool Function(T item) canDelete;

  final Stream<List<ConnectivityResult>> internetConnectionStream;

  /// Indicates if the list should show the add button at the bottom. If this evaluates to
  /// false, no button will be shown. If true, then the button will be shown.
  final bool Function() canAdd;

  /// Indicates if the a given list [item] can be edited
  final bool Function(T item) canEdit;

  const BiEditableListView({
    super.key,
    this.onItemDeleted,
    this.onItemSaved,
    this.onItemsLoaded,
    required this.load,
    this.addButtonText,
    required this.titleBuilder,
    required this.subtitleBuilder,
    required this.canDelete,
    required this.canAdd,
    this.deleteConfirmationText,
    required this.editItemFormFields,
    required this.onEditItemInit,
    required this.editItemTitle,
    this.editFormSaveButtonText,
    this.editFormSaveButtonPurpose,
    required this.internetConnectionStream,
    required this.canEdit,
  });

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use_from_same_package
    return BiEmbarkBlocProvider<EditableListCubit<T>, EditableListState<T>>(
      create: (context) => EditableListCubit<T>(),
      init: (cubit) => cubit.init(load: load),
      internetConnectivityStream: internetConnectionStream,
      child: BlocConsumer<EditableListCubit<T>, EditableListState<T>>(
        listener: (context, state) async {
          if (state.status == EditableListStatus.loaded) {
            await onItemsLoaded?.call();
          }
        },
        builder: (context, state) {
          if (state.status == EditableListStatus.loading ||
              state.status == EditableListStatus.initial) {
            return const BiFormLoading();
          }

          // If there is a parent BiForm, sync the changes.
          BiForm.maybeOf<List<ListDataChange<T>>>(context)?.sync(
            state.items,
          );

          final cubit = BlocProvider.of<EditableListCubit<T>>(context);

          // only show transportations not marked as deleted.
          final visibleItems = state.items
              .where((t) => t.operationType != DataOperationType.deleted)
              .map((t) => t.data)
              .toList();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              BiFormListView<T>(
                key: const Key('EditableListView'),
                canDelete: canDelete,
                canEdit: canEdit,
                titleBuilder: titleBuilder,
                subtitleBuilder: subtitleBuilder,
                onTap: (item) async {
                  // show the edit view to get any changes
                  final updated = await _pushPage(context, item);

                  // operation was canceled, so do nothing else.
                  if (updated == null) return;

                  cubit.update(item, updated);
                },
                onDeleteTapped: (item) async {
                  await BiConfirmationDialog.show(
                    context: context,
                    title:
                        deleteConfirmationText ?? context.l10n.requestToDelete,
                    onPosButtonTapped: () async {
                      cubit.delete(item);
                      await onItemDeleted?.call(item);
                    },
                  );
                },
                items: visibleItems,
              ),
              const Padding(
                padding: EdgeInsets.all(5),
              ),
              canAdd() == false
                  ? const SizedBox.shrink()
                  : BiEditableListAddButton(
                      key: const Key('AddButton'),
                      text: addButtonText!,
                      onTap: () async {
                        // show the edit view to get any changes
                        final added = await _pushPage(context, null);

                        // operation was canceled, so do nothing else.
                        if (added == null) return;

                        cubit.add(added);
                      },
                    ),
            ],
          );
        },
      ),
    );
  }

  Future<T?> _pushPage(BuildContext context, T? item) async {
    final page = BiFormPage<T?>(
      item: item,
      formBuilder: (context, initSnapshotData) =>
          editItemFormFields(context, initSnapshotData, item),
      init: onEditItemInit,
      onSave: onItemSaved,
      title: editItemTitle,
      saveButtonPurpose: editFormSaveButtonPurpose,
      saveButtonText: editFormSaveButtonText,
    );
    final route = MaterialPageRoute<T?>(builder: (context) => page);

    return await Navigator.of(context).push<T?>(route);
  }
}
