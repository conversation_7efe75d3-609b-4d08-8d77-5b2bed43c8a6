import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

class BiEmbarkInternetStreamListener extends StatefulWidget {
  const BiEmbarkInternetStreamListener({
    super.key,
    required this.child,
    required this.listener,
    required this.internetConnectivityStream,
  });
  final Widget child;
  final Stream<List<ConnectivityResult>> internetConnectivityStream;
  final void Function(
    BuildContext context, {
    required ConnectivityResult? current,
    required ConnectivityResult next,
  }) listener;
  @override
  State<BiEmbarkInternetStreamListener> createState() =>
      _BiEmbarkInternetStreamListenerState();
}

class _BiEmbarkInternetStreamListenerState
    extends State<BiEmbarkInternetStreamListener> {
  StreamSubscription<List<ConnectivityResult>>? _internetSubscription;
  ConnectivityResult? current;

  @override
  void initState() {
    super.initState();
    _updateInternetSubscription();
  }

  @override
  void didUpdateWidget(covariant BiEmbarkInternetStreamListener oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.internetConnectivityStream !=
        widget.internetConnectivityStream) {
      _updateInternetSubscription();
    }
  }

  void _updateInternetSubscription() {
    _internetSubscription?.cancel();
    _internetSubscription = widget.internetConnectivityStream.listen((event) {
      if (context.mounted) {
        /*
        If the build context is still valid at this point it should still be valid when listener is called.
        It'll be the responsibility of the user of this class to make sure they don't use the context across any additional async gaps in
        the function they pass to BiEmbarkInternetStreamListener.
         */
        widget.listener(
          // ignore: use_build_context_synchronously
          context,
          current: current,
          next: event.lastOrNull ?? ConnectivityResult.none,
        );
      }
      current = event.lastOrNull;
    });
  }

  @override
  void dispose() {
    _internetSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
