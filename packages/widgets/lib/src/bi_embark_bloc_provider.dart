import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:helpers/helpers.dart';
import 'package:widgets/widgets.dart';

// ignore: deprecated_member_use
class BiEmbarkBlocProvider<T extends BiEmbarkCubit<S>, S>
    extends StatefulWidget {
  final Future<void> Function(T cubit)? init;
  final T Function(BuildContext context) create;
  final Stream<List<ConnectivityResult>> internetConnectivityStream;

  final Widget child;

  /// Builds a child widget if there are no fatal errors provided by the cubit
  /// returned from the [create] method. This is a wrapper around a [BlocProvider]
  /// and follows the same API structure. The [create] method should return a
  /// [BiCubit] to register with the [BlocProvider].
  ///
  /// Where this API differs is that all [BiCubit]'s will have their init method
  /// called as soon as the [create] method runs. This follows the standard way
  /// that BI applications trigger data initialization.
  ///
  /// In order to reduce the amount of boilerplate, you can simply override
  /// the [BiCubit]'s init method and it will automatically be called by the
  /// [BiEmbarkBlocProvider] at the appropriate time.
  ///
  /// If an [init] method is provided to this widget, it will execute that method
  /// after the cubit has been created via the [create] method.
  ///
  /// The [child] is rendered when there are no fatal errors reported by the
  /// cubit created by the cubit.
  ///
  /// In the event a fatal error message is emitted, this widget will render a
  /// [BiEmbarkNoItemsView] that sets the text to be
  const BiEmbarkBlocProvider({
    super.key,
    this.init,
    required this.create,
    required this.child,
    required this.internetConnectivityStream,
  });

  @override
  State<BiEmbarkBlocProvider<T, S>> createState() =>
      _BiEmbarkBlocProviderState<T, S>();
}

// ignore: deprecated_member_use
class _BiEmbarkBlocProviderState<T extends BiEmbarkCubit<S>, S>
    extends State<BiEmbarkBlocProvider<T, S>> {
  Future<void>? loading;
  @override
  Widget build(BuildContext context) {
    return BlocProvider<T>(
      create: (context) {
        final cubit = widget.create(context);
        _tryInit(cubit);
        return cubit;
      },
      child: Builder(
        builder: (context) {
          final cubit = BlocProvider.of<T>(context);

          return StreamBuilder<String?>(
            stream: cubit.fatalErrorMessageStream,
            builder: (context, status) {
              final l10n = AppLocalizations.of(context);

              Widget viewToShow = widget.child;
              if (status.data != null) {
                // error, render no items

                viewToShow = BiEmbarkNoItemsView(
                  buttonText: l10n.refresh,
                  text: status.data,
                  onButtonPressed: () {
                    cubit.reset();
                    _tryInit(cubit);
                  },
                );
              }

              return BiStreamListener(
                stream: widget.internetConnectivityStream,
                listener: (connectionChecker) async {
                  if (connectionChecker.lastOrNull != ConnectivityResult.none) {
                    await _tryInit(cubit);
                  }
                },
                child: viewToShow,
              );
            },
          );
        },
      ),
    );
  }

  Future<void> _tryInit(T cubit) async {
    if (widget.init == null || loading != null) {
      return;
    }

    try {
      // poor man's mutex
      loading = widget.init!(cubit);
      // always do cubit init
      await loading;
    } catch (e, stack) {
      // some error was raised during init
      cubit.setFatalError(e.toString(), stack);
    } finally {
      loading = null;
    }
  }
}
