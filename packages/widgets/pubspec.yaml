name: widgets
description: BI widgets
version: 0.0.2
homepage: www.bi.com
publish_to: none

environment:
  sdk: ">=3.1.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  bi_flutter_widgets:
    hosted: https://dart.pkg.jetbrains.space/bidevelopment/p/flutter-dev/dart/
    version: ^12.0.0

  bi_flutter_font_awesome_pro:
    hosted: https://dart.pkg.jetbrains.space/bidevelopment/p/flutter-dev/dart
    version: ^3.0.0

  bi_embark_l10n:
    path: ../bi_embark_l10n

  bi_flutter_smlk_api:
    hosted: https://dart.pkg.jetbrains.space/bidevelopment/p/flutter-dev/dart
    version: ^15.0.0

  bi_flutter_bloc:
    hosted: https://dart.pkg.jetbrains.space/bidevelopment/p/flutter-dev/dart
    version: ^1.0.0

  flutter_svg: ^2.0.0
  flutter_html: ^3.0.0-beta.2
  flutter_bloc: any
  helpers:
    path: ../helpers
  freezed_annotation: ^2.4.1
  equatable: ^2.0.3
  collection: ^1.17.2
  url_launcher: ^6.1.14
  phone_numbers_parser: ^9.0.3
  url_launcher_platform_interface: ^2.2.0
  connectivity_plus: ^6.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  bi_flutter_lints:
    hosted: https://dart.pkg.jetbrains.space/bidevelopment/p/flutter-dev/dart/
    version: ^5.0.0
  freezed: ^2.4.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  uses-material-design: true

# To add assets to your package, add an assets section, like this:
# assets:
#   - assets/graphics/

#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware.

# To add custom fonts to your package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages
