name: twilio_voice
description: Provides an interface to <PERSON><PERSON><PERSON>'s Programmable Voice SDK to allow adding voice-over-IP (VoIP) calling into your Flutter applications.
version: 0.1.3
homepage: https://github.com/cybex-dev/twilio_voice

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: ">=1.20.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  plugin_platform_interface: ^2.1.4
  js: ^0.6.4

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.twilio.twilio_voice
        pluginClass: TwilioVoicePlugin
      ios:
        pluginClass: TwilioVoicePlugin
      web:
        # As a temporary measure, the web implementation of a plugin is defined in
        # `twilio_voice_web`, but this will change soon with the migration to a federated
        # plugin structure.
        pluginClass: TwilioVoiceWeb
        fileName: _internal/twilio_voice_web.dart
      macos:
        pluginClass: TwilioVoicePlugin

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
