group 'com.twilio.twilio_voice'
version '1.0'

buildscript {
    ext.kotlin_version = '1.8.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'org.jetbrains.kotlin.android'

android {
    namespace 'com.twilio.twilio_voice'
    compileSdk 36

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        minSdkVersion 26
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation(platform("org.jetbrains.kotlin:kotlin-bom:$kotlin_version"))
    implementation(platform("com.google.firebase:firebase-bom:32.2.2"))
    implementation("com.twilio:voice-android:6.9.0")
    implementation("com.google.firebase:firebase-messaging-ktx:23.2.1")
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.lifecycle:lifecycle-process:2.6.1'
    implementation 'androidx.core:core-ktx:1.13.1'
    runtimeOnly("androidx.lifecycle:lifecycle-extensions:2.2.0")
    runtimeOnly("androidx.constraintlayout:constraintlayout:2.1.4")
}

