import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_flutter_bluetooth_platform_api/plugin/plugin.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/repositories/_background_isolate_starter_repository.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/smart_band/smart_band.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';
import '../unit_test_helpers/fakes/fake_bi_bluetooth_api.dart';
import '../unit_test_helpers/fakes/fake_service_instance.dart';
import '../unit_test_helpers/fakes/fake_tracking_repository.dart';

void main() {
  group('DashboardCubit', () {
    late DashboardCubit cubit;
    late MockAppRepository appRepository;
    late BiStreamController<Locale> localeChanges;
    late BiStreamController<GetClientSettingsResponse> clientSettingsChanges;
    late BiStreamController<BluetoothBackendEvent> smartBandConnectionUpdates;
    late MockEmbarkAuthenticationRepository authenticationRepository;
    late MockClientSettingsRepository clientSettingsRepository;
    late MockAppSettingsRepository appSettingsRepository;
    late MockDeviceSetupRepository deviceSetupRepository;
    late MockSharedPreferencesService sharedPreferencesService;
    late MockSmartBandPreferences smartBandPreferences;
    const mdmSkipPinValidationKey = 'mdm_skip_pin_validation';
    late BiStreamController<AppLifecycleState> appLifecycleStateChanges;
    late MockBiTokenManager biTokenManager;
    late MockBiLogger logger;
    DateTime? staticNowTime;
    late CheckinTimestampRepository timestampRepository;
    late FakeBiBluetoothApi bluetoothApi;
    late SmartBandUiRepository smartBandUiRepository;
    late MockFlutterSecureStorage flutterSecureStorage;
    late FakeTrackingRepository trackingSetup;
    late MockBluetoothFrontend bluetoothFrontend;
    late MockSmartLinkFirmwareInfoHttpClient
        mockSmartLinkFirmwareInfoHttpClient;
    late FakeFlutterBackgroundService serviceInstance;

    const clientSettings = GetClientSettingsResponse(
      preferredLanguage: PreferredLanguageResponse(
        cultureCode: 'en',
        cultureName: 'name',
        languageCodeId: 1,
        languageId: 1,
      ),
      plugins: [
        PluginResponse(type: 1, icon: '', title: ''),
        PluginResponse(type: 2, icon: '', title: ''),
        PluginResponse(type: 3, icon: '', title: ''),
      ],
    );

    void changeLanugage(String locale) {
      assert(locale == 'en' || locale == 'es');
      localeChanges.add(Locale(locale));
    }

    void updateClientSettings({
      GetClientSettingsResponse newSettings = clientSettings,
    }) {
      clientSettingsChanges.add(newSettings);
    }

    void grantBluetoothPermissions(bool granted) {
      final status = granted
          ? BluetoothPermissionStatus.granted
          : BluetoothPermissionStatus.denied;

      bluetoothApi.setPermissionStatus(status);
      smartBandUiRepository.permissionStatus.value = status;
    }

    setUp(() async {
      // use sync streams to make testing easier.
      clientSettingsChanges =
          BiStreamController<GetClientSettingsResponse>.broadcast(sync: true);
      smartBandConnectionUpdates =
          BiStreamController<BluetoothBackendEvent>.broadcast(sync: true);
      localeChanges = BiStreamController<Locale>(sync: true);
      smartBandPreferences = MockSmartBandPreferences();
      appRepository = MockAppRepository();
      authenticationRepository = MockEmbarkAuthenticationRepository();
      clientSettingsRepository = MockClientSettingsRepository();
      appSettingsRepository = MockAppSettingsRepository();
      logger = MockBiLogger();
      deviceSetupRepository = MockDeviceSetupRepository();
      sharedPreferencesService = MockSharedPreferencesService();
      appLifecycleStateChanges =
          BiStreamController<AppLifecycleState>(sync: true);
      biTokenManager = MockBiTokenManager();
      timestampRepository = CheckinTimestampRepository(
        sharedPreferencesService: sharedPreferencesService,
      );
      flutterSecureStorage = MockFlutterSecureStorage();
      trackingSetup = FakeTrackingRepository();
      bluetoothFrontend = MockBluetoothFrontend();
      bluetoothApi = FakeBiBluetoothApi.init();
      mockSmartLinkFirmwareInfoHttpClient =
          MockSmartLinkFirmwareInfoHttpClient();

      serviceInstance = FakeFlutterBackgroundService();

      when(appRepository.localeStream)
          .thenAnswer((realInvocation) => localeChanges.stream);

      when(clientSettingsRepository.clientSettings)
          .thenAnswer((realInvocation) => clientSettingsChanges.stream);
      when(bluetoothFrontend.events).thenAnswer(
        (realInvocation) =>
            BluetoothFrontendEvents(smartBandConnectionUpdates.stream),
      );

      when(appSettingsRepository.storeAndSetAppLocaleIfChanged(any))
          .thenAnswer((realInvocation) async => null);

      when(deviceSetupRepository.lastKnownDeviceType)
          .thenReturn(ResolvedDeviceType.unknown);

      when(appRepository.appLifecycleStateStream)
          .thenAnswer((realInvocation) => appLifecycleStateChanges.stream);

      when(biTokenManager.isTokenExpired()).thenAnswer((_) async => false);

      getIt.registerSingleton<DeviceSetupRepository>(deviceSetupRepository);
      smartBandUiRepository = SmartBandUiRepository(
        sharedPreferencesService: smartBandPreferences,
        bluetoothApi: bluetoothApi,
        frontend: bluetoothFrontend,
        sensorEventInfoRepository: MockSensorEventInfoRepository(),
        smartLinkFirmwareInfoHttpClient: mockSmartLinkFirmwareInfoHttpClient,
        backgroundIsolateStarterRepository: BackgroundIsolateStarterRepository(
          backgroundService: serviceInstance,
        ),
        permissionRequestRepository: BiPermissionRequestRepository(
          bluetoothApi: bluetoothApi,
        ),
      );
      cubit = DashboardCubit(
        appRepository,
        authenticationRepository,
        clientSettingsRepository,
        appSettingsRepository,
        smartBandPreferences,
        logger,
        timestampRepository,
        smartBandUiRepository,
        flutterSecureStorage,
        trackingSetup,
        serviceInstance,
        getNow: () => staticNowTime ?? DateTime.now(),
      );
      await smartBandUiRepository.initialize();
    });

    tearDown(() async {
      staticNowTime = null;
      await localeChanges.close();
      await clientSettingsChanges.close();
      await smartBandConnectionUpdates.close();
      await appLifecycleStateChanges.close();
      await getIt.reset();
    });
    void setAdHocCheckin(List<DateTime> times) => when(
          sharedPreferencesService
              .getStringList(CheckinTimestampRepository.adHocCheckInsListKey),
        ).thenReturn(
          times.map((e) => e.toIso8601String()).toList(),
        );

    void setAdHocCheckinCount(int count) {
      setAdHocCheckin(List.generate(count, (index) => DateTime.now()));
    }

    void setFacialLoginEnabled() {
      final clientSettings = cubit.state.clientSettings ??
          const GetClientSettingsResponse(
            preferredLanguage: PreferredLanguageResponse(
              cultureCode: '',
              cultureName: 'cultureName',
              languageCodeId: 1,
              languageId: 1,
            ),
          );
      cubit.emit(
        cubit.state.copyWith(
          clientSettings: (clientSettings).copyWith(enrolledFacial: true),
        ),
      );
    }

    void withSmartBandEnabled(bool enabled) {
      const serialNumber = '123456';
      final plugin = PluginResponse(
        type: PluginType.smartband.value,
        extraInfo: {'serialNumber': serialNumber},
        title: 'SmartBand',
      );

      final updatedClientSettings = clientSettings.copyWith(
        plugins: [plugin],
      );

      cubit.emit(cubit.state.copyWith(clientSettings: updatedClientSettings));
    }

    test('starts with correct state', () {
      expect(cubit.state, equals(const DashboardState()));
    });

    test('emits correctly when client settings load', () async {
      // start listening for client settings changes.
      await cubit.init();

      // start listening prior to updating the settings
      expect(
        cubit.stream,
        emits(
          DashboardState(
            plugins: clientSettings.pluginMap,
            clientSettings: clientSettings,
            status: DashboardStatus.loaded,
          ),
        ),
      );

      updateClientSettings();
    });
    test(
        'make icon actionable when client settings has a null tracking icon and trackingStatus is not ready',
        () async {
      // start listening for client settings changes.
      final plugins = [
        const PluginResponse(type: 9, icon: null, title: ''),
      ];
      final newClientSettings = clientSettings.copyWith(
        plugins: plugins,
      );
      await cubit.init();

      updateClientSettings(newSettings: newClientSettings);
      await pumpEventQueue();

      final isTrackingActionable =
          cubit.state.plugins[PluginType.tracking]?.icon == '_action';
      expect(isTrackingActionable, true);
    });

    test('tries to sync client settings preferred language', () async {
      // start listening for client settings changes.
      await cubit.init();

      updateClientSettings();
      await pumpEventQueue();

      verify(
        appSettingsRepository.storeAndSetAppLocaleIfChanged(
          clientSettings.preferredLanguage.languageCodeId,
        ),
      ).called(1);
    });

    test('logs error message on client settings error', () async {
      // start listening for client settings changes.
      await cubit.init();

      final error = Error();
      clientSettingsChanges.addError(error);

      verify(logger.error(error: error)).called(1);
    });

    test('resets l10n correctly on client settings error', () async {
      // start listening for client settings changes.
      await cubit.init();

      changeLanugage('es');

      final error = Error();
      clientSettingsChanges.addError(error);

      verify(logger.error(error: error)).called(1);
    });

    test('logs out correctly', () async {
      await cubit.logout();
      verify(authenticationRepository.logOut()).called(1);
    });

    //  App is starting
    test('do not ask for pin validation if pin was just set', () async {
      when(deviceSetupRepository.lastKnownDeviceType)
          .thenReturn(ResolvedDeviceType.mdm);
      when(
        sharedPreferencesService.getBool(
          mdmSkipPinValidationKey,
          true,
        ),
      ).thenReturn(true);

      final pinSetClientSettings = clientSettings.copyWith(
        pinSet: true,
      );
      updateClientSettings(
        newSettings: pinSetClientSettings,
      );

      // start listening for client settings changes.
      await cubit.init();

      verifyNever(
        sharedPreferencesService.setBool(
          any,
          false,
        ),
      );
    });

    test('do not ask for pin validation if non mdm device', () async {
      when(deviceSetupRepository.lastKnownDeviceType)
          .thenReturn(ResolvedDeviceType.byod);
      when(
        sharedPreferencesService.getBool(
          mdmSkipPinValidationKey,
          false,
        ),
      ).thenReturn(true);

      final pinSetClientSettings = clientSettings.copyWith(
        pinSet: true,
      );
      updateClientSettings(
        newSettings: pinSetClientSettings,
      );

      // start listening for client settings changes.
      await cubit.init();

      verifyNever(
        sharedPreferencesService.setBool(
          any,
          false,
        ),
      );
    });

    test('do not ask for pin validation if mdm device and pin was not just set',
        () async {
      when(deviceSetupRepository.lastKnownDeviceType)
          .thenReturn(ResolvedDeviceType.mdm);
      when(
        sharedPreferencesService.getBool(
          mdmSkipPinValidationKey,
          false,
        ),
      ).thenReturn(false);

      final pinSetClientSettings = clientSettings.copyWith(
        pinSet: false,
      );
      updateClientSettings(
        newSettings: pinSetClientSettings,
      );

      // start listening for client settings changes.
      await cubit.init();

      verifyNever(
        sharedPreferencesService.setBool(
          any,
          false,
        ),
      );
    });

    test('resubscribe to client settings when app life cycle in resumed state ',
        () async {
      // change the app state to resumed
      appLifecycleStateChanges.add(AppLifecycleState.resumed);
      final plugins = [
        const PluginResponse(type: 1, icon: '', title: ''),
        const PluginResponse(type: 2, icon: '', title: ''),
        const PluginResponse(type: 3, icon: '', title: ''),
        const PluginResponse(type: 4, icon: '', title: ''),
        const PluginResponse(type: 5, icon: '', title: ''),
      ];
      final newClientSettings = clientSettings.copyWith(
        plugins: plugins,
      );
      updateClientSettings(newSettings: newClientSettings);

      await pumpEventQueue();
      expect(cubit.state.plugins.length, 5);
    });

    test('resetTabToBeShown resets state correctly', () async {
      cubit.emit(const DashboardState(tabToBeShown: PluginType.calendar));
      cubit.resetTabToBeShown();
      expect(cubit.state.tabToBeShown, null);
    });
    group('check in', () {
      setUp(() {
        setFacialLoginEnabled();
      });
      test('allow check in when now checkins', () async {
        setAdHocCheckinCount(0);
        await cubit.checkIn();
        expect(cubit.state.dashboardCheckIn.viewing, true);
      });

      test('shows when next check in is available', () async {
        staticNowTime = DateTime(2010, 1, 1, 9);

        setAdHocCheckin([
          DateTime(2010, 1, 1, 8, 20),
          DateTime(2010, 1, 1, 8, 30),
          DateTime(2010, 1, 1, 8, 40),
          DateTime(2010, 1, 1, 8, 50),
        ]);
        await cubit.checkIn();

        expect(
          cubit.state.dashboardCheckIn,
          isNot(const DashboardCheckIn.initial()),
        );
        expect(
          cubit.state.dashboardCheckIn.nextAllowedCheckin,
          DateTime(2010, 1, 1, 9, 20),
        );
      });
      test(
          'will clear old checkin and allow to checkin once 1 hour has passed from first checkin',
          () async {
        staticNowTime = DateTime(2010, 1, 1, 9);

        setAdHocCheckin([
          DateTime(2010, 1, 1, 7, 20),
          DateTime(2010, 1, 1, 8, 30),
          DateTime(2010, 1, 1, 8, 40),
          DateTime(2010, 1, 1, 8, 50),
        ]);
        await cubit.checkIn();
        expect(cubit.state.dashboardCheckIn.viewing, isTrue);
        verify(
          sharedPreferencesService.setStringList(
            CheckinTimestampRepository.adHocCheckInsListKey,
            [],
          ),
        ).called(1);
      });
      test(
          'user can see check in when user has performed 1 check-in in the last hour',
          () async {
        setAdHocCheckinCount(1);

        await cubit.checkIn();
        expect(cubit.state.dashboardCheckIn.viewing, true);
      });
      test(
          'user can not see check in when user has checked-in 2 times in the last hour',
          () async {
        setAdHocCheckinCount(2);

        await cubit.checkIn();
        expect(cubit.state.dashboardCheckIn.viewing, false);
      });

      test(
          'when client settings contains check in value, they can still check in even if multiple checkins',
          () async {
        await cubit.init();
        final checkInPlugin = PluginResponse(
          title: '',
          icon: 'iconCheckin_action',
          type: PluginType.checkInFacial.value,
        );

        final updatedClientSettings = clientSettings.copyWith(
          plugins: [checkInPlugin],
          enrolledFacial: true,
        );
        updateClientSettings(newSettings: updatedClientSettings);
        // allow settings to propogate the stream
        await Future.delayed(const Duration(milliseconds: 100), () {});
        setAdHocCheckinCount(3);
        await cubit.checkIn();
        verifyNever(
          sharedPreferencesService.setStringList(
            CheckinTimestampRepository.adHocCheckInsListKey,
            [],
          ),
        );
        expect(cubit.state.dashboardCheckIn.viewing, true);
      });

      test(
          'when client settings contains check in value that is not actionable and have checked in 2 times in the last hour, user cannot check in',
          () async {
        await cubit.init();
        final checkInPlugin = PluginResponse(
          title: '',
          icon: 'iconCheckin',
          type: PluginType.checkInFacial.value,
        );

        final updatedClientSettings = clientSettings.copyWith(
          plugins: [checkInPlugin],
          enrolledFacial: true,
        );
        updateClientSettings(newSettings: updatedClientSettings);
        // allow settings to propogate the stream
        await Future.delayed(const Duration(milliseconds: 100), () {});
        setAdHocCheckinCount(2);
        await cubit.checkIn();
        expect(cubit.state.dashboardCheckIn.viewing, false);
      });

      test(
          'when client settings contains no check in value and client has checked in twice in the last hour, user cannot checkin',
          () async {
        await cubit.init();
        final checkInPlugin = PluginResponse(
          title: '',
          icon: 'icon',
          type: PluginType.calendar.value,
        );

        final updatedClientSettings = clientSettings.copyWith(
          plugins: [checkInPlugin],
          enrolledFacial: true,
        );
        updateClientSettings(newSettings: updatedClientSettings);
        // allow settings to propogate the stream
        await Future.delayed(const Duration(milliseconds: 100), () {});
        setAdHocCheckinCount(2);
        await cubit.checkIn();
        expect(cubit.state.dashboardCheckIn.viewing, false);
      });
    });

    test('does not connect to backend if no permission', () async {
      withSmartBandEnabled(true);

      await cubit.tryStartSmartBandService();

      verifyNever(bluetoothFrontend.connectToBackend());
    });

    test('DashboardCubit tries to connect to smartband if necessary', () async {
      const serialNumber = '3100199';

      bluetoothApi.setSensorStatus(BluetoothAdapterState.on);
      withSmartBandEnabled(true);
      grantBluetoothPermissions(true);

      await cubit.init();

      // shouldn't be checked until we have updated client settings
      verifyNever(bluetoothFrontend.connectToBackend());
      verifyNever(
        bluetoothFrontend.scanAndConnect(
          serialNumber: anyNamed('serialNumber'),
          timeout: anyNamed('timeout'),
        ),
      );

      when(bluetoothFrontend.isConnectedToDevice())
          .thenAnswer((_) async => false);

      final plugin = PluginResponse(
        type: PluginType.smartband.value,
        extraInfo: {'serialNumber': serialNumber},
        title: 'SmartBand',
      );

      final updatedClientSettings = clientSettings.copyWith(
        plugins: [plugin],
      );

      updateClientSettings(newSettings: updatedClientSettings);

      await pumpEventQueue();

      // Updated sn is stored in both shared prefs and service instance
      verify(smartBandPreferences.setSbSerial(serialNumber)).called(1);
      assert(
        serviceInstance.invokeCalled
                .singleWhere(
                  (e) =>
                      e.$1 ==
                      SharedPreferencesKeyUpdateIsolateMethod.serial.value,
                )
                .$2?['serial'] ==
            serialNumber,
      );
      verify(bluetoothFrontend.connectToBackend()).called(1);

      verify(
        bluetoothFrontend.scanAndConnect(
          serialNumber: serialNumber,
          scanTimeout: const Duration(seconds: 23),
          timeout: const Duration(minutes: 5),
        ),
      ).called(1);
    });

    test('when sb has been unassigned, disconnects from the backend', () async {
      await cubit.tryStartSmartBandService();

      verify(bluetoothFrontend.disconnect()).called(1);
    });
  });
}
