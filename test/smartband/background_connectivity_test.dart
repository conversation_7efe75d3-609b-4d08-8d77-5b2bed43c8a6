import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/_smartlink_background_service_entrypoint.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late BackgroundInternetConnectivity connectivity;
  setUp(() {
    connectivity = BackgroundInternetConnectivity();
  });
  test('returns mobile for everything', () async {
    expect(
      await connectivity.connectionStream.first,
      [ConnectivityResult.mobile],
    );
    expect(await connectivity.reachability, [ConnectivityResult.mobile]);
  });
  test('closing', () async {
    expect(connectivity.isClosed, isFalse);
    connectivity.close();
  });
  test('can set stream', () async {
    connectivity.connectionStream =
        Stream.value([ConnectivityResult.bluetooth]);
    expect(
      await connectivity.connectionStream.first,
      [ConnectivityResult.bluetooth],
    );
  });
}
