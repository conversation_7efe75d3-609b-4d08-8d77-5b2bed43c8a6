import 'dart:async';
import 'dart:io';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/check_in/blink_detector.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/facial_enrollment/bloc/bloc.dart';
import 'package:embark/facial_enrollment/views/views.dart';
import 'package:embark/facial_enrollment/widgets/widgets.dart';
import 'package:embark/repositories/_device_setup_respository.dart';

import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:embark/ui_helpers/ui_lookup_keys.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:bi_flutter_bloc/src/bi_stream_controller/_bi_stream_controller.dart';

import 'package:mockito/mockito.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/image_compression.dart';
import '../../unit_test_helpers/stubs/auth_manager.dart';
import '../../widgets/documents/add_documents_screen_test.dart';
import '../../widgets/widget_test_helpers/golden_wrapper.dart';

class FakeCameraUtil extends Mock implements CameraUtil {
  @override
  Future<List<String>> listDeniedPermissions() async {
    return Future.value(['Location']);
  }
}

void main() {
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late FacialEnrollmentCubit bloc;
  late AppRepository appRepository;
  late BiometricRepository biometricRepository;
  late LocationRepository locationRepository;
  late MockPermissionHandler permissionHandler;
  late FaceDetector faceDetector;
  late BiStreamController<BannerMessage> bannerMessages;
  late FakeImageCompression imageCompression;
  late FaceDetectionUtil faceDetectionUtil;
  late ClientSettingsRepository clientSettingsRepository;
  late BlinkDetector blinkDetector;
  late CameraUtil cameraUtil;
  late DeviceSetupRepository deviceSetupRepository;
  late SharedPreferencesService sharedPreferencesService;
  late MockFaceDetectionService faceDetectionService;
  late MockBiLogger logger;

  group('FacialEnrollmentPage', () {
    Future<void> loadPage(WidgetTester tester) async {
      await tester.load(
        widget: Material(
          child: BlocProvider<FacialEnrollmentCubit>(
            create: (context) => bloc..setUp(),
            child: const FacialEnrollmentView(),
          ),
        ),
      );
    }

    setUp(() {
      faceDetectionService = MockFaceDetectionService();
      iCSController = BiStreamController.broadcast(sync: true);
      appRepository = MockAppRepository();
      biometricRepository = MockBiometricRepository();
      locationRepository = MockLocationRepository();
      permissionHandler = MockPermissionHandler();
      faceDetector = MockFaceDetector();
      bannerMessages = BiStreamController();
      imageCompression = FakeImageCompression();
      faceDetectionUtil = MockFaceDetectionUtil();
      clientSettingsRepository = MockClientSettingsRepository();
      blinkDetector = BlinkDetector(
        faceDetectionService,
      );
      cameraUtil = MockCameraUtil();
      deviceSetupRepository = MockDeviceSetupRepository();
      sharedPreferencesService = MockSharedPreferencesService();
      logger = MockBiLogger();

      getIt.registerFactory<Sink<BannerMessage>>(
        () => appRepository.bannerMessageSink,
      );
      getIt.registerFactory<FaceDetector>(
        () => faceDetector,
      );
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<BiometricRepository>(
        biometricRepository,
      );
      getIt.registerSingleton<LocationRepository>(
        locationRepository,
      );
      // ignore: deprecated_member_use_from_same_package
      getIt.registerSingleton<PermissionHandler>(
        permissionHandler,
      );
      getIt.registerSingleton<ImageCompression>(
        imageCompression,
      );
      getIt.registerSingleton<ClientSettingsRepository>(
        clientSettingsRepository,
      );
      getIt.registerSingleton<BlinkDetector>(
        blinkDetector,
      );
      getIt.registerSingleton<CameraUtil>(
        cameraUtil,
      );
      getIt.registerSingleton<DeviceSetupRepository>(
        deviceSetupRepository,
      );
      getIt.registerSingleton<SharedPreferencesService>(
        sharedPreferencesService,
      );
      getIt.registerSingleton(
        AuthManagerTestHelper.loggedInManager(
          authenticationRepository: MockEmbarkAuthenticationRepository(),
        ),
      );
      getIt.registerSingleton(
        CheckinTimestampRepository(
          sharedPreferencesService: sharedPreferencesService,
        ),
      );
      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );

      faceDetectionUtil = MockFaceDetectionUtil();
      getIt.registerFactory<FaceDetectionUtil>(
        () => faceDetectionUtil,
      );

      getIt.registerSingleton<BiLogger>(logger);
      bloc = FacialEnrollmentCubit(
        compression: imageCompression,
        appRepository: appRepository,
        biometricRepository: biometricRepository,
        locationRepository: locationRepository,
        permissionHandler: permissionHandler,
        authManager: AuthManagerTestHelper.loggedInManager(
          authenticationRepository: MockEmbarkAuthenticationRepository(),
        ),
        faceDetector: faceDetector,
        clientSettingsRepository: clientSettingsRepository,
        uploadWaitTime: const Duration(milliseconds: 200),
        faceDetectionUtil: faceDetectionUtil,
        logger: logger,
      )..setUp();

      bloc.cameraUtil = MockCameraUtil();

      when(appRepository.bannerMessageStream)
          .thenAnswer((realInvocation) => bannerMessages.stream);
      when(appRepository.bannerMessageSink)
          .thenAnswer((realInvocation) => bannerMessages.sink);
      when(appRepository.getStoredLocale())
          .thenAnswer((realInvocation) => const Locale('en'));
    });

    tearDown(() async {
      await iCSController.close();
      await getIt.reset();
      await bannerMessages.close();
    });

    testGoldens(
      'Enrollment looks correct',
      (tester) async {
        await tester.pumpWidgetBuilder(
          const FacialEnrollmentPage(),
          wrapper: goldenWrapper,
        );
        await screenMatchesGolden(tester, 'facial_enrollment');
      },
      skip: !Platform.isMacOS,
    );

    testWidgets('shows enrollment guidelines', (tester) async {
      await tester.load(widget: const FacialEnrollmentPage());
      await tester.pump();
      expect(find.byType(ValidCheckItem), findsNWidgets(11));
      expect(find.byKey(UiLookupKeys.facialEnrollmentHomeKey), findsOne);
    });

    testWidgets('shows camera view when state is cameraOpen', (tester) async {
      await loadPage(tester);
      bloc.emit(
        bloc.state.copyWith(
          status: FacialEnrollmentStatus.cameraOpen,
          validImages: [],
          screenSize: 1000,
          cameraController: CameraController(
            description,
            ResolutionPreset.high,
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.byKey(UiLookupKeys.facialEnrollmentCameraOpen), findsOne);
    });

    testWidgets('Ensure PopScope has canPop: false', (tester) async {
      await loadPage(tester);

      final popScopeFinder = find.byType(PopScope);

      expect(popScopeFinder, findsOneWidget);

      final popScopeWidget = tester.widget<PopScope>(popScopeFinder);

      expect(popScopeWidget.canPop, isFalse);
    });

    testWidgets(
        'shows permission dialog when missing permissions on continue pressed',
        (tester) async {
      when(permissionHandler.requestCameraPermissions(any))
          .thenAnswer((realInvocation) => Future.value(['Location']));

      await loadPage(tester);
      await tester.pump();

      final cameraButton = find.byKey(UiLookupKeys.launchCameraButton);

      await tester.tap(cameraButton);

      await tester.pumpAndSettle();

      expect(find.byKey(UiLookupKeys.permissionsDialog), findsOne);
    });

    testWidgets('shows location warning pop up when location not granted',
        (tester) async {
      await loadPage(tester);

      await tester.pump();
      bloc.emit(
        bloc.state.copyWith(
          deniedPermissions: ['Location'],
          status: FacialEnrollmentStatus.warnLocation,
        ),
      );
      await tester.pumpAndSettle();

      expect(find.byKey(UiLookupKeys.locationPopUp), findsOne);
    });

    testWidgets(
        'shows missing permissions pop up when a permission not granted',
        (tester) async {
      await loadPage(tester);

      await tester.pump();
      bloc.emit(
        bloc.state.copyWith(
          deniedPermissions: ['Location'],
          status: FacialEnrollmentStatus.rejectedPermissions,
        ),
      );
      await tester.pumpAndSettle();

      expect(
        find.byKey(UiLookupKeys.permissionsDialog),
        findsOne,
      );
    });
    testWidgets('does not show back button', (tester) async {
      await loadPage(tester);

      await tester.pumpAndSettle();

      final landingView =
          tester.widget(find.byType(EnrollAndCheckinLandingView));

      expect(
        landingView,
        isA<EnrollAndCheckinLandingView>().having(
          (p0) => p0.shouldShowBackButton,
          'Should not show back button',
          false,
        ),
      );
    });

    testWidgets(
        'stays on check in landing if camera permissions denied after pop up dismissed',
        (tester) async {
      await loadPage(tester);
      when(bloc.cameraUtil.listDeniedPermissions())
          .thenAnswer((realInvocation) => Future.value(['Camera']));
      when(permissionHandler.requestCameraPermissions(any))
          .thenAnswer((realInvocation) async => ['Camera']);

      bloc.emit(
        bloc.state.copyWith(
          status: FacialEnrollmentStatus.initial,
          deniedPermissions: ['Camera'],
        ),
      );
      bloc.emit(
        bloc.state.copyWith(
          status: FacialEnrollmentStatus.rejectedPermissions,
          deniedPermissions: ['Camera'],
        ),
      );

      await tester.pumpAndSettle();
      final permissionPopUpCloseButton = find.text('Cancel');

      await tester.ensureVisible(permissionPopUpCloseButton);
      await tester.tap(permissionPopUpCloseButton, warnIfMissed: true);

      await tester.pumpAndSettle();
      expect(find.byKey(UiLookupKeys.facialEnrollmentHomeKey), findsOne);
    });
  });
}
