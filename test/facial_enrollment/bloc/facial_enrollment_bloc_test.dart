import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:async/async.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:camera/camera.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/facial_enrollment/bloc/_facial_enrollment_cubit.dart';
import 'package:embark/facial_enrollment/bloc/_facial_enrollment_state.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/services.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:flutter_test/flutter_test.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:mockito/mockito.dart';

import '../../check_in/check_in_cubit_setup.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/data/client_settings.dart';
import '../../unit_test_helpers/fakes/camera_controller.dart';
import '../../unit_test_helpers/stubs/auth_manager.dart';

class FakeCameraValue extends CameraValue {
  FakeCameraValue({
    super.isInitialized = true,
    super.isRecordingVideo = false,
    super.isTakingPicture = false,
    super.isStreamingImages = false,
    super.isRecordingPaused = true,
    super.flashMode = FlashMode.auto,
    super.exposureMode = ExposureMode.auto,
    super.exposurePointSupported = false,
    super.focusMode = FocusMode.auto,
    super.focusPointSupported = false,
    super.deviceOrientation = DeviceOrientation.portraitUp,
    super.isPreviewPaused = true,
    super.description = const CameraDescription(
      lensDirection: CameraLensDirection.front,
      name: 'fake',
      sensorOrientation: 0,
    ),
    this.aspectRatio = 1.0,
  });

  @override
  final double aspectRatio;
}

class FakePermissionHandler extends MockPermissionHandler {
  @override
  Future<List<String>> checkCameraPermissions(AppLocalizations? l10n) async {
    return Future.value([]);
  }
}

class FakePermissionHandlerWithDenied extends MockPermissionHandler {
  @override
  Future<List<String>> checkCameraPermissions(AppLocalizations? l10n) async {
    return Future.value(['Location']);
  }

  @override
  Future<List<String>> requestCameraPermissions(AppLocalizations? l10n) {
    return Future.value(['Camera']);
  }
}

void main() {
  late MockBiometricRepository biometricRepository;
  late LocationRepository locationRepository;
  late FacialEnrollmentRequest request;
  late FaceDetector faceDetector;
  late MockPermissionHandler permissionHandler;
  late AppRepository appRepository;
  final goodResponse = Future.value(ApiResponse.ok());
  late MockImageCompression imageCompression;
  late FakeFaceDetectionUtil faceDetectionUtil;
  late ClientSettingsRepository clientSettingsRepository;
  // ignore: deprecated_member_use_from_same_package
  late PermissionHandler permissionsDeniedHandler;
  late MockEmbarkAuthenticationRepository authenticationRepository;
  late AuthManager authManager;
  late AuthHandler authHandler;
  late MockBiLogger logger;
  final AppLocalizationsEn l10n = AppLocalizationsEn();
  late Future<List<CameraDescription>> Function() availableCameras;
  setUp(() async {
    biometricRepository = MockBiometricRepository();
    locationRepository = MockLocationRepository();
    biometricRepository = MockBiometricRepository();
    faceDetector = MockFaceDetector();
    permissionHandler = FakePermissionHandler();
    permissionsDeniedHandler = FakePermissionHandlerWithDenied();
    appRepository = MockAppRepository();
    clientSettingsRepository = MockClientSettingsRepository();
    logger = MockBiLogger();
    request = const FacialEnrollmentRequest(
      fixType: 1,
      latitude: 1,
      longitude: 1,
      locationAcquired: true,
      locationServicesEnabled: true,
      photos: [],
    );
    authHandler =
        AuthHandler(defaultAuthSteps: [], runBeforeOtherAuthSteps: []);
    authenticationRepository = MockEmbarkAuthenticationRepository();
    imageCompression = MockImageCompression();
    faceDetectionUtil = FakeFaceDetectionUtil();
    authManager = AuthManagerTestHelper.loggedInManager(
      authenticationRepository: authenticationRepository,
      authHandler: authHandler,
    );
    availableCameras = () => Future.value([
      const CameraDescription(
        lensDirection: CameraLensDirection.front,
        name: 'fake',
        sensorOrientation: 0,
      ),
      const CameraDescription(
        lensDirection: CameraLensDirection.back,
        name: 'fake',
        sensorOrientation: 0,
      ),
    ]);

    /// set up path_provider
    final directory = await Directory.systemTemp.createTemp();
    const MethodChannel channel =
        MethodChannel('plugins.flutter.io/path_provider');

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      if (methodCall.method == 'getTemporaryDirectory') {
        return directory.path;
      }
      return '.';
    });

    when(appRepository.getStoredLocale()).thenAnswer(
      (realInvocation) => const Locale('en'),
    );

    when(appRepository.loadL10n()).thenAnswer(
      (_) async => l10n,
    );

    when(locationRepository.requestDeviceLocation()).thenAnswer(
      (realInvocation) => Future.value(
        geo.Position(
          longitude: 1,
          latitude: 1,
          timestamp: DateTime.now(),
          accuracy: 1,
          altitude: 1,
          altitudeAccuracy: 1,
          heading: 1,
          headingAccuracy: 1,
          speed: 1,
          speedAccuracy: 1,
        ),
      ),
    );
  });

  // ignore: deprecated_member_use_from_same_package
  Future<FacialEnrollmentCubit> build({PermissionHandler? permission, FaceDetectionUtil? faceDetection, int? maxDetectionAttempts}) async {
    final value = FacialEnrollmentCubit(
      compression: imageCompression,
      biometricRepository: biometricRepository,
      locationRepository: locationRepository,
      faceDetector: faceDetector,
      permissionHandler: permission ?? permissionHandler,
      authManager: authManager,
      appRepository: appRepository,
      faceDetectionUtil: faceDetection ?? faceDetectionUtil,
      clientSettingsRepository: clientSettingsRepository,
      logger: logger,
      uploadWaitTime: const Duration(milliseconds: 10),
      maxDetectionAttempts: maxDetectionAttempts ?? 10,
      getAvailableCameras: availableCameras,
    );
    await value.setUp();
    return value;
  }

  group('facial enrollment bloc', () {
    test('inital state on first load', () async {
      final bloc = await build();

      expect(
        bloc.state,
        const FacialEnrollmentState.initial(),
      );
    });
    test('launchCamera sets state to cameraOpen', () async {
      final bloc = await build();
      verify(permissionHandler.requestLocationPermissions(any)).called(1);
      verifyNever(permissionHandler.requestCameraPermissions(any));
      when(faceDetector.processImage(InputImage.fromFile(File(''))))
          .thenAnswer((realInvocation) => Future.value([]));

      await bloc.launchCamera(const Size(400, 400));
      expect(faceDetectionUtil.initalizedWithFps, null);
      expect(faceDetectionUtil.initializedWithPreset, ResolutionPreset.max);

      expect(bloc.state.status, FacialEnrollmentStatus.cameraOpen);
    });

    test('backPressed results in inital state', () async {
      final bloc = await build();
      bloc.resetToInitial();

      expect(
        bloc.state.status,
        FacialEnrollmentStatus.initial,
      );
    });

    test('backPressed empties valid images', () async {
      final bloc = await build();
      bloc.resetToInitial();

      expect(
        bloc.state.validImages,
        isEmpty,
      );
    });

    test('photos uploaded returns a enroll complete state', () async {
      final bloc = await build();
      when(biometricRepository.postFacialEnrollmentPhotos(request)).thenAnswer(
        (realInvocation) => goodResponse,
      );
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );

      final streamQueue = StreamQueue(bloc.stream);
      when(
        clientSettingsRepository.reloadClientSettings(
          calendarLastCheck: anyNamed('calendarLastCheck'),
        ),
      ).thenAnswer(
        (realInvocation) async =>
            someClientSettings.copyWith(enrolledFacial: true),
      );
      unawaited(bloc.upload(const Size(100, 100)));
      expect(
        await bloc.nextStep(AuthenticationStatusBase.unknown, null),
        null,
      );
      expect(
        (await streamQueue.next).status,
        FacialEnrollmentStatus.enrollmentSuccess,
      );
      expect(
        await bloc.nextStep(AuthenticationStatusBase.unknown, null),
        const EmbarkAuthenticationState.enrollment(),
      );
      expect(
        (await streamQueue.next).status,
        FacialEnrollmentStatus.enrollmentComplete,
      );
      expect(
        bloc.state.status,
        FacialEnrollmentStatus.enrollmentComplete,
      );
      verify(clientSettingsRepository.reloadClientSettings()).called(1);
      verify(authenticationRepository.reevaluateAuthenticationStatus())
          .called(2);
      verify(
        biometricRepository.postFacialEnrollmentPhotos(any),
      ).called(1);
    });

    test(
        'calls API with the correct values when the last known location is null and device location service is enabled on Android OS',
        () async {
      final bloc = await build();

      when(biometricRepository.postFacialEnrollmentPhotos(request)).thenAnswer(
        (realInvocation) => goodResponse,
      );

      when(locationRepository.getLastKnownLocation())
          .thenAnswer((realInvocation) async => null);

      when(locationRepository.serviceEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );

      await bloc.upload(const Size(100, 100));

      final expectedRequest = request.copyWith(
        fixType: 1,
        latitude: 1.0,
        longitude: 1.0,
        locationAcquired: true,
        locationServicesEnabled: true,
        photos: [],
        accuracy: 1,
      );

      verify(
        biometricRepository.postFacialEnrollmentPhotos(expectedRequest),
      ).called(1);
    });
  });

  test('location upload sucessfull if location permissions denied', () async {
    final bloc = await build();
    bloc.deniedPermissions = ['location'];
    final streamQueue = StreamQueue(bloc.stream);
    when(
      clientSettingsRepository.reloadClientSettings(
        calendarLastCheck: anyNamed('calendarLastCheck'),
      ),
    ).thenAnswer(
      (realInvocation) async =>
          someClientSettings.copyWith(enrolledFacial: true),
    );

    when(biometricRepository.postFacialEnrollmentPhotos(request)).thenAnswer(
      (realInvocation) => goodResponse,
    );

    await bloc.upload(const Size(100, 100));

    expect(
      (await streamQueue.next).status,
      FacialEnrollmentStatus.enrollmentSuccess,
    );
    expect(
      (await streamQueue.next).status,
      FacialEnrollmentStatus.enrollmentComplete,
    );
  });

  test(
      'should call requestDeviceLcoation for up to date location when device location sevice is enabled  on Android OS',
      () async {
    final bloc = await build();
    await bloc.setUp();

    when(biometricRepository.postFacialEnrollmentPhotos(request)).thenAnswer(
      (realInvocation) => goodResponse,
    );
    when(locationRepository.serviceEnabled()).thenAnswer(
      (realInvocation) => Future.value(true),
    );
    await bloc.upload(const Size(100, 100));
    verify(locationRepository.requestDeviceLocation()).called(1);
  });

  test(
      'does not call requestDeviceLcoation for up to date location when device location sevice is disabled  on Android OS',
      () async {
    final bloc = await build();

    when(locationRepository.locationPermissionsEnabled()).thenAnswer(
      (realInvocation) => Future.value(true),
    );
    await bloc.setUp();

    when(biometricRepository.postFacialEnrollmentPhotos(request)).thenAnswer(
      (realInvocation) => goodResponse,
    );
    when(locationRepository.serviceEnabled()).thenAnswer(
      (realInvocation) => Future.value(false),
    );
    await bloc.upload(const Size(100, 100));

    verifyNever(locationRepository.requestDeviceLocation());
  });

  test('setUp should not initalize camera ', () async {
    await build();

    expect(
      (faceDetectionUtil.cameraController as FakeCameraController).initCalled,
      false,
    );
  });

  test('sets state to camera open when continueEnroll called', () async {
    final bloc = await build();

    when(locationRepository.locationPermissionsEnabled()).thenAnswer(
      (realInvocation) => Future.value(false),
    );

    when(biometricRepository.postFacialEnrollmentPhotos(request)).thenAnswer(
      (realInvocation) => goodResponse,
    );

    await bloc.continueEnrollment(const Size(100, 100));
    verify(permissionHandler.requestCameraPermissions(any))
        .called(greaterThanOrEqualTo(1));

    expect(bloc.state.status, FacialEnrollmentStatus.cameraOpen);
  });

  test(
      'if permissions not empty on continue, enrollment status should return rejectedPermissions ',
      () async {
    final bloc = await build(permission: permissionsDeniedHandler);

    await bloc.continueEnrollment(const Size(100, 100));

    expect(bloc.state.status, FacialEnrollmentStatus.rejectedPermissions);
  });
  test('setup without permissions', () async {
    when(permissionsDeniedHandler.requestLocationPermissions(l10n))
        .thenAnswer((realInvocation) async => ['Location']);

    final bloc = await build(permission: permissionsDeniedHandler);
    await bloc.setUp();

    await Future<void>.delayed(Duration.zero);
    expect(bloc.state.status, FacialEnrollmentStatus.warnLocation);
    expect(bloc.state.deniedPermissions, ['Location']);
  });
  test(
      'if permissions empty on continue, enrollment status should return camera open',
      () async {
    final bloc = await build();

    await bloc.continueEnrollment(const Size(100, 100));

    expect(bloc.state.status, FacialEnrollmentStatus.cameraOpen);
  });

  test('successful upload sets state with correct values', () async {
    final bloc = await build();
    bloc.emit(
      bloc.state.copyWith(
        validImages: [
          someFile,
          someFile,
          someFile,
          someFile,
          someFile,
        ],
      ),
    );
    when(
      clientSettingsRepository.reloadClientSettings(
        calendarLastCheck: anyNamed('calendarLastCheck'),
      ),
    ).thenAnswer(
      (realInvocation) async =>
          someClientSettings.copyWith(enrolledFacial: true),
    );
    final location = geo.Position(
      latitude: 37.7749,
      longitude: -122.4194,
      accuracy: 5.0,
      altitude: 0,
      timestamp: DateTime.timestamp(),
      altitudeAccuracy: 0,
      heading: 0,
      headingAccuracy: 0,
      speed: 0,
      speedAccuracy: 0,
      floor: 0,
    );
    final compressedPhoto = utf8.encode('compressed_photo');

    when(locationRepository.requestDeviceLocation())
        .thenAnswer((_) async => location);

    when(biometricRepository.postFacialEnrollmentPhotos(any))
        .thenAnswer((_) => Future.value(ApiResponse.ok()));

    when(imageCompression.compress(any, quality: anyNamed('quality')))
        .thenAnswer((_) async => compressedPhoto);
    final streamQueue = StreamQueue(bloc.stream);

    when(locationRepository.serviceEnabled()).thenAnswer(
      (realInvocation) => Future.value(true),
    );

    await bloc.upload(const Size(1080, 1920));

    verify(
      biometricRepository.postFacialEnrollmentPhotos(
        FacialEnrollmentRequest(
          locationAcquired: true,
          accuracy: 5,
          locationServicesEnabled: true,
          photos: [
            'Y29tcHJlc3NlZF9waG90bw==',
            'Y29tcHJlc3NlZF9waG90bw==',
            'Y29tcHJlc3NlZF9waG90bw==',
            'Y29tcHJlc3NlZF9waG90bw==',
            'Y29tcHJlc3NlZF9waG90bw==',
          ],
          fixType: LocationFixType.gps.value,
          latitude: 37.7749,
          longitude: -122.4194,
        ),
      ),
    ).called(1);
    await streamQueue.next;
    await streamQueue.next;

    expect(bloc.state.status, FacialEnrollmentStatus.enrollmentComplete);
    expect(bloc.state.validImages?.length, 5);
    expect(bloc.state.captureButtonPressed, isTrue);
  });

  test('initialize camera exception is logged', () async {
    final exception = CameraException('error', 'Apple with worms');
    final mockFaceDetectionUtil = MockFaceDetectionUtil();

    when(
      mockFaceDetectionUtil.initializeCamera(
        resolutionPreset: anyNamed('resolutionPreset'),
      ),
    ).thenAnswer(
      (_) => Future.error(exception),
    );

    final bloc = FacialEnrollmentCubit(
      compression: imageCompression,
      biometricRepository: biometricRepository,
      locationRepository: locationRepository,
      faceDetector: faceDetector,
      permissionHandler: permissionHandler,
      authManager: authManager,
      appRepository: appRepository,
      faceDetectionUtil: mockFaceDetectionUtil,
      clientSettingsRepository: clientSettingsRepository,
      logger: logger,
      uploadWaitTime: const Duration(milliseconds: 10),
    );
    await bloc.setUp();

    await bloc.launchCamera(const Size(400, 400));

    verify(
      logger.error(
        error: exception,
        message: 'Enrollment: Error initializing camera',
      ),
    );
  });

  test('uploading exception is logged', () async {
    final exception = CameraException('error', 'Apple with worms');

    when(biometricRepository.postFacialEnrollmentPhotos(any))
        .thenAnswer((_) => Future.error(exception));

    final bloc = await build();

    await bloc.upload(const Size(400, 400));

    verify(
      logger.error(
        error: exception,
        message: 'Enrollment: Error uploading photos',
      ),
    );
  });

  group('begin facial detection', () {
    late FacialEnrollmentState enrollmentSuccess;
    late FacialEnrollmentState faceNotFoundTimeoutError;
    late FacialEnrollmentState uploadError;
    late FacialEnrollmentState generalError;
    late Exception exception;
    late MockCameraController mockCameraController;
    setUp(() {
      mockCameraController = MockCameraController();
      exception = CameraException('error', 'Apple with worms');
      enrollmentSuccess = const FacialEnrollmentState.initial().copyWith(
        status: FacialEnrollmentStatus.enrollmentSuccess,
        validImages: List.filled(5, someFile),
        captureButtonPressed: true,
      );
      faceNotFoundTimeoutError = const FacialEnrollmentState.initial().copyWith(
        status: FacialEnrollmentStatus.error,
        validImages: [],
      );
      uploadError = const FacialEnrollmentState.initial().copyWith(
        status: FacialEnrollmentStatus.error,
        validImages: [],
        exception: exception,
      );
      generalError = const FacialEnrollmentState.initial().copyWith(
        status: FacialEnrollmentStatus.error,
        validImages: [],
        cameraController: mockCameraController,
      );
    });
    test('no camera permissions', () async {
      final bloc = await build(permission: permissionsDeniedHandler);
      await bloc.beginFacialDetection(const Size(400, 400));
      expect(bloc.state.status, FacialEnrollmentStatus.initial);
    });
    test('take and upload photos', () async {
      final bloc = await build();
      await faceDetectionUtil.initializeCamera(
          resolutionPreset: ResolutionPreset.max,);
      await bloc.beginFacialDetection(const Size(400, 400));
      await expectLater(
        bloc.stream,
        emitsInOrder([
          _createCameraOpenState(faceDetectionUtil, 1),
          _createCameraOpenState(faceDetectionUtil, 2),
          _createCameraOpenState(faceDetectionUtil, 3),
          _createCameraOpenState(faceDetectionUtil, 4),
          _createCameraOpenState(faceDetectionUtil, 5),
          enrollmentSuccess,
        ]),
      );
    });
    test('no face found', () async {
      final mockFaceDetectionUtil = MockFaceDetectionUtil();
      when(mockFaceDetectionUtil.findValidFacesForEnrollment(any))
          .thenAnswer((_) => Future.value(null));
      final bloc = await build(
          faceDetection: mockFaceDetectionUtil,
          maxDetectionAttempts: 6,
      );
      await faceDetectionUtil.initializeCamera(resolutionPreset: ResolutionPreset.max);
      await bloc.beginFacialDetection(const Size(400, 400));
      await expectLater(
        bloc.stream,
        emitsInOrder([
          faceNotFoundTimeoutError,
        ]),
      );
    });
    test('upload exception throws error', () async {
      when(biometricRepository.postFacialEnrollmentPhotos(any))
          .thenThrow(exception);
      final bloc = await build();
      await faceDetectionUtil.initializeCamera(resolutionPreset: ResolutionPreset.max);
      await bloc.beginFacialDetection(const Size(400, 400));
      await expectLater(
        bloc.stream,
        emitsInOrder([
          _createCameraOpenState(faceDetectionUtil, 1),
          _createCameraOpenState(faceDetectionUtil, 2),
          _createCameraOpenState(faceDetectionUtil, 3),
          _createCameraOpenState(faceDetectionUtil, 4),
          _createCameraOpenState(faceDetectionUtil, 5),
          uploadError,
        ]),
      );
    });
    test('general exception throws error', () async {
      final mockFaceDetectionUtil = MockFaceDetectionUtil();
      when(mockFaceDetectionUtil.cameraController)
          .thenReturn(mockCameraController);
      when(mockCameraController.takePicture()).thenThrow(exception);
      when(mockCameraController.value).thenReturn(
        const CameraValue(
          isInitialized: true,
          isRecordingVideo: false,
          isTakingPicture: false,
          isStreamingImages: false,
          isRecordingPaused: false,
          flashMode: FlashMode.always,
          exposureMode: ExposureMode.auto,
          focusMode: FocusMode.auto,
          exposurePointSupported: false,
          focusPointSupported: false,
          previewSize: Size(300, 300),
          deviceOrientation: DeviceOrientation.portraitUp,
          description: CameraDescription(
              lensDirection: CameraLensDirection.back,
              name: '',
              sensorOrientation: 0,
          ),
        ),
      );

      final bloc = await build(faceDetection: mockFaceDetectionUtil);
      await faceDetectionUtil.initializeCamera(resolutionPreset: ResolutionPreset.max);
      await bloc.beginFacialDetection(const Size(400, 400));
      await expectLater(
        bloc.stream,
        emitsInOrder([
          generalError,
        ]),
      );
    });
    test('flip camera back to front', () async {
      final bloc = await build();
      await faceDetectionUtil.initializeCamera(
        resolutionPreset: ResolutionPreset.max,);
      expect(faceDetectionUtil.cameraController?.description.lensDirection, CameraLensDirection.back);
      await bloc.flipCamera(const Size(400, 400));
      await Future<void>.delayed(Duration.zero);
      expect(bloc.state.cameraController?.description.lensDirection, CameraLensDirection.front);
    });
    test('flip camera front to back', () async {
      final bloc = await build();
      await faceDetectionUtil.initializeCamera(
        resolutionPreset: ResolutionPreset.max,);
      faceDetectionUtil.cameraController = CameraController(
        const CameraDescription(
          lensDirection: CameraLensDirection.front,
          name: '',
          sensorOrientation: 0,
        ),
        ResolutionPreset.max,
        enableAudio: false,
      );
      expect(faceDetectionUtil.cameraController?.description.lensDirection, CameraLensDirection.front);
      await bloc.flipCamera(const Size(400, 400));
      await Future<void>.delayed(Duration.zero);
      expect(bloc.state.cameraController?.description.lensDirection, CameraLensDirection.back);
    });
  });
}

FacialEnrollmentState _createCameraOpenState(FakeFaceDetectionUtil faceDetectionUtil, int imageCount) {
  final cameraOpenState = const FacialEnrollmentState.initial().copyWith(
    status: FacialEnrollmentStatus.cameraOpen,
    screenSize: double.infinity,
    cameraController: faceDetectionUtil.cameraController,
    imageCount: imageCount,
    validImages: List.filled(imageCount, someFile),
    captureButtonPressed: true,
  );
  return cameraOpenState;
}
