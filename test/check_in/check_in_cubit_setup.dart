import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/check_in/blink_detector.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/check_in_type.dart';
import 'package:embark/check_in/cubit/check_in_cubit.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:flutter/foundation.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';
import '../unit_test_helpers/fakes/camera_controller.dart';
import '../unit_test_helpers/fakes/image_compression.dart';
import '../widgets/documents/add_documents_screen_test.dart';

final someFile = XFile.fromData(Uint8List(1));

class CheckInCubitSetup {
  CheckInCubitSetup({required CameraDescription cameraDescription})
      : cameraController = FakeCameraController(cameraDescription) {
    getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
      iCSController.stream,
    );
    getIt.registerSingleton<BiometricRepository>(
      biometricRepository,
    );
    getIt.registerSingleton<ImageCompression>(
      imageCompression,
    );
    getIt.registerSingleton<SharedPreferencesService>(sharedPreferencesService);
    getIt.registerSingleton(
      timestampRepo,
    );
    when(appRepository.loadL10n())
        .thenAnswer((_) async => AppLocalizationsEn());
  }

  late final CheckinCubit cubit = CheckinCubit(
    biometricRepository: biometricRepository,
    deviceSetupRepository: deviceSetupRepository,
    imageCompression: imageCompression,
    locationRepository: locationRepository,
    connectivityStream: iCSController.stream,
    checkInType: CheckInType.other,
    faceDetectionUtil: faceDetectionUtil,
    cameraUtil: cameraUtil,
    sharedPreferencesService: sharedPreferencesService,
    timestampRepository: timestampRepo,
    permissionHandler: permissionHandler,
    appRepository: appRepository,
    clientSettingsRepository: clientSettingsRepository,
  );

  late final CheckinCubit cubitAdHocTrue = CheckinCubit(
    biometricRepository: biometricRepository,
    deviceSetupRepository: deviceSetupRepository,
    imageCompression: imageCompression,
    locationRepository: locationRepository,
    connectivityStream: iCSController.stream,
    checkInType: CheckInType.other,
    faceDetectionUtil: faceDetectionUtil,
    cameraUtil: cameraUtil,
    sharedPreferencesService: sharedPreferencesService,
    timestampRepository: timestampRepo,
    permissionHandler: permissionHandler,
    appRepository: appRepository,
    clientSettingsRepository: clientSettingsRepository,
  );

  late final CheckinCubit selfReportTrue = CheckinCubit(
    biometricRepository: biometricRepository,
    deviceSetupRepository: deviceSetupRepository,
    imageCompression: imageCompression,
    locationRepository: locationRepository,
    connectivityStream: iCSController.stream,
    faceDetectionUtil: faceDetectionUtil,
    cameraUtil: cameraUtil,
    sharedPreferencesService: sharedPreferencesService,
    checkInType: CheckInType.selfReport,
    timestampRepository: timestampRepo,
    permissionHandler: permissionHandler,
    appRepository: appRepository,
    clientSettingsRepository: clientSettingsRepository,
  );

  late final CheckinCubit pinResetCubit = CheckinCubit(
    biometricRepository: biometricRepository,
    deviceSetupRepository: deviceSetupRepository,
    imageCompression: imageCompression,
    locationRepository: locationRepository,
    connectivityStream: iCSController.stream,
    checkInType: CheckInType.other,
    faceDetectionUtil: faceDetectionUtil,
    cameraUtil: cameraUtil,
    sharedPreferencesService: sharedPreferencesService,
    timestampRepository: timestampRepo,
    permissionHandler: permissionHandler,
    appRepository: appRepository,
    clientSettingsRepository: clientSettingsRepository,
  );

  late final timestampRepo = CheckinTimestampRepository(
    sharedPreferencesService: sharedPreferencesService,
  );
  final MockBiometricRepository biometricRepository = MockBiometricRepository();
  final LocationRepository locationRepository = MockLocationRepository();
  final FakeImageCompression imageCompression = FakeImageCompression();
  final MockSharedPreferencesService sharedPreferencesService =
      MockSharedPreferencesService();
  final deviceSetupRepository = MockDeviceSetupRepository();

  final BiStreamController<List<ConnectivityResult>> iCSController =
      BiStreamController<List<ConnectivityResult>>();
  late final BlinkDetector blinkDetector = BlinkDetector(
    faceDetectionService,
  );
  final MockFaceDetectionService faceDetectionService =
      MockFaceDetectionService();
  final FakeFaceDetectionUtil faceDetectionUtil = FakeFaceDetectionUtil();
  final FakeCameraController cameraController;
  final MockCameraUtil cameraUtil = MockCameraUtil();
  final MockPermissionHandler permissionHandler = MockPermissionHandler();
  final MockAppRepository appRepository = MockAppRepository();
  final MockClientSettingsRepository clientSettingsRepository =
      MockClientSettingsRepository();

  Future<void> teardown() async {
    await iCSController.close();
    await getIt.reset();
  }
}

class FakeFaceDetectionUtil extends Mock implements FaceDetectionUtil {
  CameraController _controller = FakeCameraController(description);
  @override
  CameraController? get cameraController => _controller;

  @override
  set cameraController(CameraController? value) => _controller = FakeCameraController(value!.description);

  @override
  Future<XFile?> captureAndCompressImage(int? imageNumber) async {
    return Future.value(someFile);
  }

  @override
  Future<XFile?> findValidFacesForEnrollment(int? imageNumber) async =>
      Future.value(someFile);

  bool didDispose = false;
  @override
  Future<void> dispose() async {
    didDispose = true;
  }

  @override
  Future<void> initializeCamera({
    int? fps,
    required ResolutionPreset resolutionPreset,
  }) async {
    initalizedWithFps = fps;
    initializedWithPreset = resolutionPreset;
  }

  int? initalizedWithFps;
  ResolutionPreset? initializedWithPreset;
  @override
  Future<void> resetCamera() async {}
}
