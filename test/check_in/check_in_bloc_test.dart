import 'dart:async';
import 'dart:io';

import 'package:bi_embark_l10n/l10n/app_localizations_es.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/cubit/check_in_cubit.dart';
import 'package:embark/check_in/cubit/check_in_state.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:permission_handler/permission_handler.dart';

import '../unit_test.mocks.dart';
import '../unit_test_helpers/fakes/camera_controller.dart';
import '../widgets/documents/add_documents_screen_test.dart';
import 'check_in_cubit_setup.dart';

class FakeAppRepository extends Mock implements MockAppRepository {
  @override
  Locale getStoredLocale() => const Locale('en', 'EN');
}

const validRequestIsNotAlive = CheckInRequest(
  fixType: 1,
  latitude: 1.0,
  longitude: 1.0,
  locationAcquired: true,
  locationServicesEnabled: true,
  photo: 'AA==',
  accuracy: null,
  cellStrength: null,
  hdop: null,
  isAlive: false,
  heading: null,
  satellites: null,
  speed: null,
  fingerprintConfirmed: null,
  resetPin: false,
  useWifi: true,
);
const validRequestIsAlive = CheckInRequest(
  fixType: 1,
  latitude: 1.0,
  longitude: 1.0,
  locationAcquired: true,
  locationServicesEnabled: true,
  photo: 'AA==',
  accuracy: 1.0,
  cellStrength: null,
  hdop: null,
  isAlive: true,
  heading: null,
  satellites: null,
  speed: null,
  fingerprintConfirmed: null,
  resetPin: false,
  useWifi: false,
);

const validRequestUseWifi = CheckInRequest(
  fixType: 1,
  latitude: 1.0,
  longitude: 1.0,
  locationAcquired: true,
  locationServicesEnabled: true,
  photo: 'AA==',
  accuracy: 1.0,
  cellStrength: null,
  hdop: null,
  isAlive: true,
  heading: null,
  satellites: null,
  speed: null,
  fingerprintConfirmed: null,
  resetPin: false,
  useWifi: true,
);

void main() {
  late CheckInCubitSetup setup;

  late MockBiometricRepository biometricRepository;
  late LocationRepository locationRepository;
  late FakeCameraController cameraController;
  late CheckinCubit bloc;
  late CheckinCubit adHocBloc;
  late CameraUtil cameraUtil;
  late FakeFaceDetectionUtil faceDetectionUtil;
  late MockDeviceSetupRepository deviceSetupRepository;
  late MockPermissionHandler permissionHandler;
  late MockClientSettingsRepository clientSettingsRepository;
  final goodResponse = Future.value(ApiResponse.ok());

  setUp(() async {
    setup = CheckInCubitSetup(cameraDescription: description);
    deviceSetupRepository = setup.deviceSetupRepository;
    biometricRepository = setup.biometricRepository;
    locationRepository = setup.locationRepository;
    cameraUtil = setup.cameraUtil;
    faceDetectionUtil = setup.faceDetectionUtil;
    cameraController = setup.cameraController;
    permissionHandler = setup.permissionHandler;
    clientSettingsRepository = setup.clientSettingsRepository;

    setup.iCSController.sink.add([ConnectivityResult.wifi]);

    /// set up path_provider
    final directory = await Directory.systemTemp.createTemp();
    const MethodChannel channel =
        MethodChannel('plugins.flutter.io/path_provider');

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      if (methodCall.method == 'getTemporaryDirectory') {
        return directory.path;
      }
      return '.';
    });

    when(locationRepository.serviceEnabled()).thenAnswer(
      (realInvocation) => Future.value(true),
    );

    when(locationRepository.getLastKnownLocation()).thenAnswer(
      (realInvocation) => Future.value(
        geo.Position(
          longitude: 1,
          latitude: 1,
          timestamp: DateTime.now(),
          accuracy: 1,
          altitude: 1,
          altitudeAccuracy: 1,
          heading: 1,
          headingAccuracy: 1,
          speed: 1,
          speedAccuracy: 1,
        ),
      ),
    );

    when(locationRepository.requestDeviceLocation()).thenAnswer(
      (realInvocation) => Future.value(
        geo.Position(
          longitude: 1,
          latitude: 1,
          timestamp: DateTime.now(),
          accuracy: 1,
          altitude: 1,
          altitudeAccuracy: 1,
          heading: 1,
          headingAccuracy: 1,
          speed: 1,
          speedAccuracy: 1,
        ),
      ),
    );

    when(permissionHandler.grantStatus(Permission.location)).thenAnswer(
      (realInvocation) => Future.value(PermissionStatus.granted),
    );

    bloc = setup.cubit;
    adHocBloc = setup.cubitAdHocTrue;
    when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
      ResolvedDeviceType.byod,
    );
  });

  tearDown(
    () async {
      await setup.teardown();
    },
  );

  group('check in bloc', () {
    test('beginFacialDetection emits cameraOpen state', () async {
      await runZonedGuarded(
        () async {
          FlutterError.onError = (details) {
            /* Fails after 15 seconds. Ignoring as not part of this test */
          };

          await bloc.beginFacialDetection(Size.zero);
          expect(faceDetectionUtil.initalizedWithFps, 30);
          expect(faceDetectionUtil.initializedWithPreset, ResolutionPreset.low);

          expect(
            bloc.state.status,
            equals(CheckInStatus.cameraOpen),
          );
        },
        (error, stack) {},
      );
    });

    test('check emits pop up when location permissions denied on set up ',
        () async {
      when(cameraUtil.listDeniedPermissions())
          .thenAnswer((realInvocation) => Future.value(['Location']));

      when(locationRepository.serviceEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );

      when(permissionHandler.grantStatus(Permission.location)).thenAnswer(
        (realInvocation) => Future.value(PermissionStatus.denied),
      );
      await bloc.setUp();

      expect(
        bloc.state.status,
        equals(CheckInStatus.rejectedLocation),
      );
    });

    test(
        'check emits pop up when location permission is permanently denied on set up ',
        () async {
      when(cameraUtil.listDeniedPermissions())
          .thenAnswer((realInvocation) => Future.value(['Location']));

      when(locationRepository.serviceEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );

      when(permissionHandler.grantStatus(Permission.location)).thenAnswer(
        (realInvocation) => Future.value(PermissionStatus.denied),
      );

      when(permissionHandler.grantStatus(Permission.location)).thenAnswer(
        (realInvocation) => Future.value(PermissionStatus.permanentlyDenied),
      );
      await bloc.setUp();

      expect(
        bloc.state.status,
        equals(CheckInStatus.rejectedLocation),
      );
    });

    test('check does not emit permission pop up for mdm devices', () async {
      when(cameraUtil.listDeniedPermissions()).thenAnswer(
        (_) => Future.value([
          'Camera',
        ]),
      );
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(false),
      );
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.mdm,
      );
      const size = Size(100, 100);
      final screenSize = cameraUtil.calculateScale(
        size,
        cameraController,
      );

      final rejectedPermissionsCheckInState = CheckinState(
        status: CheckInStatus.rejectedPermissions,
        screenSize: screenSize,
        deniedPermissions: const ['Camera'],
      );

      final List<CheckInStatus> events = [];
      bloc.stream.listen((state) {
        events.add(state.status);
        expect(state, isNot(rejectedPermissionsCheckInState));
      });

      await bloc.continueCheckin(size);
      await Future<void>.delayed(Duration.zero);
      // We dont want the timers to run their course for this test
      bloc.stopImageProcessing();
      expect(events, [
        CheckInStatus.cameraOpen,
      ]);
    });

    test('check does emit permission pop up for non mdm devices', () async {
      when(cameraUtil.listDeniedPermissions()).thenAnswer(
        (_) => Future.value([
          'Camera',
        ]),
      );
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(false),
      );
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );
      const size = Size(100, 100);

      final List<CheckInStatus> events = [];
      bloc.stream.listen((state) {
        events.add(state.status);
      });

      await bloc.continueCheckin(size);
      await Future<void>.delayed(Duration.zero);
      expect(
        events,
        [CheckInStatus.initial, CheckInStatus.rejectedPermissions],
      );
    });

    test(
        'check does emit permission pop up for non mdm devices, non english languages',
        () async {
      final l10n = AppLocalizationsEs();
      when(bloc.appRepository.loadL10n())
          .thenAnswer((realInvocation) => Future.value(l10n));
      when(cameraUtil.listDeniedPermissions()).thenAnswer(
        (_) => Future.value([
          l10n.camera,
        ]),
      );
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(false),
      );
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );
      const size = Size(100, 100);

      final List<CheckInStatus> events = [];
      bloc.stream.listen((state) {
        events.add(state.status);
      });

      await bloc.continueCheckin(size);
      await Future<void>.delayed(Duration.zero);
      expect(
        events,
        [CheckInStatus.initial, CheckInStatus.rejectedPermissions],
      );
    });

    test('calls repository with "isAlive" value of true when user has blinked',
        () async {
      await runZonedGuarded(
        () async {
          const size = Size(100, 100);
          FlutterError.onError = (details) {
            /* Fails after 15 seconds. Ignoring as not part of this test  */
          };
          when(locationRepository.locationPermissionsEnabled()).thenAnswer(
            (realInvocation) => Future.value(false),
          );

          when(
            biometricRepository.postCheckInPhoto(validRequestIsAlive),
          ).thenAnswer(
            (realInvocation) => goodResponse,
          );
          await bloc.continueCheckin(size);
          await bloc.userLive(someFile);

          verify(biometricRepository.postCheckInPhoto(validRequestIsAlive))
              .called(1);
        },
        (e, s) {},
      );
    });

    test(
        'calls repository with "isAlive" value of true when user has not blinked',
        () async {
      () async {
        await runZonedGuarded(
          () async {
            FlutterError.onError = (details) {
              /* Fails after 15 seconds. Ignoring as not part of this test  */
            };
            when(locationRepository.locationPermissionsEnabled()).thenAnswer(
              (realInvocation) => Future.value(false),
            );

            when(
              biometricRepository.postCheckInPhoto(validRequestIsNotAlive),
            ).thenAnswer(
              (realInvocation) => goodResponse,
            );
            await bloc.continueCheckin(const Size(100, 100));
            await bloc.userNotLive(someFile);

            verify(biometricRepository.postCheckInPhoto(validRequestIsNotAlive))
                .called(1);
          },
          (e, s) {},
        );
      };
    });

    test('reloads client settings when user has blinked', () async {
      await runZonedGuarded(
        () async {
          const size = Size(100, 100);
          FlutterError.onError = (details) {
            /* Fails after 15 seconds. Ignoring as not part of this test  */
          };
          when(locationRepository.locationPermissionsEnabled()).thenAnswer(
            (realInvocation) => Future.value(false),
          );

          when(
            biometricRepository.postCheckInPhoto(validRequestIsAlive),
          ).thenAnswer(
            (realInvocation) => goodResponse,
          );
          await bloc.continueCheckin(size);
          await bloc.userLive(someFile);

          verify(
            clientSettingsRepository.reloadClientSettings(
              after: const Duration(seconds: 1),
            ),
          ).called(1);
        },
        (e, s) {},
      );
    });

    test('reloads client settings when user has not blinked', () async {
      () async {
        await runZonedGuarded(
          () async {
            FlutterError.onError = (details) {
              /* Fails after 15 seconds. Ignoring as not part of this test  */
            };
            when(locationRepository.locationPermissionsEnabled()).thenAnswer(
              (realInvocation) => Future.value(false),
            );

            when(
              biometricRepository.postCheckInPhoto(validRequestIsNotAlive),
            ).thenAnswer(
              (realInvocation) => goodResponse,
            );
            await bloc.continueCheckin(const Size(100, 100));
            await bloc.userNotLive(someFile);

            verify(
              clientSettingsRepository.reloadClientSettings(
                after: const Duration(seconds: 1),
              ),
            ).called(1);
          },
          (e, s) {},
        );
      };
    });

    test('should call dispose at end of upload', () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );

      await bloc.upload(someFile, isAlive: true);
      expect(
        bloc.faceDetectionUtil.cameraController?.value.isInitialized,
        false,
      );
    });

    test(
        'should call saveAdHocCheckIn if cubit initialized with adHocCheckIn = true',
        () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );

      await adHocBloc.upload(someFile, isAlive: true);

      // setStringList should only get called if saveAdHocCheckIn called
      verify(
        setup.sharedPreferencesService.setStringList(
          CheckinTimestampRepository.adHocCheckInsListKey,
          any,
        ),
      ).called(1);
    });

    test('save adHocCheckIn triggers correct methods when called', () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );

      await adHocBloc.upload(someFile, isAlive: true);

      verify(
        setup.sharedPreferencesService.setStringList(
          CheckinTimestampRepository.adHocCheckInsListKey,
          any,
        ),
      ).called(1);
    });

    test('should call upload with useWifi true when wifi enabled', () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );
      // CheckInRequest(fixType: 1, latitude: 1.0, longitude: 1.0, locationAcquired: true, locationServicesEnabled: true, photo: AQ==, accuracy: 1.0, cellStrength: null, hdop: null, isAlive: true, heading: null, satellites: null, speed: null, fingerprintConfirmed: null, resetPin: false, useWifi: true);
      await setup.cubit.setUp();
      setup.iCSController.sink.add([ConnectivityResult.wifi]);

      await bloc.upload(someFile, isAlive: true);

      verify(biometricRepository.postCheckInPhoto(validRequestUseWifi))
          .called(1);
    });

    test('should call requestDeviceLcoation for up to date location ',
        () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );
      await setup.cubit.setUp();

      when(
        biometricRepository.postCheckInPhoto(validRequestUseWifi),
      ).thenAnswer((realInvocation) {
        return goodResponse;
      });

      await bloc.upload(someFile, isAlive: true);

      verify(locationRepository.requestDeviceLocation()).called(1);
    });

    test('setUp should not initalize camera ', () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );
      await setup.cubit.setUp();

      expect(
        (setup.faceDetectionUtil.cameraController as FakeCameraController)
            .initCalled,
        false,
      );
    });

    test('should return a report when buildSelfReportWithPhoto called ',
        () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );
      await setup.cubit.setUp();

      when(
        biometricRepository.postCheckInPhoto(validRequestUseWifi),
      ).thenAnswer((realInvocation) {
        return goodResponse;
      });

      expect(
        await bloc.buildSelfReportWithPhoto(someFile),
        isA<PostSelfReportAnswerRequestWithPhoto>(),
      );
    });

    test(
        'should return a report with location when location permissions granted ',
        () async {
      await setup.cubit.setUp();

      when(cameraUtil.listDeniedPermissions())
          .thenAnswer((real) => Future.value([]));

      when(locationRepository.requestDeviceLocation()).thenAnswer(
        (realInvocation) => Future<geo.Position>.value(
          geo.Position(
            accuracy: 1,
            latitude: 0,
            longitude: 0,
            altitude: 0,
            altitudeAccuracy: 0,
            heading: 0,
            headingAccuracy: 0,
            speed: 0,
            speedAccuracy: 0,
            timestamp: DateTime.timestamp(),
          ),
        ),
      );

      final report = await bloc.buildSelfReportWithPhoto(someFile);

      expect(report.accuracy, 1);
      expect(report.latitude, 0);
      expect(report.longitude, 0);
      expect(report.heading, 0);
      expect(report.speed, 0);
      expect(report.locationAcquired, true);
    });

    test('should contain success state when selfReportCheckIn is true',
        () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );
      await setup.selfReportTrue.setUp();

      await setup.selfReportTrue.upload(someFile, isAlive: true);

      await Future.delayed(const Duration(seconds: 3), () {});

      expect(
        setup.selfReportTrue.state.status,
        CheckInStatus.checkInSuccess,
      );
    });

    test('should contain success state when doing a pin reset without delay',
        () async {
      when(locationRepository.locationPermissionsEnabled()).thenAnswer(
        (realInvocation) => Future.value(true),
      );
      await setup.pinResetCubit.setUp();

      when(
        biometricRepository.postCheckInPhoto(validRequestUseWifi),
      ).thenAnswer((realInvocation) {
        return goodResponse;
      });

      await setup.pinResetCubit.upload(someFile, isAlive: true);

      expect(
        setup.pinResetCubit.state.status,
        CheckInStatus.checkInSuccess,
      );
    });
  });

  test('should not call saveAdHocCheckIn when in self report flow  ', () async {
    when(locationRepository.locationPermissionsEnabled()).thenAnswer(
      (realInvocation) => Future.value(true),
    );
    await setup.selfReportTrue.setUp();

    when(
      biometricRepository.postCheckInPhoto(validRequestUseWifi),
    ).thenAnswer((realInvocation) {
      return goodResponse;
    });

    await setup.selfReportTrue.upload(someFile, isAlive: true);

    verifyNever(
      setup.sharedPreferencesService
          .setStringList(CheckinTimestampRepository.adHocCheckInsListKey, any),
    );
  });
}
