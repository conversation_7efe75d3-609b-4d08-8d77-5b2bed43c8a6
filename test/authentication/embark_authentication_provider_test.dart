import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/authentication/auth_steps/default_auth_step.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/authentication/auth_steps/get_auth_settings.dart';
import 'package:embark/authentication/auth_steps/post_authentication_auth_step.dart';
import 'package:embark/authentication/auth_steps/showing_dashboard_step.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_provider.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/check_in/blink_detector.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/check_in/face_detector_service.dart';
import 'package:embark/check_in/views/check_in_page.dart';
import 'package:embark/dashboard/cubit/_dashboard_cubit.dart';
import 'package:embark/dashboard/cubit/_dashboard_state.dart';
import 'package:embark/dashboard/dashboard.dart';
import 'package:embark/extensions/_stream_to_listener.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/home/<USER>/home_state.dart';
import 'package:embark/language/cubit/language_cubit.dart';
import 'package:embark/language/views/language_page.dart';
import 'package:embark/login/cubit/embark_login_state.dart';
import 'package:embark/login/cubit/login_cubit.dart';
import 'package:embark/login/view/login_page.dart';
import 'package:embark/navigation_observers/_arrived_on_dashboard_observer.dart';
import 'package:embark/permissions/cubit/permissions_cubit.dart';
import 'package:embark/permissions/permissions.dart';
import 'package:embark/pin/reset/check_in_result.dart';
import 'package:embark/pin/reset/cubit/pin_reset_cubit.dart';
import 'package:embark/pin/reset/view/pin_reset_page.dart';
import 'package:embark/pin/set/view/_pin_set_page.dart';
import 'package:embark/pin/validate/bloc/_pin_validate_page.dart';
import 'package:embark/pin/validate/views/_pin_entry_view.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/push_notifications/cubit/push_notifications_cubit.dart';
import 'package:embark/push_notifications/models/push_notification_type.dart';
import 'package:embark/repositories/_background_isolate_starter_repository.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/repositories/smart_band/smart_band.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/cubit/_self_report_state.dart';
import 'package:embark/self_report/cubit/state_machine/_self_report_state_machine.dart';
import 'package:embark/self_report/self_report.dart';
import 'package:embark/services/_permission_handler.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:embark/starter.dart';
import 'package:embark/terms_and_conditions/cubit/terms_and_conditions_cubit.dart';
import 'package:embark/terms_and_conditions/terms_and_conditions_page.dart';
import 'package:embark/ui_helpers/ui_lookup_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:mockito/mockito.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../unit_test.mocks.dart';
import '../unit_test_helpers/data/user.dart';
import '../unit_test_helpers/fakes/fake_bi_permission_request_repository.dart';
import '../unit_test_helpers/fakes/fake_bluetooth_frontend.dart';
import '../unit_test_helpers/fakes/fake_service_instance.dart';
import '../unit_test_helpers/fakes/image_compression.dart';

void main() {
  // This is a massive code smell.  If you are adding to this talk to a lead before you do.
  // TODO convert to using FakeAuthenticationRoutes to get this a reasonable level.
  late MockEmbarkAuthenticationRepository authenticationRepository;
  late MockClientSettingsRepository clientSettingsRepository;
  late EmbarkAuthenticationBloc authenticationBloc;
  late BiStreamController<List<ConnectivityResult>> internetConnectionStream;
  late MockLanguageCubit languageCubit;
  late MockPermissionsCubit permissionsCubit;
  late MockAppSettings appSettings;
  late MockEmbarkAuthenticationRepository mockEmbarkAuthenticationRepository;
  late MockPushNotificationsCubit pushNotificationsCubit;
  late MockAppRepository appRepository;
  late BiStreamController<AuthenticationStatusBase> authStatusStream;
  late MockDeviceInfoRepository deviceInfoRepository;
  late MockDashboardCubit dashboardCubit;
  late MockHomeCubit homeCubit;
  late MockPushNotificationListenerCubit pushNotificationListenerCubit;
  late MockSharedPreferencesService sharedPreferencesService;
  late MockSmartBandPreferences smartLinkPreferences;
  late MockFaceDetector faceDetector;
  late MockPermissionHandler permissionHandler;
  late MockSelfReportCubit selfReportCubit;
  late MockLoginCubit loginCubit;
  late MockBiometricRepository biometricRepository;
  late FakeImageCompression imageCompression;
  late LocationRepository locationRepository;
  late BlinkDetector blinkDetector;
  late FaceDetectionUtil faceDetectionUtil;
  late MockDeviceSetupRepository deviceSetupRepository;
  late ArrivedOnDashboardObserver arrivedOnDashboardObserver;
  late ValueNotifier<GetClientSettingsResponse?> clientSettingNotifier;
  late ValueNotifier<Object?> valueNotifierError;
  late MockL10nAssetLoader termsAndConditions;
  late MockTermsAndConditionsCubit termsAndConditionsCubit;
  late CameraUtil cameraUtil;
  late MockPinRepository pinRepository;
  late MockBiLogger biLogger;
  late PinResetCubit pinResetCubit;
  late AuthHandler authHandler;
  late MdmAuthHandler mdmAuthHandler;
  late ShowingDashboardStep showingDashboardStep;
  final ValueNotifier<bool> reset = ValueNotifier(false);
  late MockAppSettingsRepository appSettingsRepository;
  late MockBiTokenManager mockBiTokenManager;
  late SmartBandSettingsCubit smartBandSettingsCubit;
  late FakeBluetoothFrontend frontend;
  late MockSensorEventInfoRepository sensorEventInfoRepository;
  late FakeBiPermissionRequestRepository permissionRequestRepository;
  late MockEmbarkPermissionPreferences permissionPreferences;
  late MockSmartLinkFirmwareInfoHttpClient mockSmartLinkFirmwareInfoHttpClient;
  group('EmbarkAuthenticationProvider', () {
    setUp(() {
      reset.value = false;
      VisibilityDetectorController.instance.updateInterval = Duration.zero;
      authenticationRepository = MockEmbarkAuthenticationRepository();
      clientSettingsRepository = MockClientSettingsRepository();
      internetConnectionStream =
          BiStreamController<List<ConnectivityResult>>.broadcast();
      mockEmbarkAuthenticationRepository = MockEmbarkAuthenticationRepository();
      languageCubit = MockLanguageCubit();
      permissionsCubit = MockPermissionsCubit();
      appSettings = MockAppSettings();
      pushNotificationsCubit = MockPushNotificationsCubit();
      appRepository = MockAppRepository();
      showingDashboardStep = ShowingDashboardStep();
      authStatusStream = BiStreamController();
      deviceInfoRepository = MockDeviceInfoRepository();
      dashboardCubit = MockDashboardCubit();
      homeCubit = MockHomeCubit();
      pushNotificationListenerCubit = MockPushNotificationListenerCubit();
      sharedPreferencesService = MockSharedPreferencesService();
      smartLinkPreferences = MockSmartBandPreferences();
      faceDetector = MockFaceDetector();
      permissionHandler = MockPermissionHandler();
      selfReportCubit = MockSelfReportCubit();
      loginCubit = MockLoginCubit();
      biometricRepository = MockBiometricRepository();
      imageCompression = FakeImageCompression();
      locationRepository = MockLocationRepository();
      faceDetectionUtil = MockFaceDetectionUtil();
      blinkDetector = BlinkDetector(
        FaceDetectionService(faceDetector),
      );
      arrivedOnDashboardObserver = ArrivedOnDashboardObserver();
      deviceSetupRepository = MockDeviceSetupRepository();
      termsAndConditions = MockL10nAssetLoader();
      mdmAuthHandler =
          MdmAuthHandler(deviceSetupRepository, clientSettingsRepository);
      termsAndConditionsCubit = MockTermsAndConditionsCubit();
      cameraUtil = CameraUtil(
        permissionHandler: permissionHandler,
        appRepository: appRepository,
      );
      pinRepository = MockPinRepository();
      biLogger = MockBiLogger();
      mockBiTokenManager = MockBiTokenManager();
      frontend = FakeBluetoothFrontend();
      sensorEventInfoRepository = MockSensorEventInfoRepository();
      mockSmartLinkFirmwareInfoHttpClient =
          MockSmartLinkFirmwareInfoHttpClient();

      pinResetCubit = PinResetCubit(
        appRepository,
        sharedPreferencesService,
        mdmAuthHandler,
        clientSettingsRepository,
      );

      final bluetoothApi = MockBiBluetoothApi();
      permissionRequestRepository = FakeBiPermissionRequestRepository(
        bluetoothApi: bluetoothApi,
      );
      permissionPreferences = MockEmbarkPermissionPreferences();

      smartBandSettingsCubit = SmartBandSettingsCubit(
        clientSettingsRepository,
        SmartBandUiRepository(
          sharedPreferencesService: smartLinkPreferences,
          bluetoothApi: bluetoothApi,
          frontend: frontend,
          sensorEventInfoRepository: sensorEventInfoRepository,
          smartLinkFirmwareInfoHttpClient: mockSmartLinkFirmwareInfoHttpClient,
          backgroundIsolateStarterRepository:
              BackgroundIsolateStarterRepository(
            backgroundService: FakeFlutterBackgroundService(),
          ),
          permissionRequestRepository: permissionRequestRepository,
        ),
        MockBiLogger(),
        biometricRepository,
      );

      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: false,
      );
      clientSettingNotifier = ValueNotifier(getClientSettingsResponse);
      valueNotifierError = ValueNotifier(null);
      appSettingsRepository = MockAppSettingsRepository();
      when(appSettings.packageInfo).thenReturn(MockBiPackageInfo());
      when(appSettings.featureFlags).thenReturn(const FeatureFlags());
      when(clientSettingsRepository.clientSettingsListener)
          .thenAnswer((realInvocation) => clientSettingNotifier);
      when(clientSettingsRepository.clientSettingsErrorListener)
          .thenAnswer((realInvocation) => valueNotifierError);
      authHandler = AuthHandler.prod(
        mdmAuthHandler: mdmAuthHandler,
        postAuthenticationAuthStep: PostAuthenticationAuthStep(
          deviceInfoRepository: deviceInfoRepository,
          clientSettingsRepository: clientSettingsRepository,
          authenticationRepository: authenticationRepository,
          deviceSetupRepository: deviceSetupRepository,
          pushNotificationsCubit: pushNotificationsCubit,
          appSettingsRepository: appSettingsRepository,
          permissionHandler: permissionHandler,
        ),
        defaultAuthStep: DefaultAuthStep(
          clientSettingsRepository: clientSettingsRepository,
        ),
        getSettings: GetAuthSettingsStep(
          clientSettingsRepository: clientSettingsRepository,
        ),
      );
      authenticationBloc = EmbarkAuthenticationBloc(
        deviceSetupRepository: deviceSetupRepository,
        authenticationRepository: authenticationRepository,
        clientSettingsRepository: clientSettingsRepository,
        authHandler: authHandler,
      );

      when(appRepository.authenticationStatusSink)
          .thenAnswer((realInvocation) => authStatusStream.sink);
      when(dashboardCubit.state).thenReturn(const DashboardState());
      when(homeCubit.state).thenReturn(
        HomeState.initial().copyWith(
          todoTasksLoading: false,
          todoTasks: [],
        ),
      );
      when(pushNotificationListenerCubit.startingDashBoardTabId).thenReturn(-1);
      when(pushNotificationListenerCubit.state).thenReturn(
        PushNotificationListenerState.initial(false),
      );
      when(loginCubit.state).thenReturn(
        const EmbarkLoginState(status: EmbarkLoginStatus.login),
      );

      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.mdm,
      );
      when(appRepository.loadL10n())
          .thenAnswer((_) => Future.value(AppLocalizationsEn()));

      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );

      when(
        termsAndConditions.loadTermsAndConditions(
          context: anyNamed('context'),
          locale: anyNamed('locale'),
        ),
      ).thenAnswer((realInvocation) async => '<h1>terms</h1>');

      getIt.registerSingleton<DashboardCubit>(dashboardCubit);
      getIt.registerSingleton<TermsAndConditionsCubit>(termsAndConditionsCubit);
      getIt.registerSingleton<LanguageCubit>(languageCubit);
      getIt.registerSingleton<PermissionsCubit>(permissionsCubit);
      getIt.registerSingleton<HomeCubit>(homeCubit);
      getIt.registerSingleton(mdmAuthHandler);
      getIt.registerSingleton(showingDashboardStep);
      getIt.registerSingleton<MdmShowAuth>(mdmAuthHandler);
      getIt.registerSingleton(
        AuthManager(
          authenticationRepository: authenticationRepository,
          authStatus: authenticationBloc.stream
              .toValueListener(authenticationBloc.state),
          authHandler: authHandler,
        ),
      );
      getIt.registerSingleton(internetConnectionStream.stream);
      getIt.registerSingleton(authenticationBloc);
      getIt.registerSingleton<DeviceInfoRepository>(deviceInfoRepository);
      getIt.registerSingleton<SharedPreferencesService>(
        sharedPreferencesService,
      );
      getIt.registerSingleton<ArrivedOnDashboardObserver>(
        arrivedOnDashboardObserver,
      );
      getIt.registerSingleton<FaceDetector>(faceDetector);
      // ignore: deprecated_member_use_from_same_package
      getIt.registerSingleton<PermissionHandler>(permissionHandler);
      getIt.registerSingleton<ClientSettingsRepository>(
        clientSettingsRepository,
      );
      getIt.registerSingleton<SelfReportCubit>(selfReportCubit);
      getIt.registerSingleton<LoginCubit>(loginCubit);
      getIt.registerSingleton<BiometricRepository>(biometricRepository);
      getIt.registerSingleton<ImageCompression>(imageCompression);
      getIt.registerSingleton<LocationRepository>(locationRepository);
      getIt.registerSingleton<BlinkDetector>(blinkDetector);
      getIt.registerSingleton(
        CheckinTimestampRepository(
          sharedPreferencesService: sharedPreferencesService,
        ),
      );
      faceDetectionUtil = MockFaceDetectionUtil();
      getIt.registerFactory<FaceDetectionUtil>(
        () => faceDetectionUtil,
      );
      getIt.registerSingleton<DeviceSetupRepository>(deviceSetupRepository);
      getIt.registerSingleton<L10nAssetLoader>(termsAndConditions);
      getIt.registerFactory<CameraUtil>(() => cameraUtil);
      getIt.registerSingleton<PinRepository>(pinRepository);
      getIt.registerSingleton<BiLogger>(biLogger);
      getIt.registerSingleton<PinResetCubit>(pinResetCubit);
      getIt.registerSingleton<EmbarkAuthenticationRepository>(
        mockEmbarkAuthenticationRepository,
      );
      getIt.registerSingleton<PushNotificationsCubit>(
        pushNotificationsCubit,
      );
      getIt.registerSingleton<BiTokenManager>(
        mockBiTokenManager,
      );
      getIt.registerSingleton<VideoConferenceRepository>(
        MockVideoConferenceRepository(),
      );
      getIt.registerSingleton<EmbarkPermissionPreferences>(
        permissionPreferences,
      );
      getIt.registerSingleton<BiPermissionRequestRepository>(
        permissionRequestRepository,
      );

      when(permissionPreferences.showBlockingPage(any))
          .thenAnswer((_) => false);
    });

    tearDown(() async {
      authHandler.dispose();
      await getIt.reset();
      await authStatusStream.close();
      await internetConnectionStream.close();
    });

    Future<void> loadTestApp(
      WidgetTester widgetTester, {
      void Function()? onPush,
    }) async {
      // test doesn't actually care about this key, just needed to render widgets
      // for the actual test.
      final navKey = GlobalKey<NavigatorState>();
      final observer = RouteObserver<MaterialPageRoute<dynamic>>();

      // here, we don't use the standard .load method because of where/how
      // we want to test this widget. The tests are meant to run as if the contents
      // are rendered above the nav widget, so we need to manually create an app
      // that matches this scenario, as well as provides the mocks as repositories
      // as necessary.
      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: navKey,
          localizationsDelegates: const [
            BiWidgetLocalizations.delegate,
            ...AppLocalizations.localizationsDelegates,
          ],
          navigatorObservers: [
            observer,
            getIt.get<ArrivedOnDashboardObserver>(),
          ],
          onGenerateRoute: (settings) => MaterialPageRoute(
            builder: (_) => const SizedBox.shrink(),
          ),
          builder: (context, child) {
            return ValueListenableBuilder<bool>(
              valueListenable: reset,
              builder: (context, snapshot, _) {
                if (snapshot) {
                  return const SizedBox();
                }
                return Provider.value(
                  value: ResolvedDeviceType.byod,
                  child: _NavigationObserver(
                    observer: observer,
                    onPush: onPush,
                    child: MultiRepositoryProvider(
                      providers: [
                        RepositoryProvider<EmbarkAuthenticationRepository>(
                          create: (_) => mockEmbarkAuthenticationRepository,
                        ),
                        RepositoryProvider<AppSettings>(
                          create: (_) => appSettings,
                        ),
                        BlocProvider<PushNotificationsCubit>(
                          create: (_) => pushNotificationsCubit,
                        ),
                        RepositoryProvider(
                          create: (_) => AuthorizationType.standard,
                        ),
                        BlocProvider<PushNotificationListenerCubit>(
                          create: (_) => pushNotificationListenerCubit,
                        ),
                        BlocProvider<SmartBandSettingsCubit>(
                          create: (_) => smartBandSettingsCubit,
                        ),
                      ],
                      // this is the widget actually being tested.
                      child: EmbarkAuthenticationProvider(
                        navigatorKey: navKey,
                        routes: FakeAuthenticationRoutes(),
                        child: child!,
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      );
    }

    testWidgets(
        'calls postDeviceInfo when user is authenticated and isLogin is true',
        (widgetTester) async {
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );
      when(authenticationRepository.getCurrentUserSession())
          .thenReturn(const User('id', 'some_token'));
      await loadTestApp(widgetTester);
      await widgetTester.runAsync(() async {
        authenticationBloc.add(
          AuthenticationStatusChanged(
            AuthenticationStatusBase.authenticated(
              returnedFromBackground: false,
              isLogin: true,
            ),
          ),
        );
        await widgetTester.idle();
        await widgetTester.pumpAndSettle();
        verify(deviceInfoRepository.postDeviceInfo()).called(1);
        expect(find.byType(DashboardPage), findsNothing);
      });
    });

    testWidgets(
        'should not call postDeviceInfo when user is authenticated and isLogin is false',
        (widgetTester) async {
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );

      await loadTestApp(widgetTester);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false, isLogin: false),
      );
      await widgetTester.pumpAndSettle();
      verifyNever(deviceInfoRepository.postDeviceInfo());
      expect(find.byType(DashboardPage), findsOneWidget);
    });

    testWidgets('shows language page', (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);

      expect(find.byType(LanguagePage), findsNothing);

      authenticationBloc.emit(
        const EmbarkAuthenticationState.languageSelection(),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(LanguagePage), findsOneWidget);
      expect(pushed, isTrue);
    });
    testWidgets('handles widget tree disposal, (throws exception if incorrect)',
        (tester) async {
      await loadTestApp(tester);
      await tester.pump();
      reset.value = true;
      await tester.pump();
      expect(find.byType(EmbarkAuthenticationProvider), findsNothing);
      reset.value = false;
      await tester.pumpAndSettle();
      expect(find.byType(EmbarkAuthenticationProvider), findsOneWidget);
    });

    testWidgets('shows permissions page', (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      expect(find.byType(PermissionsPage), findsNothing);

      authenticationBloc.emit(
        const EmbarkAuthenticationState.permissions(),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(PermissionsPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets('shows Pin validation page for pin validation event',
        (widgetTester) async {
      var pushed = false;
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.mdm,
      );
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: true,
        pinSet: true,
      );
      clientSettingNotifier.value = getClientSettingsResponse;
      // clientSettingsController.add(getClientSettingsResponse);

      await loadTestApp(widgetTester, onPush: () => pushed = true);
      expect(find.byType(PinValidatePage), findsNothing);

      authenticationBloc.emit(
        const EmbarkAuthenticationState.validatePin(),
      );

      await widgetTester.pump();
      expect(find.byType(PinValidatePage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets('should check for voip call after Pin validation page',
        (widgetTester) async {
      const GetPinValidateResponseInfo responseValid =
          GetPinValidateResponseInfo(isValid: true);
      when(pinRepository.validatePin('123456')).thenAnswer((_) async {
        return responseValid;
      });
      await loadTestApp(widgetTester);

      authenticationBloc.emit(const EmbarkAuthenticationState.validatePin());
      await widgetTester.pump();

      final pinEntryField = find.byType(PinEntryView);
      expect(pinEntryField, findsOneWidget);

      await widgetTester.enterText(find.byType(Pinput), '123456');
      await widgetTester.pump();

      verify(pushNotificationListenerCubit.checkVoipCallFromBackground())
          .called(1);
    });

    testWidgets(
        'should check for saved background notification after Pin validation page',
        (widgetTester) async {
      const GetPinValidateResponseInfo responseValid =
          GetPinValidateResponseInfo(isValid: true);
      when(pinRepository.validatePin('123456')).thenAnswer((_) async {
        return responseValid;
      });
      await loadTestApp(widgetTester);

      authenticationBloc.emit(const EmbarkAuthenticationState.validatePin());
      await widgetTester.pump();

      final pinEntryField = find.byType(PinEntryView);
      expect(pinEntryField, findsOneWidget);

      await widgetTester.enterText(find.byType(Pinput), '123456');
      await widgetTester.pump();

      verify(
        pushNotificationListenerCubit
            .checkIfThereIsSavedAppOpenedNotification(),
      ).called(1);
    });

    testWidgets('shows pin set page for pinSet event', (widgetTester) async {
      var pushed = false;
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.mdm,
      );
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: true,
        pinSet: false,
      );
      clientSettingNotifier.value = getClientSettingsResponse;

      await loadTestApp(widgetTester, onPush: () => pushed = true);
      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        const EmbarkAuthenticationState.setPin(),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(PinSetPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets('shows pin reset page for pin reset event',
        (widgetTester) async {
      var pushed = false;
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.mdm,
      );
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: true,
        pinSet: false,
        enrolledFacial: true,
      );
      clientSettingNotifier.value = getClientSettingsResponse;

      // clientSettingsController.add(getClientSettingsResponse);

      await loadTestApp(widgetTester, onPush: () => pushed = true);
      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        const EmbarkAuthenticationState.resetPin(),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(PinResetPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets('shows login page', (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);

      expect(find.byType(LoginPage), findsNothing);

      authenticationBloc.emit(
        const AuthenticationInitial.unauthenticated(),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(LoginPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets(
        'shows dashboard page while in foreground when terms and conditions are accepted',
        (widgetTester) async {
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: true,
      );
      clientSettingNotifier.value = getClientSettingsResponse;

      // clientSettingsController.add(getClientSettingsResponse);
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);

      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets('multiple auths will send you to dashboard once',
        (tester) async {
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: true,
      );
      clientSettingNotifier.value = getClientSettingsResponse;

      // clientSettingsController.add(getClientSettingsResponse);
      var pushed = 0;
      await loadTestApp(tester, onPush: () => pushed++);

      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await tester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      expect(pushed, 1);
      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );
      await tester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      expect(pushed, 1);
    });

    testWidgets('shows check in page while in foreground',
        (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      when(appRepository.getStoredLocale()).thenAnswer(
        (realInvocation) => const Locale('en'),
      );

      expect(find.byType(CheckInPage), findsNothing);
      when(
        sharedPreferencesService.getJson(
          PushNotificationListenerCubit.pendingPushPayLoadPrefsKey,
        ),
      ).thenReturn(
        {'action': PushNotificationType.checkin.value},
      );

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(CheckInPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets('shows self report page while in foreground',
        (widgetTester) async {
      when(selfReportCubit.state).thenReturn(
        SelfReportState(
          status: SelfReportStatus.completed,
          stateMachine: SelfReportStateMachine(const []),
        ),
      );
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      expect(find.byType(SelfReportPage), findsNothing);

      when(
        sharedPreferencesService
            .getJson(PushNotificationListenerCubit.pendingPushPayLoadPrefsKey),
      ).thenReturn({'action': PushNotificationType.report.value});
      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle(const Duration(seconds: 10));
      expect(find.byType(SelfReportPage), findsOneWidget);
      expect(pushed, isTrue);
    });
    testWidgets(
        'goes back to facial enrollment if settings come back that user has been kicked off',
        (tester) async {
      when(authenticationRepository.getCurrentUserSession())
          .thenReturn(someLoggedInUser);
      when(permissionHandler.grantStatus(Permission.location))
          .thenAnswer((_) async => PermissionStatus.granted);
      mdmAuthHandler.needsToShow = false;
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        enrolledFacial: true,
        acceptedTerms: true,
        pinSet: true,
      );
      clientSettingNotifier.value = getClientSettingsResponse;
      await loadTestApp(tester);
      await tester.runAsync<void>(
        () async {
          authenticationBloc.add(
            AuthenticationStatusChanged(
              AuthenticationStatusBase.authenticated(
                returnedFromBackground: false,
              ),
            ),
          );
          await tester.idle();
          await tester.idle();
          verify(authenticationRepository.reevaluateAuthenticationStatus())
              .called(2);

          await tester.pumpAndSettle();
          expect(find.byType(DashboardPage), findsOneWidget);
          expect(find.byKey(UiLookupKeys.enrollmentLandingView), findsNothing);
          clientSettingNotifier.value =
              getClientSettingsResponse.copyWith(enrolledFacial: false);
          authenticationBloc.add(
            AuthenticationStatusChanged(
              AuthenticationStatusBase.authenticated(
                returnedFromBackground: false,
              ),
            ),
          );
          await tester.idle();

          await tester.pumpAndSettle();

          expect(
            find.byKey(UiLookupKeys.enrollmentLandingView),
            findsOneWidget,
          );
          verify(authenticationRepository.reevaluateAuthenticationStatus())
              .called(greaterThan(0));
        },
      );
    });
    testWidgets(
        'shows dashboard page with message tab as starting tab  while in foreground',
        (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      when(
        sharedPreferencesService
            .getJson(PushNotificationListenerCubit.pendingPushPayLoadPrefsKey),
      ).thenReturn({'action': PushNotificationType.message.value});

      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      verify(
        pushNotificationListenerCubit
            .setStartingDashBoardTabId(PluginType.conversation.value),
      ).called(1);
      expect(pushed, isTrue);
    });

    testWidgets(
        'shows dashboard page with calendar tab as starting tab  while in foreground',
        (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      when(
        sharedPreferencesService
            .getJson(PushNotificationListenerCubit.pendingPushPayLoadPrefsKey),
      ).thenReturn({'action': PushNotificationType.calendar.value});

      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      verify(
        pushNotificationListenerCubit
            .setStartingDashBoardTabId(PluginType.calendar.value),
      ).called(1);
      expect(pushed, isTrue);
    });

    testWidgets(
        'shows dashboard page with documents tab as starting tab  while in foreground',
        (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      when(
        sharedPreferencesService
            .getJson(PushNotificationListenerCubit.pendingPushPayLoadPrefsKey),
      ).thenReturn({'action': PushNotificationType.media.value});

      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      verify(
        pushNotificationListenerCubit
            .setStartingDashBoardTabId(PluginType.myDocuments.value),
      ).called(1);
      expect(pushed, isTrue);
    });

    testWidgets('shows dashboard page while in foreground',
        (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);

      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets(
        'shows dashboard page with messages tab as starting tab when tap on background notification',
        (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      when(pushNotificationListenerCubit.startingDashBoardTabId).thenReturn(6);

      expect(find.byType(DashboardPage), findsNothing);

      authenticationBloc.emit(
        AuthenticationInitial.authenticated(User.empty, false),
      );

      await widgetTester.pumpAndSettle();
      expect(find.byType(DashboardPage), findsOneWidget);
      expect(find.byType(DashboardPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets(
        'shows terms and conditions page if device is Byod and state is authenticated with terms and conditions equals to false',
        (widgetTester) async {
      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );

      expect(find.byType(TermsAndConditionsPage), findsNothing);

      authenticationBloc.emit(const EmbarkAuthenticationState.terms());

      await widgetTester.pumpAndSettle();
      expect(find.byType(TermsAndConditionsPage), findsOneWidget);
      expect(pushed, isTrue);
    });

    testWidgets(
        'shows terms and conditions page if terms and conditions status',
        (widgetTester) async {
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: null,
      );
      clientSettingNotifier.value = getClientSettingsResponse;

      var pushed = false;
      await loadTestApp(widgetTester, onPush: () => pushed = true);
      when(deviceSetupRepository.lastKnownDeviceType).thenReturn(
        ResolvedDeviceType.byod,
      );

      expect(find.byType(TermsAndConditionsPage), findsNothing);

      authenticationBloc.emit(const EmbarkAuthenticationState.terms());

      await widgetTester.pumpAndSettle();
      expect(find.byType(TermsAndConditionsPage), findsOneWidget);
      expect(pushed, isTrue);
    });
    testWidgets('got to check in after enrollment', (tester) async {
      await loadTestApp(tester);
      var arrivedOnDashboardObserverCalled = false;
      arrivedOnDashboardObserver.addListener(() {
        arrivedOnDashboardObserverCalled = true;
      });
      const checkInKey = Key(FakeAuthenticationRoutes.checkInTitle);
      authenticationBloc.emit(const EmbarkAuthenticationState.enrollment());
      await tester.pumpAndSettle();
      expect(find.byKey(checkInKey), findsNothing);

      authenticationBloc
          .emit(AuthenticationInitial.authenticated(User.empty, false));
      await tester.pumpAndSettle();
      await tester.idle();
      expect(arrivedOnDashboardObserverCalled, isTrue);
      expect(find.byKey(checkInKey), findsOneWidget);
    });
    for (final notificationsEnabled in [true, false]) {
      testWidgets(
        'does not prompt for notification permission when android is ($notificationsEnabled)',
        (widgetTester) async {
          await loadTestApp(widgetTester);

          authenticationBloc.emit(
            AuthenticationInitial.authenticated(User.empty, false),
          );

          await widgetTester.pumpAndSettle();
          verifyNever(
            pushNotificationsCubit.initializeForegroundNotifications(),
          );
        },
        variant: TargetPlatformVariant.only(TargetPlatform.android),
      );
    }
    for (final notificationsEnabled in [true, false, null]) {
      const getClientSettingsResponse = GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: 'cultureCode',
          cultureName: 'cultureName',
          languageCodeId: 1,
          languageId: 1,
        ),
        acceptedTerms: true,
        pinSet: true,
      );

      testWidgets(
        'iOS always prompts for notification permission regardless of android enabled state ($notificationsEnabled)',
        (widgetTester) async {
          mdmAuthHandler.needsToShow = false;
          clientSettingNotifier.value = getClientSettingsResponse;
          when(deviceSetupRepository.lastKnownDeviceType)
              .thenReturn(ResolvedDeviceType.byod);
          when(authenticationRepository.getCurrentUserSession())
              .thenReturn(const User('id', 'some_token'));
          await loadTestApp(widgetTester);
          authenticationBloc.add(
            AuthenticationStatusChanged(
              AuthenticationStatusBase.authenticated(
                returnedFromBackground: false,
                isLogin: true,
              ),
            ),
          );
          await widgetTester.runAsync(() async {
            await widgetTester.idle();

            await widgetTester.pumpAndSettle();
          });
          verify(
            pushNotificationsCubit.initializeForegroundNotifications(),
          ).called(1);
        },
        variant: TargetPlatformVariant.only(TargetPlatform.iOS),
      );

      testWidgets('makes call to location permission correctly',
          (widgetTester) async {
        await loadTestApp(widgetTester);
        authenticationBloc.emit(const EmbarkAuthenticationState.location());
        verify(authenticationRepository.reevaluateAuthenticationStatus())
            .called(1);

        await widgetTester.pumpAndSettle();
        verify(permissionHandler.request(Permission.location)).called(1);
        verify(authenticationRepository.reevaluateAuthenticationStatus())
            .called(1);
      });
    }
  });
}

class _NavigationObserver extends StatefulWidget {
  final RouteObserver<MaterialPageRoute<dynamic>> observer;
  final Widget child;
  final void Function()? onPush;

  const _NavigationObserver({
    required this.observer,
    this.onPush,
    required this.child,
  });

  @override
  State<StatefulWidget> createState() => _NavigationObserverState();
}

class _NavigationObserverState extends State<_NavigationObserver>
    with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    widget.observer.subscribe(
      this,
      MaterialPageRoute<dynamic>(builder: (_) => const SizedBox.shrink()),
    );
  }

  @override
  void dispose() {
    widget.observer.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPush() {
    widget.onPush?.call();
  }

  @override
  void didPopNext() {
    // Covering route was popped off the navigator.
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

class FakeAuthenticationRoutes implements AuthenticationRoutes {
  static const String checkInTitle = 'check_in';

  @override
  Route<CheckInResult> get checkIn => MaterialPageRoute(
        builder: (context) => const FakeWidgetPage(titleString: checkInTitle),
      );
}

class FakeWidgetPage extends StatelessWidget {
  const FakeWidgetPage({super.key, required this.titleString});

  final String titleString;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BiAppBar(
        titleString: titleString,
        key: Key(titleString),
      ),
    );
  }
}
