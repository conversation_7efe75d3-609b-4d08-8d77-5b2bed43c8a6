import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/starter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  group('ClosableParent', () {
    test('isClosed throws UnimplementedError', () {
      final closableParent = ClosableParent();
      expect(() => closableParent.isClosed, throwsUnimplementedError);
    });
  });
  group(
    'AppRepository',
    () {
      late MockSharedPreferencesService mockSharedPreferencesService;
      late MockRemoteNotificationsService mockRemoteNotificationsService;
      late AppRepository appRepository;
      const expectedLocale = Locale.fromSubtags(languageCode: 'en');
      final AuthenticationStatusBase authenticationStatusBase =
          AuthenticationStatusBase.authenticated(returnedFromBackground: false);
      late BiStreamController<PushPayload> foregroundMessageController;
      // ignore: prefer_final_locals
      Locale currentLocale = expectedLocale;
      setUp(() async {
        getIt.registerSingleton(
          Connectivity(),
          instanceName: 'Connectivity',
        );

        mockSharedPreferencesService = MockSharedPreferencesService();
        mockRemoteNotificationsService = MockRemoteNotificationsService();
        foregroundMessageController = BiStreamController.broadcast();
        when(mockRemoteNotificationsService.foregroundNotifications).thenAnswer(
          (realInvocation) => foregroundMessageController.stream,
        );
        appRepository = AppRepository(
          mockSharedPreferencesService,
          mockRemoteNotificationsService,
          () => currentLocale,
        );
      });

      tearDown(() async {
        await foregroundMessageController.close();
        await getIt.reset();
      });

      test(
        'getStoredLocale returns platform default when no stored value',
        () {
          when(mockSharedPreferencesService.getString(appRepository.localeKey))
              .thenAnswer((realInvocation) => null);

          final locale = appRepository.getStoredLocale();

          expect(locale, expectedLocale);
        },
      );

      test(
        'getStoredLocale returns locale when there is a stored value',
        () {
          when(mockSharedPreferencesService.getString(appRepository.localeKey))
              .thenAnswer((realInvocation) => 'en');

          final locale = appRepository.getStoredLocale();

          expect(locale.languageCode, expectedLocale.languageCode);
        },
      );

      test(
        'setLocale adds value to stream',
        () {
          expect(appRepository.localeStream, emits(expectedLocale));

          appRepository.setLocale(expectedLocale);
        },
      );

      test(
        'authenticationStatusStream emit correct value',
        () {
          expect(
            appRepository.authenticationStatusStream,
            emits(authenticationStatusBase),
          );

          appRepository.authenticationStatusSink.add(authenticationStatusBase);
        },
      );

      test(
        'getTranslateMessages returns value when there is a stored value',
        () {
          when(mockSharedPreferencesService.getBool(any, false))
              .thenAnswer((realInvocation) => true);

          final value = appRepository.getTranslateMessages();

          expect(value, true);
        },
      );

      test(
        'setTranslateMessages sets value',
        () async {
          when(mockSharedPreferencesService.setBool(any, any))
              .thenAnswer((realInvocation) async => true);

          final response = await appRepository.storeTranslateMessages(true);

          expect(response, true);
          verify(mockSharedPreferencesService.setBool(any, any)).called(1);
        },
      );

      test(
        'hasSelectedLanguage returns based on shared preference key existing',
        () {
          when(
            mockSharedPreferencesService.containsKey(appRepository.localeKey),
          ).thenReturn(false);

          expect(appRepository.hasSelectedLanguage, isFalse);

          when(
            mockSharedPreferencesService.containsKey(appRepository.localeKey),
          ).thenReturn(true);

          expect(appRepository.hasSelectedLanguage, isTrue);
        },
      );

      test(
        'setLocale updates intl defaultLocale and emits',
        () {
          expect(
            appRepository.localeStream,
            emitsInOrder(
              [
                // from the setLocale call
                const Locale('ht'),
              ],
            ),
          );

          appRepository.setLocale(const Locale('ht'));
        },
      );

      test('message to remote notification causes foreground message',
          () async {
        final message = PushPayload(
          title: 'title',
        );

        appRepository.foregroundNotificationsStream.listen((payload) {
          expect(payload, message);
        });
        foregroundMessageController.sink.add(message);
      });

      test('setAcceptedPermissions updates shared preferences', () async {
        when(mockSharedPreferencesService.setBool('permissions', true))
            .thenAnswer((realInvocation) async => true);
        final response = await appRepository.setAcceptedPermissions(true);
        expect(response, true);
        verify(mockSharedPreferencesService.setBool('permissions', true))
            .called(1);
      });

      test('getAcceptedPermissions returns shared preferences value', () async {
        when(mockSharedPreferencesService.getBool('permissions', false))
            .thenReturn(true);
        final response = appRepository.getAcceptedPermissions();
        expect(response, true);
        verify(mockSharedPreferencesService.getBool('permissions', false))
            .called(1);
      });

      test('starting tab index', () async {
        expect(appRepository.getStartingTabIndex(), 2);
      });

      test('setTranslatedMessage updates stream', () async {
        appRepository.translateMessagesStream.listen((value) {
          expect(value, true);
        });
        appRepository.setTranslateMessages(true);
      });

      test('setAppLifecycleState updates stream', () async {
        appRepository.appLifecycleStateStream.listen((state) {
          expect(state, AppLifecycleState.resumed);
        });
        appRepository.setAppLifecycleState(AppLifecycleState.resumed);
      });

      test('setShowTranslationMessage updates shared preferences', () async {
        when(
          mockSharedPreferencesService.setBool(
            'showTranslationMessage',
            true,
          ),
        ).thenAnswer((realInvocation) async => true);
        final response = await appRepository.setShowTranslationMessage(true);
        expect(response, true);
        verify(
          mockSharedPreferencesService.setBool(
            'showTranslationMessage',
            true,
          ),
        ).called(1);
      });

      test('getShowTranslationMessage returns shared preferences value',
          () async {
        when(
          mockSharedPreferencesService.getBool(
            'showTranslationMessage',
            true,
          ),
        ).thenReturn(false);
        final response = appRepository.getShowTranslationMessage();
        expect(response, false);
        verify(
          mockSharedPreferencesService.getBool(
            'showTranslationMessage',
            true,
          ),
        ).called(1);
      });

      test('containsTranslateMessagesKey returns shared preferences value',
          () async {
        when(mockSharedPreferencesService.containsKey('translateMessages'))
            .thenReturn(true);
        final response = appRepository.containsTranslateMessagesKey();
        expect(response, true);
        verify(mockSharedPreferencesService.containsKey('translateMessages'))
            .called(1);
      });

      test('test currentLocaleIsEnglish true', () async {
        when(mockSharedPreferencesService.getString('locale')).thenReturn('en');
        final response = appRepository.currentLocaleIsEnglish();
        expect(response, true);
        verify(mockSharedPreferencesService.getString('locale')).called(2);
      });

      test('test currentLocaleIsEnglish false', () async {
        when(mockSharedPreferencesService.getString('locale')).thenReturn('ht');
        final response = appRepository.currentLocaleIsEnglish();
        expect(response, false);
        verify(mockSharedPreferencesService.getString('locale')).called(2);
      });

      test('adding message to foregroundNotificationsSink', () async {
        final message = PushPayload(
          title: 'title',
        );
        appRepository.foregroundNotificationsStream.listen((payload) {
          expect(payload, message);
        });
        appRepository.foregroundNotificationsSink.add(message);
      });

      test('loadL10n', () async {
        final response = await appRepository.loadL10n();
        expect(response, isA<AppLocalizationsEn>());
        verify(mockSharedPreferencesService.getString('locale')).called(2);
      });

      test('close', () async {
        var foreGroundNotificationsStreamClosed = false;
        var appLifecycleStateStreamClosed = false;
        var translateMessagesStreamClosed = false;

        appRepository.foregroundNotificationsStream.listen(
          (payload) {},
          onDone: () {
            foreGroundNotificationsStreamClosed = true;
          },
        );

        appRepository.appLifecycleStateStream.listen(
          (state) {},
          onDone: () {
            appLifecycleStateStreamClosed = true;
          },
        );

        appRepository.translateMessagesStream.listen(
          (value) {},
          onDone: () {
            translateMessagesStreamClosed = true;
          },
        );

        await appRepository.close();
        expect(foreGroundNotificationsStreamClosed, true);
        expect(appLifecycleStateStreamClosed, true);
        expect(translateMessagesStreamClosed, true);
      });

      test('isClosed throws UnimplementedError', () async {
        expect(() => appRepository.isClosed, throwsUnimplementedError);
      });
    },
  );
}
