import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/location/_location_permission_status.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../unit_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  const MethodChannel channel =
      MethodChannel('flutter.baseflow.com/permissions/methods');

  group('DeviceInfoRepository', () {
    late MockSmartLinkAppSettingsHttpClient mockSmartLinkAppSettingsHttpClient;
    late MockLocationRepository mockLocationRepository;
    late MockBatteryRepository mockBatteryRepository;
    late MockAppRepository mockAppRepository;
    late MockInternetConnectivityRepository mockInternetConnectivityRepository;
    late MockPushNotificationsRepository mockPushNotificationsRepository;
    late MockBiLogger mockBiLogger;
    late MockDeviceInfoPlugin mockDeviceInfoPlugin;
    late MockAndroidDeviceInfo mockAndroidDeviceInfo;
    late MockAndroidBuildVersion mockAndroidBuildVersion;
    late MockBiPackageInfo mockBiPackageInfo;
    late DeviceInfoRepository deviceInfoRepository;

    final testPositionFix = PositionFix(
      fixTypeId: 1,
      locationServicesEnabled: false,
      position: Position(
        accuracy: null,
        cellStrength: null,
        hdop: 0,
        heading: null,
        latitude: null,
        longitude: null,
        satellites: null,
        speed: null,
        timestamp: null,
      ),
      positionAcquired: false,
    );

    final testDeviceInformationRequest = DeviceInformationRequest(
      model:
          'Dummy_String_created_while_calling_model_on_MockAndroidDeviceInfo.',
      osVersion: '13',
      platform: 'Android',
      batteryLevel: 0,
      batteryCharging: false,
      positionFix: testPositionFix,
      pushNotificationsEnabled: true,
      culturalCode: 'en-US',
      usingMobileData: false,
      usingWiFi: false,
      appVersion: 'version',
    );

    setUp(() {
      mockSmartLinkAppSettingsHttpClient = MockSmartLinkAppSettingsHttpClient();
      mockLocationRepository = MockLocationRepository();
      mockBatteryRepository = MockBatteryRepository();
      mockAppRepository = MockAppRepository();
      mockInternetConnectivityRepository = MockInternetConnectivityRepository();
      mockPushNotificationsRepository = MockPushNotificationsRepository();
      mockBiLogger = MockBiLogger();
      mockDeviceInfoPlugin = MockDeviceInfoPlugin();
      mockAndroidDeviceInfo = MockAndroidDeviceInfo();
      mockAndroidBuildVersion = MockAndroidBuildVersion();
      mockBiPackageInfo = MockBiPackageInfo();

      deviceInfoRepository = DeviceInfoRepository(
        mockSmartLinkAppSettingsHttpClient,
        mockLocationRepository,
        mockBatteryRepository,
        mockAppRepository,
        mockInternetConnectivityRepository,
        mockPushNotificationsRepository,
        mockDeviceInfoPlugin,
        mockBiPackageInfo,
        mockBiLogger,
      );

      when(mockLocationRepository.requestLocationPermission()).thenAnswer(
        (realInvocation) => Future.value(LocationPermissionStatus.enabled),
      );

      when(mockLocationRepository.getLastKnownLocation())
          .thenAnswer((_) async => null);

      when(mockLocationRepository.serviceEnabled())
          .thenAnswer((_) async => false);

      when(mockInternetConnectivityRepository.reachability).thenAnswer(
        (realInvocation) => Future.value([ConnectivityResult.none]),
      );

      when(mockBatteryRepository.isBatteryCharging())
          .thenAnswer((_) async => false);

      when(mockBatteryRepository.getBatteryLevel()).thenAnswer((_) async => 0);

      when(mockPushNotificationsRepository.arePushNotificationsEnabled())
          .thenReturn(true);

      when(mockAppRepository.getStoredLocale())
          .thenReturn(const Locale('en-US'));

      when(mockDeviceInfoPlugin.androidInfo).thenAnswer(
        (_) async => mockAndroidDeviceInfo,
      );

      when(mockAndroidDeviceInfo.version).thenReturn(mockAndroidBuildVersion);

      when(mockAndroidBuildVersion.release).thenReturn('13');

      when(mockAndroidDeviceInfo.model).thenReturn(
        'Dummy_String_created_while_calling_model_on_MockAndroidDeviceInfo.',
      );

      when(mockBiPackageInfo.package).thenReturn(
        PackageInfo(
          appName: 'appName',
          buildNumber: '5016',
          packageName: 'packageName',
          version: 'version',
        ),
      );
      // Updated to use the new recommended method
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        channel, // Pass the channel as the first argument
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermissionStatus':
              return PermissionStatus.granted.index;
            default:
              return null;
          }
        },
      );
    });
    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);
    });
    group('device info', () {
      test('deviceInfoRepository calls postDeviceInformation', () async {
        dynamic value;
        when(
          mockSmartLinkAppSettingsHttpClient.postDeviceInformation(
            any,
          ),
        ).thenAnswer((realInvocation) async {
          value = realInvocation.positionalArguments.first;
          return ApiResponse(
            httpStatusCode: 200,
            apiCode: 0,
            apiMessage: 'apiMessage',
            originalUri: Uri(),
            httpMethod: 'POST',
          );
        });

        await deviceInfoRepository.postDeviceInfo();
        verify(mockBatteryRepository.isBatteryCharging());
        verify(mockBatteryRepository.getBatteryLevel());
        verify(mockLocationRepository.serviceEnabled());
        verify(mockLocationRepository.getLastKnownLocation());
        verify(mockInternetConnectivityRepository.reachability);
        verify(mockPushNotificationsRepository.arePushNotificationsEnabled());
        verify(mockAppRepository.getStoredLocale());
        verify(mockDeviceInfoPlugin.androidInfo);
        verify(mockSmartLinkAppSettingsHttpClient.postDeviceInformation(any));

        expect(value, equals(testDeviceInformationRequest));
      });
      test('does not post 0 values for position, position is uploaded as null',
          () async {
        late DeviceInformationRequest value;
        when(
          mockSmartLinkAppSettingsHttpClient.postDeviceInformation(
            any,
          ),
        ).thenAnswer((realInvocation) async {
          value = realInvocation.positionalArguments.first
              as DeviceInformationRequest;
          return ApiResponse(
            httpStatusCode: 200,
            apiCode: 0,
            apiMessage: 'apiMessage',
            originalUri: Uri(),
            httpMethod: 'POST',
          );
        });
        when(mockLocationRepository.requestLocationPermission()).thenAnswer(
          (realInvocation) => Future.value(LocationPermissionStatus.denied),
        );

        await deviceInfoRepository.postDeviceInfo();
        verify(mockBatteryRepository.isBatteryCharging());
        verify(mockBatteryRepository.getBatteryLevel());
        verify(mockLocationRepository.serviceEnabled());
        verify(mockInternetConnectivityRepository.reachability);
        verify(mockPushNotificationsRepository.arePushNotificationsEnabled());
        verify(mockAppRepository.getStoredLocale());
        verify(mockDeviceInfoPlugin.androidInfo);
        verify(mockSmartLinkAppSettingsHttpClient.postDeviceInformation(any));

        expect(
          value.positionFix,
          PositionFix(
            fixTypeId: 1,
            locationServicesEnabled: false,
            positionAcquired: false,
            position: null,
          ),
        );
      });
    });

    test(
        'returns PositionFix with locationServicesEnabled=false if permission is denied and location services are disabled',
        () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermissionStatus') {
            return PermissionStatus.denied.index;
          }
          return null;
        },
      );

      when(mockLocationRepository.serviceEnabled())
          .thenAnswer((_) async => false);

      final result = await deviceInfoRepository.getPositionFix();

      expect(result.locationServicesEnabled, false);
      expect(result.positionAcquired, false);
    });

    test(
        'returns PositionFix with locationServicesEnabled=false if permission is denied and location services are enabled',
        () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermissionStatus') {
            return PermissionStatus.denied.index;
          }
          return null;
        },
      );
      when(mockLocationRepository.requestLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.denied);

      when(mockLocationRepository.serviceEnabled())
          .thenAnswer((_) async => true);

      final result = await deviceInfoRepository.getPositionFix();

      expect(result.locationServicesEnabled, false);
      expect(result.positionAcquired, false);
    });

    test(
        'returns PositionFix with locationServicesEnabled=true if permission is enabled and location services are enabled',
        () async {
      when(mockLocationRepository.requestLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.enabled);
      when(mockLocationRepository.serviceEnabled())
          .thenAnswer((_) async => true);

      final result = await deviceInfoRepository.getPositionFix();

      expect(result.locationServicesEnabled, true);
    });

    test(
        'returns PositionFix with default location if permission is enabled and position is null',
        () async {
      when(mockLocationRepository.requestLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.enabled);
      when(mockLocationRepository.getLastKnownLocation())
          .thenAnswer((_) async => null);
      when(mockLocationRepository.serviceEnabled())
          .thenAnswer((_) async => true);

      final result = await deviceInfoRepository.getPositionFix();

      expect(result.locationServicesEnabled, true);
      expect(result.positionAcquired, false);
      verify(
        mockBiLogger.info(
          message: 'Failed to get position fix',
          debugProps: {
            'locationPermission': 'LocationPermissionStatus.enabled',
            'locationServicesEnabled': true,
          },
        ),
      ).called(1);
    });

    test(
        'returns PositionFix with acquired position if permission is enabled and position is not null',
        () async {
      final position = Position(
        latitude: 1,
        longitude: 1,
        accuracy: 1,
        heading: 1,
        speed: 1,
        timestamp: DateTime.now().toString(),
      );
      when(mockLocationRepository.requestLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.enabled);
      when(mockLocationRepository.getLastKnownLocation()).thenAnswer(
        (realInvocation) => Future.value(
          geo.Position(
            longitude: 1,
            latitude: 1,
            timestamp: DateTime.now(),
            accuracy: 1,
            altitude: 1,
            altitudeAccuracy: 1,
            heading: 1,
            headingAccuracy: 1,
            speed: 1,
            speedAccuracy: 1,
          ),
        ),
      );
      when(mockLocationRepository.serviceEnabled())
          .thenAnswer((_) async => true);

      final result = await deviceInfoRepository.getPositionFix();

      expect(result.locationServicesEnabled, true);
      expect(result.positionAcquired, true);
      expect(result.position!.latitude, position.latitude);
      expect(result.position!.longitude, position.longitude);
      expect(result.position!.accuracy, position.accuracy);
      expect(result.position!.heading, position.heading);
      expect(result.position!.speed, position.speed);
    });
  });
}
