import 'dart:async';

import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/push_notifications/cubit/push_notifications_cubit.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/push_notifications_services/_push_notifications_token_service.dart';
import 'package:embark/starter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  group('PushNotificationsCubit', () {
    late PushNotificationsCubit pushNotificationsCubit;
    late MockPushNotificationsRepository mockPushNotificationsRepository;
    late MockInternetConnectivityRepository mockInternetConnectivityRepository;
    late MockPushNotificationsTokenSerivce mockPushNotificationsTokenSerivce;
    late MockBiLogger mockLogger;

    const somePushToken = 'somepushtoken';

    setUp(() async {
      mockPushNotificationsRepository = MockPushNotificationsRepository();
      mockInternetConnectivityRepository = MockInternetConnectivityRepository();
      mockPushNotificationsTokenSerivce = MockPushNotificationsTokenSerivce();
      mockLogger = MockBiLogger();

      // Clear previous registrations
      await getIt.reset();

      // Register mocks with GetIt
      getIt.registerSingleton<InternetConnectivityRepository>(
        mockInternetConnectivityRepository,
      );
      getIt.registerSingleton<PushNotificationsTokenSerivce>(
        mockPushNotificationsTokenSerivce,
      );

      pushNotificationsCubit = PushNotificationsCubit(
        mockPushNotificationsRepository,
        mockPushNotificationsTokenSerivce,
        mockLogger,
      );
    });

    void withToken({required String newToken}) {
      when(mockPushNotificationsTokenSerivce.getFirebaseMessagingToken())
          .thenAnswer((_) async => newToken);
    }

    void withConnectivity(List<ConnectivityResult> connectivity) {
      when(mockInternetConnectivityRepository.reachability)
          .thenAnswer((_) async => connectivity);
    }

    test(
        'initializeForegroundNotifications called and permissions have been requested',
        () async {
      await pushNotificationsCubit.init();

      verify(
        mockPushNotificationsRepository.initializeForegroundNotifications(),
      ).called(1);
    });

    group('initialize foreground notifications', () {
      test('prevents calling initialize notification before future is returned',
          () async {
        final completer = Completer<void>();
        when(
          mockPushNotificationsRepository.initializeForegroundNotifications(),
        ).thenAnswer((realInvocation) {
          return completer.future;
        });
        final first =
            pushNotificationsCubit.initializeForegroundNotifications();
        when(
          mockPushNotificationsRepository.initializeForegroundNotifications(),
        ).thenAnswer((realInvocation) async {});
        final second =
            pushNotificationsCubit.initializeForegroundNotifications();
        await Future.microtask(() {});
        completer.complete();
        await Future.wait([first, second]);

        verify(
          mockPushNotificationsRepository.initializeForegroundNotifications(),
        ).called(1);
      });
    });

    test(
        'multiple requests to requestAndRegisterPushNotifications only call a single time',
        () async {
      withConnectivity([ConnectivityResult.mobile]);
      withToken(newToken: 'new_mock_token');
      final completer = Completer<ApiResponse>();
      final wait = Completer<void>();
      // Mock the repository's response for registration
      when(mockPushNotificationsRepository.registerForNotifications(any))
          .thenAnswer((_) {
        wait.complete();
        return completer.future;
      });

      unawaited(pushNotificationsCubit.requestAndRegisterPushNotifications());
      unawaited(pushNotificationsCubit.requestAndRegisterPushNotifications());
      unawaited(pushNotificationsCubit.requestAndRegisterPushNotifications());

      await wait.future;
      completer.complete(ApiResponse.ok());
      await pushNotificationsCubit.requestAndRegisterPushNotifications();
      verify(mockPushNotificationsRepository.registerForNotifications(any))
          .called(1);
    });

    testWidgets(
      'registers correctly as smartlink',
      (_) async {
        withConnectivity([ConnectivityResult.mobile]);
        withToken(newToken: somePushToken);

        NotificationRegistrationRequest? sentRequest;
        when(mockPushNotificationsRepository.registerForNotifications(any))
            .thenAnswer((realInvocation) async {
          sentRequest = realInvocation.positionalArguments.first
              as NotificationRegistrationRequest;
          return Future.value(ApiResponse.ok());
        });

        await pushNotificationsCubit.requestAndRegisterPushNotifications();

        final expectedPlatform = defaultTargetPlatform == TargetPlatform.android
            ? BiPushPlatforms.fcm.value
            : BiPushPlatforms.apns.value;

        expect(sentRequest, isNotNull);
        expect(sentRequest!.appType, equals(PushNotificationAppId.smartlink));
        expect(sentRequest!.handle, equals(somePushToken));
        expect(sentRequest!.platform, equals(expectedPlatform));
      },
      variant: const TargetPlatformVariant(
        {
          TargetPlatform.android,
          TargetPlatform.iOS,
        },
      ),
    );
  });
}
