import 'dart:async';

import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:embark/profile/views/_self_report_module_page.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../widget_test_helpers/golden_wrapper.dart';

void main() {
  late StreamController<List<ConnectivityResult>> internetStream;
  late MockSelfReportCubit selfReportCubit;

  void registerGetItDependencies() {
    getIt.registerSingleton(internetStream.stream);
    getIt.registerSingleton<SelfReportCubit>(selfReportCubit);
  }

  setUp(() {
    internetStream = StreamController();
    selfReportCubit = MockSelfReportCubit();

    registerGetItDependencies();
  });

  tearDown(() {
    internetStream.close();
    getIt.reset();
  });

  testWidgets('does not load self report questions', (tester) async {
    await tester.load(
      widget: const ProfileSelfReportModulePage(
        title: 'title',
        childWidget: SizedBox.shrink(),
      ),
    );

    verify(
      selfReportCubit.init(
        l10n: anyNamed('l10n'),
        loadQuestions: false,
      ),
    ).called(1);
  });

  testWidgets('passes locale to init', (tester) async {
    await tester.load(
      locale: const Locale('es'),
      widget: const ProfileSelfReportModulePage(
        title: 'title',
        childWidget: SizedBox.shrink(),
      ),
    );

    final assertion = verify(
      selfReportCubit.init(
        l10n: captureAnyNamed('l10n'),
        loadQuestions: anyNamed('loadQuestions'),
      ),
    )..called(1);
    final l10n = assertion.captured.first as AppLocalizations;
    expect(l10n.localeName, equals('es'));
  });

  testWidgets('uses safe area', (tester) async {
    await tester.load(
      locale: const Locale('es'),
      widget: const ProfileSelfReportModulePage(
        title: 'title',
        childWidget: SizedBox.shrink(),
      ),
    );

    expect(
      tester.widget<EmbarkScaffold>(find.byType(EmbarkScaffold)).body,
      isA<SafeArea>(),
    );
  });

  testGoldens('renders correctly', (tester) async {
    await tester.pumpWidgetBuilder(
      const ProfileSelfReportModulePage(
        title: 'title',
        childWidget: Center(
          child: Text('child'),
        ),
      ),
      wrapper: goldenWrapper,
      surfaceSize: const Size(200, 200),
    );

    await screenMatchesGolden(tester, 'profile self report page');
  });
}
