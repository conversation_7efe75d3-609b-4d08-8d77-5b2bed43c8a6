import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/address_change_request/cubit/_address_change_request_cubit.dart';
import 'package:embark/address_change_request/cubit/_address_change_request_state.dart';
import 'package:embark/address_change_request/views/_address_change_request_form.dart';
import 'package:embark/profile/views/responses/_profile_residence_info_response.dart';
import 'package:embark/repositories/_client_settings_repository.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('SelfReportAddressResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late MockAddressChangeRequestCubit mockAddressChangeRequestCubit;
    late MockClientSettingsRepository clientSettingsRepository;

    final rules = AddressRules(
      // only used for testing that changes to the form bubble up.
      address1MaxLength: 255,
      address2MaxLength: 1,
      address3MaxLength: 1,
      addressTypes: [],
      bedroomsMaxValue: 1,
      cityMaxLength: 1,
      commentMaxLength: 1,
      countyMaxLength: 1,
      homeZoneIdMinValue: 1,
      homeZoneIdMaxValue: 1,
      requiredFields: [],
      residenceTypes: [],
      stateMaxLength: 1,
      states: [
        // Needed for form to auto-select a state
        StateProvince(countryCode: 1, state: 'WA', stateId: 1, stateName: ''),
      ],
      timeZones: [
        // Needed for form to auto-select a timezone
        IdWithName(id: 1, name: 'PST'),
      ],
      postalCodeMask: '',
      postalCodeMaxLength: 1,
      countryCode: 1,
      countyLabel: '',
      postalCodeLabel: '',
      stateOrProvinceLabel: '',
    );

    late BiStreamController<AddressChangeRequestState> addressChangeCubitStream;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    setUp(() async {
      mockSelfReportCubit = MockSelfReportCubit();
      mockAddressChangeRequestCubit = MockAddressChangeRequestCubit();
      addressChangeCubitStream = BiStreamController.broadcast();

      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<AddressChangeRequestCubit>(
        mockAddressChangeRequestCubit,
      );

      clientSettingsRepository = MockClientSettingsRepository();
      getIt.registerSingleton<ClientSettingsRepository>(
        clientSettingsRepository,
      ); // Register ClientSettingsRepository with GetIt

      var currentState = AddressChangeRequestState.initial();

      when(mockAddressChangeRequestCubit.stream)
          .thenAnswer((_) => addressChangeCubitStream.stream);

      when(mockAddressChangeRequestCubit.emit(any)).thenAnswer(
        (realInvocation) {
          currentState = realInvocation.positionalArguments.first
              as AddressChangeRequestState;
          addressChangeCubitStream.add(currentState);
        },
      );

      when(mockAddressChangeRequestCubit.state)
          .thenAnswer((realInvocation) => currentState);

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(() async {
      await getIt.reset();
      await addressChangeCubitStream.close();
      await iCSController.close();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: BlocProvider<SelfReportCubit>(
          create: (_) => mockSelfReportCubit,
          child: const ProfileResidenceInfoResponse(),
        ),
      );

      mockAddressChangeRequestCubit
          .emit(AddressChangeRequestState.loaded(rules, null));

      await tester.pump();
    }

    Future<void> tapButton(
      WidgetTester tester, {
      required Finder finder,
    }) async {
      // grab the last scrollable widget so that we know we are scolling the most
      // inner view (form fields): https://stackoverflow.com/a/73881623
      final scrollable = find.byType(Scrollable).first;

      final buttonFinder = find.descendant(
        of: find.byType(ListView),
        matching: finder,
      );
      await tester.scrollUntilVisible(
        buttonFinder,
        30,
        scrollable: scrollable,
      );
      await tester.pump();
      await tester.tap(buttonFinder);

      // run tap animation
      await tester.pump();
    }

    Future<void> tapContinue(WidgetTester tester) async => tapButton(
          tester,
          finder: find.byType(BiPositiveButton),
        );

    Future<void> tapCancel(WidgetTester tester) async => tapButton(
          tester,
          finder: find.byType(BiNegativeButton),
        );

    testWidgets('renders correct form', (tester) async {
      await loadWidget(tester);

      expect(
        find.descendant(
          of: find.byType(ProfileResidenceInfoResponse),
          matching: find.byType(AddressChangeRequestForm),
        ),
        findsOneWidget,
      );
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);

      await tapCancel(tester);

      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });

    testWidgets('continues form correctly when no inputs changed',
        (tester) async {
      await loadWidget(tester);

      await tapContinue(tester);

      verify(
        mockSelfReportCubit.saveAddress(
          AddressResponse.empty().copyWith(
            // auto-populated values
            countryCode: rules.countryCode,
          ),
          submitChangesLive: true,
        ),
      ).called(1);

      await tester.pumpAndSettle(const Duration(seconds: 5));
    });

    testWidgets('continues form correctly when inputs changed', (tester) async {
      await loadWidget(tester);

      const inputText = 'address 1';

      await tester.enterText(find.byType(TextFormField).first, inputText);

      // let the text input settle and the keyboard dismiss.
      await tester.pump();

      await tapContinue(tester);

      final expectedForm = AddressResponse.empty().copyWith(
        // the value we expected to change
        address1: inputText,
        // auto-populated values
        countryCode: rules.countryCode,
      );

      verify(
        mockSelfReportCubit.saveAddress(expectedForm, submitChangesLive: true),
      ).called(1);

      await tester.pumpAndSettle(const Duration(seconds: 5));
    });
  });
}
