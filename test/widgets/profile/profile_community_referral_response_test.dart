import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/profile/views/responses/_profile_community_referral_response.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/self_report/models/models.dart';
import 'package:embark/self_report/self_report.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:widgets/widgets.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';
import '../embark_scaffold/bi_embark_drawer_test.dart';

void main() {
  group('ProfileCommunityReferralResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late MockCommunityReferralRepository mockCommunityReferralRepository;
    late CommunityReferral testReferral;
    late List<CommunityReferralChange> referralChanges;
    late List<CommunityReferral> referrals;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    setUp(() async {
      testReferral = CommunityReferral(
        id: 1,
        name: 'Test Referral Name 1',
        referralStatus: 'Pending',
      );

      referrals = [
        CommunityReferral(
          id: 1,
          name: 'Test Referral Name 1',
          referralStatus: 'Pending',
        ),
        CommunityReferral(
          id: 2,
          name: 'Test Referral Name 2',
          referralStatus: 'Ongoing',
        ),
        CommunityReferral(
          id: 3,
          name: 'Test Referral Name 3',
          referralStatus: 'Utilized',
        ),
      ];

      referralChanges = [
        CommunityReferralChange(
          DataOperationType.updated,
          data: referrals[0].copyWith(referralStatus: 'Ongoing'),
        ),
        CommunityReferralChange(
          DataOperationType.updated,
          data: referrals[1].copyWith(referralStatus: 'Utilized'),
        ),
      ];

      mockSelfReportCubit = MockSelfReportCubit();
      mockCommunityReferralRepository = MockCommunityReferralRepository();
      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<CommunityReferralRepository>(
        mockCommunityReferralRepository,
      );

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(() async {
      await getIt.reset();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.mdm,
          child: BlocProvider<SelfReportCubit>(
            create: (_) => mockSelfReportCubit,
            child: const ProfileCommunityReferralResponse(),
          ),
        ),
      );
      await tester.pumpAndSettle();
      await iCSController.close();
    }

    testWidgets('renders referral summary card', (tester) async {
      await loadWidget(tester);

      expect(
        find.byType(ProfileCommunityReferralResponse),
        findsOneWidget,
      );
    });

    testWidgets('renders referral summary tiles for 2 items', (tester) async {
      when(mockCommunityReferralRepository.fetchCommunityReferrals())
          .thenAnswer(
        (_) => Future(
          () => referralChanges,
        ),
      );

      await loadWidget(tester);

      expect(
        find.byType(ListTile),
        findsNWidgets(2),
      );
    });

    testWidgets('renders referral with item which can be edited',
        (tester) async {
      when(mockCommunityReferralRepository.fetchCommunityReferrals())
          .thenAnswer(
        (_) => Future(
          () => [
            CommunityReferralChange(
              DataOperationType.updated,
              data: testReferral,
            ),
          ],
        ),
      );

      await loadWidget(tester);

      expect(find.byType(BIListViewTile), findsOneWidget);
      expect(find.byType(BIListViewTrailingIconButton), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);
      await tester.tap(find.text(l10n.cancel));
      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });
  });
}
