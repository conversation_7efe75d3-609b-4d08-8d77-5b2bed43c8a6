import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/dashboard/cubit/_dashboard_cubit.dart';
import 'package:embark/dashboard/cubit/_dashboard_state.dart';
import 'package:embark/profile/cubit/cubit.dart';
import 'package:embark/profile/views/views.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:widgets/widgets.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('ProfileView', () {
    late MockProfileCubit profileCubit;
    late DashboardCubit dashboardCubit;
    late BiLogger logger;
    late BiStreamController<DashboardState> dashboardStateStreamController;
    late Stream<List<ConnectivityResult>> connectionStatus;
    late BiStreamController<List<ConnectivityResult>> iCSStreamController;
    late MockDeviceInfoRepository deviceInfoRepository;
    late MockClientSettingsRepository clientSettingsRepository;
    late LocationRepository locationRepository;

    setUp(() async {
      profileCubit = MockProfileCubit();
      dashboardCubit = MockDashboardCubit();
      dashboardStateStreamController = BiStreamController<DashboardState>();

      iCSStreamController = BiStreamController<List<ConnectivityResult>>();
      connectionStatus = iCSStreamController.stream;
      deviceInfoRepository = MockDeviceInfoRepository();
      clientSettingsRepository = MockClientSettingsRepository();
      locationRepository = MockLocationRepository();

      getIt.registerSingleton<LocationRepository>(locationRepository);
      getIt.registerSingleton<ClientSettingsRepository>(
        clientSettingsRepository,
      ); // Register ClientSettingsRepository with GetIt

      when(clientSettingsRepository.clientSettings).thenAnswer(
        (_) => Stream.value(
          const GetClientSettingsResponse(
            enrolledGeneralChangeRequest: true,
            preferredLanguage: PreferredLanguageResponse(
              cultureCode: '',
              cultureName: '',
              languageCodeId: 0,
              languageId: 0,
            ),
          ),
        ),
      );

      when(profileCubit.state)
          .thenAnswer((_) => const ProfileState(status: ProfileStatus.initial));

      when(dashboardCubit.state).thenAnswer((_) => const DashboardState());
      when(dashboardCubit.stream)
          .thenAnswer((_) => dashboardStateStreamController.stream);

      getIt.registerSingleton<BiLogger>(MockBiLogger());
      logger = getIt.get<BiLogger>();

      final MockRemoteNotificationsService mockRemoteNotificationsService =
          MockRemoteNotificationsService();
      getIt.registerSingleton(
        mockRemoteNotificationsService,
        instanceName: 'RemoteNotificationsService',
      );
      when(mockRemoteNotificationsService.foregroundNotifications)
          .thenAnswer((_) => const Stream.empty());
      when(mockRemoteNotificationsService.requestPermission())
          .thenAnswer((_) => Future(() => true));
      when(mockRemoteNotificationsService.initializeLocalNotifications())
          .thenAnswer((_) => Future(() => FlutterLocalNotificationsPlugin()));

      getIt.registerSingleton<SharedPreferencesService>(
        MockSharedPreferencesService(),
      );
      final Connectivity internetConnectionChecker = MockConnectivity();
      getIt.registerSingleton(
        internetConnectionChecker,
        instanceName: 'Connectivity',
      );
      when(internetConnectionChecker.onConnectivityChanged).thenAnswer(
        (_) => Stream<List<ConnectivityResult>>.value(
          [ConnectivityResult.mobile],
        ),
      );

      getIt.registerSingleton(
        AppRepository(
          getIt.get<SharedPreferencesService>(),
          getIt.get<MockRemoteNotificationsService>(
            instanceName: 'RemoteNotificationsService',
          ),
          () => const Locale('en'),
        ),
        instanceName: 'AppRepository',
      );

      getIt.registerSingleton<ProfileCubit>(profileCubit);
      getIt.registerSingleton<DeviceInfoRepository>(deviceInfoRepository);
    });

    tearDown(() async {
      await getIt.reset();
      await dashboardStateStreamController.close();
      await iCSStreamController.close();
    });

    Future<void> loadWidget(
      WidgetTester tester,
      ProfileView widget,
    ) async {
      await tester.load(
        widget: BiEmbarkBlocProvider<ProfileCubit, ProfileState>(
          create: (_) => profileCubit,
          internetConnectivityStream: connectionStatus,
          child: widget,
        ),
      );
    }

    testWidgets('finds profile view', (tester) async {
      await loadWidget(
        tester,
        ProfileView(
          logger: logger,
        ),
      );

      final profileView = find.descendant(
        of: find.byType(Scaffold),
        matching: find.byType(ProfileView),
      );

      expect(profileView, findsOneWidget);
    });
  });
}
