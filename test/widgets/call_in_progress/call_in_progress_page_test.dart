import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/call_in_progress/cubit/call_in_progress_cubit.dart';
import 'package:embark/call_in_progress/views/_call_in_progress_page.dart';
import 'package:embark/dashboard/cubit/_dashboard_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/src/widgets/basic.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';

import '../../unit_test.mocks.dart';
import '../widget_test_helpers/golden_wrapper.dart';

void main() {
  group('CallInProgressPage Golden Test', () {
    late BiStreamController<List<ConnectivityResult>> connectivityStream;
    late MockVoIPCallRepository mockVoIPCallRepository;
    late MockTwilioDialer mockTwilioDialer;
    late MockDashboardCubit mockDashboardCubit;
    late MockPushNotificationListenerCubit mockPushNotificationListenerCubit;

    setUp(() {
      connectivityStream = BiStreamController.broadcast();
      mockVoIPCallRepository = MockVoIPCallRepository();
      mockTwilioDialer = MockTwilioDialer();
      mockDashboardCubit = MockDashboardCubit();
      mockPushNotificationListenerCubit = MockPushNotificationListenerCubit();

      getIt.registerSingleton(connectivityStream.stream);

      getIt.registerSingleton<DashboardCubit>(mockDashboardCubit);
      getIt.registerSingleton<PushNotificationListenerCubit>(
        mockPushNotificationListenerCubit,
      );

      getIt.registerFactory(
        () => CallInProgressCubit(
          twilioDialer: mockTwilioDialer,
          voIPCallRepository: mockVoIPCallRepository,
        ),
      );
    });

    tearDown(() async {
      await connectivityStream.close();
      await getIt.reset();
    });

    testGoldens('renders correctly', (widgetTester) async {
      final builder = DeviceBuilder()
        ..addScenario(
          name: 'with speaker enabled',
          widget: BlocProvider<PushNotificationListenerCubit>(
            create: (_) => mockPushNotificationListenerCubit,
            child: Builder(
              builder: (context) {
                return const CallInProgressPage(
                  phoneNumber: '12345678',
                  isOutBound: false,
                  toIdentifier: '12345678',
                );
              },
            ),
          ),
        )
        ..addScenario(
          name: 'with speaker disabled',
          widget: BlocProvider<PushNotificationListenerCubit>(
            create: (_) => mockPushNotificationListenerCubit,
            child: Builder(
              builder: (context) {
                return const CallInProgressPage(
                  phoneNumber: '12345678',
                  isOutBound: false,
                  toIdentifier: '12345678',
                );
              },
            ),
          ),
          onCreate: (_) async {
            getIt.get<CallInProgressCubit>().toggleSpeaker();
          },
        );
      await widgetTester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

      await screenMatchesGolden(
        widgetTester,
        'call_in_progress_page',
      );
    });
  });
}
