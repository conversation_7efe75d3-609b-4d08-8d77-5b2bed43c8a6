import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/call_in_progress/cubit/call_in_progress_state.dart';
import 'package:embark/call_in_progress/views/_call_in_progress_view.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:embark/call_in_progress/cubit/call_in_progress_cubit.dart';
import 'package:widgets/widgets.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';

void main() {
  group('callInProgressViewTest', () {
    late Stream<List<ConnectivityResult>> connectionStatus;
    late BiStreamController<List<ConnectivityResult>> iCSStreamController;
    late MockVoIPCallRepository mockVoIPCallRepository;
    late MockTwilioDialer mockTwilioDialer;

    setUp(() {
      iCSStreamController = BiStreamController<List<ConnectivityResult>>();
      connectionStatus = iCSStreamController.stream;
      mockVoIPCallRepository = MockVoIPCallRepository();
      mockTwilioDialer = MockTwilioDialer();
      getIt.registerSingleton(
        CallInProgressCubit(
          twilioDialer: mockTwilioDialer,
          voIPCallRepository: mockVoIPCallRepository,
        ),
      );
    });

    Future<void> loadPage(WidgetTester tester) async {
      await tester.load(
        widget: Material(
          child: BiEmbarkBlocProvider<CallInProgressCubit, CallInProgressState>(
            create: (_) =>
                getIt.get<CallInProgressCubit>()..init('', false, ' '),
            internetConnectivityStream: connectionStatus,
            child: const CallInProgressView(
              phoneNumber: '12345',
            ),
          ),
        ),
      );
    }

    testWidgets(
        'Check that there is no back arrow and PopScope has canPop set to false',
        (WidgetTester tester) async {
      await loadPage(tester);

      expect(find.byType(BackButton), findsNothing);

      final popScopeFinder = find.byType(PopScope);

      expect(popScopeFinder, findsOneWidget);

      final popScopeWidget = tester.widget<PopScope>(popScopeFinder);

      expect(popScopeWidget.canPop, isFalse);
    });

    tearDown(() async {
      await iCSStreamController.close();
      await getIt.reset();
    });
  });
}
