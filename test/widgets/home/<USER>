import 'dart:async';

import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/dashboard/views/video_conference_task/_video_conference_task_provider.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/home/<USER>';
import 'package:embark/home/<USER>/_home_tab_view.dart';
import 'package:embark/home/<USER>/tabs/tabs.dart';
import 'package:embark/navigation_observers/navigation_observers.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/push_notifications/models/push_notification_type.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/repositories/smart_band/_smart_band_ui_repository.dart';
import 'package:embark/starter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:mockito/mockito.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bluetooth_frontend.dart';
import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';
import '../../unit_test_helpers/fakes/fake_tracking_repository.dart';
import '../widget_test_helpers/video_conference_task_provider_page_object.dart';

class _TestCubit extends HomeCubit {
  bool loadCalled = false;
  bool reloadTasksCalled = false;

  _TestCubit({
    required super.clientSettingsRepository,
    required super.appRepository,
    required super.calendarRepository,
    required super.trackingSetup,
    required super.locationRepository,
    required super.smartBandUiRepository,
  });

  @override
  Future<void> load({bool reloadClientSettings = true}) {
    loadCalled = true;
    return super.load();
  }

  @override
  Future<void> reloadTasks() {
    reloadTasksCalled = true;
    return super.reloadTasks();
  }
}

void main() {
  const emptyClientSettings = GetClientSettingsResponse(
    preferredLanguage: PreferredLanguageResponse(
      cultureCode: '',
      cultureName: '',
      languageCodeId: 1,
      languageId: 1,
    ),
  );

  group('HomeTabPage', () {
    late _TestCubit cubit;
    late BiStreamController<List<ConnectivityResult>> iCSController;
    late MockClientSettingsRepository clientSettingsRepository;
    late MockLocationRepository locationRepository;
    late BiStreamController<GetClientSettingsResponse> clientSettings;
    late MockAppRepository appRepository;
    late MockCalendarRepository calendarRepository;
    late MockCalendarPreference calendarPreference;
    late FakeTrackingSetup trackingSetup;
    final DateTime calendarLastCheckDate = DateTime.now();
    late MockPushNotificationListenerCubit pushNotificationListenerCubit;
    late StreamController<PushNotificationListenerState>
        pushNotificationStateChanges;
    late MockVideoConferenceRepository videoConferenceRepository;
    late MockBiBluetoothApi biBluetoothApi;
    late MockDashboardCubit dashboardCubit;
    late MockSmartLinkFirmwareInfoHttpClient
        mockSmartLinkFirmwareInfoHttpClient;

    setUp(() async {
      clientSettingsRepository = MockClientSettingsRepository();
      locationRepository = MockLocationRepository();
      clientSettings = BiStreamController(sync: true);
      appRepository = MockAppRepository();
      calendarRepository = MockCalendarRepository();
      calendarPreference = MockCalendarPreference();
      trackingSetup = FakeTrackingSetup();
      videoConferenceRepository = MockVideoConferenceRepository();
      pushNotificationListenerCubit = MockPushNotificationListenerCubit();
      pushNotificationStateChanges = StreamController.broadcast();
      biBluetoothApi = MockBiBluetoothApi();
      dashboardCubit = MockDashboardCubit();
      mockSmartLinkFirmwareInfoHttpClient =
          MockSmartLinkFirmwareInfoHttpClient();

      when(calendarPreference.lastCalendarCheckDate)
          .thenReturn(calendarLastCheckDate);
      when(calendarRepository.getCalendarPreference())
          .thenReturn(calendarPreference);

      when(biBluetoothApi.sensorStatus)
          .thenAnswer((_) => Future.value(BluetoothAdapterState.on));
      when(clientSettingsRepository.clientSettingsListener)
          .thenReturn(ValueNotifier(emptyClientSettings));

      when(clientSettingsRepository.clientSettings)
          .thenAnswer((realInvocation) => clientSettings.stream);

      when(pushNotificationListenerCubit.state)
          .thenReturn(PushNotificationListenerState.initial(false));
      when(pushNotificationListenerCubit.stream)
          .thenAnswer((_) => pushNotificationStateChanges.stream);

      cubit = _TestCubit(
        clientSettingsRepository: clientSettingsRepository,
        appRepository: appRepository,
        calendarRepository: calendarRepository,
        trackingSetup: trackingSetup,
        locationRepository: locationRepository,
        smartBandUiRepository: SmartBandUiRepository(
          sharedPreferencesService: MockSmartBandPreferences(),
          bluetoothApi: biBluetoothApi,
          frontend: FakeBluetoothFrontend(),
          sensorEventInfoRepository: MockSensorEventInfoRepository(),
          smartLinkFirmwareInfoHttpClient: mockSmartLinkFirmwareInfoHttpClient,
          backgroundIsolateStarterRepository:
              MockBackgroundIsolateStarterRepository(),
          permissionRequestRepository: BiPermissionRequestRepository(
            bluetoothApi: biBluetoothApi,
          ),
        ),
      );
      when(biBluetoothApi.sensorStatusStream).thenAnswer(
        (_) => Stream.value(BluetoothAdapterState.unknown),
      );
      getIt.registerSingleton<HomeCubit>(cubit);
      getIt.registerSingleton(CurrentRouteObserver());

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<VideoConferenceRepository>(
        videoConferenceRepository,
      );

      when(locationRepository.requestDeviceLocation()).thenAnswer(
        (_) async => geo.Position(
          longitude: 0,
          latitude: 0,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        ),
      );
    });

    tearDown(() async {
      unawaited(pushNotificationStateChanges.close());
      await getIt.reset();
      await clientSettings.close();
      await iCSController.close();
    });

    Future<void> loadWidgetTest(
      WidgetTester widgetTester, {
      bool initialLoad = true,
    }) async {
      await widgetTester.load(
        widget: MultiBlocProvider(
          providers: [
            BlocProvider<PushNotificationListenerCubit>(
              create: (_) => pushNotificationListenerCubit,
            ),
            BlocProvider<DashboardCubit>(
              create: (_) => dashboardCubit,
            ),
          ],
          child: VideoConferenceTaskProvider(
            child: HomeTabPage(initialLoad: initialLoad),
          ),
        ),
      );
    }

    testWidgets('calls cubit init on first render', (widgetTester) async {
      await loadWidgetTest(widgetTester);

      expect(cubit.loadCalled, isTrue);
    });

    testWidgets('reloads tasks when video conference hangs up',
        (widgetTester) async {
      FakePermissionHandlerPlatform.init().setStatuses({
        Permission.microphone: PermissionStatus.granted,
        Permission.camera: PermissionStatus.granted,
        Permission.bluetoothConnect: PermissionStatus.granted,
      });

      await loadWidgetTest(widgetTester);

      final vcPageObject = VideoConferenceTaskProviderPageObject();

      pushNotificationStateChanges.add(
        PushNotificationListenerState.initial(false).copyWith(
          newPushPayLoad: PushPayload(
            action: PushNotificationType.video.value,
          ),
        ),
      );
      await widgetTester.pump();

      when(videoConferenceRepository.prepareConnection()).thenAnswer(
        (_) async => const VideoConferenceConnectResponse(
          status: VideoConferenceConnectionStatus.unavailable,
        ),
      );

      await widgetTester.tap(vcPageObject.joinCallDialog.acceptButton);
      await widgetTester.pump();

      expect(cubit.reloadTasksCalled, isTrue);
    });

    testWidgets('renders correct child view', (widgetTester) async {
      await loadWidgetTest(widgetTester);

      expect(
        find.descendant(
          of: find.byType(BlocProvider<HomeCubit>),
          matching: find.byType(HomeTabView),
        ),
        findsOneWidget,
      );
    });

    testWidgets('updates dashboard title when task counts change',
        (widgetTester) async {
      await loadWidgetTest(widgetTester);

      clientSettings.add(
        emptyClientSettings.copyWith(
          plugins: [
            PluginResponse(
              type: PluginType.myDocuments.value,
              icon: '_action',
            ),
          ],
        ),
      );

      verifyNever(dashboardCubit.updateTodoCount(any));

      await widgetTester.pumpAndSettle();

      verify(dashboardCubit.updateTodoCount(1)).called(1);
    });

    testWidgets('reloads todo task list correctly when refresh button pressed',
        (widgetTester) async {
      await loadWidgetTest(widgetTester);

      // set initial client settings so we get out of the loding view.
      clientSettings.add(emptyClientSettings);

      // let animations and what not switch.
      await widgetTester.pump(const Duration(milliseconds: 350));

      // tap the "reload" button
      final refreshButton = find.descendant(
        of: find.byType(HomeTabView),
        matching: find.byType(BiButton),
      );
      await widgetTester.tap(refreshButton);

      // switch to loading view.
      await widgetTester.pump();

      // by now, client settings should trigger a reload, and the cubit state
      // should be loading.
      verify(
        clientSettingsRepository.reloadClientSettings(
          calendarLastCheck: calendarLastCheckDate,
        ),
      ).called(1);
      expect(find.byType(TaskListLoadingView), findsOneWidget);

      // let client settings API call return
      clientSettings.add(emptyClientSettings);

      // now, setting should switch back to empty list view.
      await widgetTester.pump();

      expect(find.byType(TaskListLoadingView), findsNothing);
      expect(find.byType(TaskList), findsOneWidget);
    });
  });
}
