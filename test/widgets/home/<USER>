import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/check_in/blink_detector.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/dashboard/cubit/_dashboard_cubit.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/home/<USER>/home_state.dart';
import 'package:embark/home/<USER>/models.dart';
import 'package:embark/home/<USER>/_home_tab_view.dart';
import 'package:embark/home/<USER>/tabs/list/_task_view.dart';
import 'package:embark/home/<USER>/tabs/tabs.dart';
import 'package:embark/navigation_observers/navigation_observers.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:embark/repositories/_biometric_repository.dart';
import 'package:embark/repositories/_client_settings_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/_image_compression.dart';
import 'package:embark/repositories/_location_repository.dart';
import 'package:embark/repositories/smart_band/_smart_band_ui_repository.dart';
import 'package:embark/routing/_embark_routes.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/self_report/cubit/state_machine/_self_report_state_machine.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:mockito/mockito.dart';
import 'package:widgets/widgets.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bluetooth_frontend.dart';
import '../../unit_test_helpers/fakes/fake_tracking_repository.dart';
import '../../unit_test_helpers/stubs/auth_manager.dart';

const _emptyClientSettings = GetClientSettingsResponse(
  preferredLanguage: PreferredLanguageResponse(
    cultureCode: '',
    cultureName: '',
    languageCodeId: 1,
    languageId: 1,
  ),
);

void main() {
  late MockClientSettingsRepository clientSettingsRepository;
  late MockLocationRepository locationRepository;
  // ignore: deprecated_member_use_from_same_package
  late PermissionHandler permissionHandler;
  late MockAppRepository appRepository;
  late MockCalendarRepository calendarRepository;
  late MockDashboardCubit dashboardCubit;
  late HomeCubit cubit;
  const TaskType messageTask = TaskType.message;
  late AppLocalizations l10n;
  late BiStreamController<List<ConnectivityResult>> connectivityStream;
  late MockCalendarPreference calendarPreference;
  late MockSelfReportCubit selfReportCubit;
  late CurrentRouteObserver navigatorObserver;
  late MockFaceDetector faceDetector;
  late MockBiometricRepository biometricRepository;
  late MockImageCompression imageCompression;
  late BlinkDetector blinkDetector;
  late FaceDetectionUtil faceDetectionUtil;
  late CameraUtil cameraUtil;
  late MockDeviceSetupRepository deviceSetupRepository;
  late SharedPreferencesService sharedPreferencesService;
  late MockFaceDetectionService faceDetectionService;
  late FakeTrackingSetup trackingSetup;
  late MockBiBluetoothApi biBluetoothApi;
  late MockSmartLinkFirmwareInfoHttpClient mockSmartLinkFirmwareInfoHttpClient;
  late ValueNotifier<GetClientSettingsResponse?> clientSettingsNotifier;

  Future<void> loadWidgetTest(
    WidgetTester widgetTester,
    HomeState initialState,
    HomeTabView homeTabView,
  ) async {
    await widgetTester.load(
      widget: MaterialApp(
        navigatorObservers: [navigatorObserver],
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: MultiBlocProvider(
          providers: [
            BlocProvider<DashboardCubit>(
              create: (_) => dashboardCubit,
            ),
            BlocProvider<PushNotificationListenerCubit>(
              create: (_) => MockPushNotificationListenerCubit(),
            ),
          ],
          child: DefaultTabController(
            length: 5,
            initialIndex: 2,
            child: Scaffold(
              appBar: const TabBar(
                tabs: [
                  Text('calendar'),
                  Text('resources'),
                  Text('home'),
                  Text('documents'),
                  Text('messages'),
                ],
              ),
              body: TabBarView(
                children: [
                  const Text('calendar content'),
                  const Text('resources content'),
                  BlocProvider(
                    create: (_) => cubit = HomeCubit(
                      initialState: initialState,
                      clientSettingsRepository: clientSettingsRepository,
                      appRepository: appRepository,
                      calendarRepository: calendarRepository,
                      trackingSetup: trackingSetup,
                      locationRepository: locationRepository,
                      smartBandUiRepository: SmartBandUiRepository(
                        sharedPreferencesService: MockSmartBandPreferences(),
                        bluetoothApi: biBluetoothApi,
                        frontend: FakeBluetoothFrontend(),
                        smartLinkFirmwareInfoHttpClient:
                            mockSmartLinkFirmwareInfoHttpClient,
                        sensorEventInfoRepository:
                            MockSensorEventInfoRepository(),
                        backgroundIsolateStarterRepository:
                            MockBackgroundIsolateStarterRepository(),
                        permissionRequestRepository:
                            BiPermissionRequestRepository(
                          bluetoothApi: biBluetoothApi,
                        ),
                      ),
                    ),
                    child: homeTabView,
                  ),
                  const Text('documents content'),
                  const Text('messages content'),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    // render header image
    await widgetTester.pump();
    // render content
    await widgetTester.pump();
  }

  setUp(() async {
    faceDetectionService = MockFaceDetectionService();
    clientSettingsRepository = MockClientSettingsRepository();
    locationRepository = MockLocationRepository();
    permissionHandler = MockPermissionHandler();
    appRepository = MockAppRepository();
    calendarRepository = MockCalendarRepository();
    dashboardCubit = MockDashboardCubit();
    l10n = await AppLocalizations.delegate.load(const Locale('en'));
    connectivityStream = BiStreamController.broadcast();
    calendarPreference = MockCalendarPreference();
    selfReportCubit = MockSelfReportCubit();
    navigatorObserver = CurrentRouteObserver();
    faceDetector = MockFaceDetector();
    biometricRepository = MockBiometricRepository();
    imageCompression = MockImageCompression();
    blinkDetector = BlinkDetector(
      faceDetectionService,
    );
    faceDetectionUtil = MockFaceDetectionUtil();
    trackingSetup = FakeTrackingSetup();
    biBluetoothApi = MockBiBluetoothApi();
    clientSettingsNotifier = ValueNotifier(null);
    mockSmartLinkFirmwareInfoHttpClient = MockSmartLinkFirmwareInfoHttpClient();

    when(clientSettingsRepository.clientSettingsListener)
        .thenReturn(clientSettingsNotifier);

    when(biBluetoothApi.sensorStatusStream)
        .thenAnswer((_) => Stream.value(BluetoothAdapterState.unknown));

    getIt.registerSingleton<ClientSettingsRepository>(clientSettingsRepository);
    // ignore: deprecated_member_use_from_same_package
    getIt.registerSingleton<PermissionHandler>(permissionHandler);
    getIt.registerSingleton(MdmShowAuth());
    getIt.registerSingleton(
      AuthManagerTestHelper.loggedInManager(
        authenticationRepository: MockEmbarkAuthenticationRepository(),
      ),
    );
    getIt.registerSingleton<LocationRepository>(locationRepository);
    getIt.registerSingleton<BiBluetoothApi>(biBluetoothApi);

    sharedPreferencesService = MockSharedPreferencesService();
    cameraUtil = CameraUtil(
      permissionHandler: permissionHandler,
      appRepository: appRepository,
    );
    deviceSetupRepository = MockDeviceSetupRepository();

    when(calendarPreference.lastCalendarCheckDate)
        .thenAnswer((_) => DateTime.now());

    when(calendarRepository.getCalendarPreference())
        .thenAnswer((_) => calendarPreference);

    when(selfReportCubit.state).thenReturn(SelfReportState.initial());
  });

  tearDown(() {
    connectivityStream.close();
    getIt.reset();
  });

  group('HomeTabView', () {
    testWidgets('builds todo tab initially', (widgetTester) async {
      await loadWidgetTest(
        widgetTester,
        HomeState.initial(),
        const HomeTabView(),
      );
      final todoListFinder = find.byKey(const Key('todo'));
      expect(todoListFinder, findsOneWidget);

      final taskList = widgetTester.widget<TaskList>(todoListFinder);
      expect(taskList.loading, isTrue);
      expect(taskList.tasks, isEmpty);
    });

    testWidgets('buildWhen condition todoTasks are different',
        (WidgetTester tester) async {
      await loadWidgetTest(
        tester,
        HomeState.initial().copyWith(todoTasksLoading: false),
        const HomeTabView(),
      );
      await tester.pump();

      expect(find.byType(BiEmbarkNoItemsView), findsOneWidget);
      expect(find.byType(TaskView), findsNothing);

      cubit.emit(
        HomeState.initial()
            .copyWith(todoTasks: [messageTask], todoTasksLoading: false),
      );
      await tester.pumpAndSettle();

      expect(find.byType(BiEmbarkNoItemsView), findsNothing);
      expect(find.byType(TaskView), findsOneWidget);
    });

    testWidgets('reload client settings on lifecycle change to resumed',
        (widgetTester) async {
      await loadWidgetTest(
        widgetTester,
        HomeState.initial(),
        const HomeTabView(),
      );
      widgetTester.binding
          .handleAppLifecycleStateChanged(AppLifecycleState.resumed);

      expect(cubit.state.todoTasksLoading, isTrue);
    });

    // US21745:  Comment out for the pending tab somce it is hidden for MVP
    // testWidgets('builds pending tab when tapped', (widgetTester) async {
    //   await loadWidgetTest(
    //     widgetTester,
    //     HomeState.initial(),
    //     const HomeTabView(),
    //   );
    //   await widgetTester.tap(find.text('Pending Tasks'));
    //   await widgetTester.pump();

    //   final pendingFinder = find.byKey(const Key('pending'));
    //   expect(pendingFinder, findsOneWidget);

    //   final taskList = widgetTester.widget<TaskList>(pendingFinder);
    //   expect(taskList.loading, isTrue);
    //   expect(taskList.tasks, isEmpty);
    // });

    testWidgets('passes correct state to todo list', (widgetTester) async {
      await loadWidgetTest(
        widgetTester,
        HomeState.initial(),
        const HomeTabView(),
      );

      cubit.emit(
        HomeState.initial().copyWith(
          todoTasks: [messageTask],
          todoTasksLoading: false,
        ),
      );

      // we are adding items, so let animation run
      await widgetTester.pump(const Duration(milliseconds: 400));

      final todoFinder = find.byKey(const Key('todo'));

      final taskList = widgetTester.widget<TaskList>(todoFinder);
      expect(taskList.loading, isFalse);
      expect(listEquals(taskList.tasks, [messageTask]), isTrue);
    });

// US21745:  Comment out for the pending tab since it is hidden for MVP
    // testWidgets('passes correct state to pending list', (widgetTester) async {
    //   await loadWidgetTest(
    //     widgetTester,
    //     HomeState.initial(),
    //     const HomeTabView(),
    //   );

    //   cubit.emit(
    //     HomeState.initial().copyWith(
    //       pendingTasks: [messageTask],
    //       pendingTasksLoading: false,
    //     ),
    //   );

    //   // we are adding items, so let animation run
    //   await widgetTester.pump(const Duration(milliseconds: 400));

    //   // switch to pending tab, note that the task count is loaded due to the
    //   // cubit emitting the state change prior to tapping this.
    //   await widgetTester.tap(find.text('Pending Tasks (1)'));
    //   await widgetTester.pump();

    //   final pendingFinder = find.byKey(const Key('pending'));

    //   final taskList = widgetTester.widget<TaskList>(pendingFinder);
    //   expect(taskList.loading, isFalse);
    //   expect(listEquals(taskList.tasks, [messageTask]), isTrue);
    // });

    testWidgets('navigates to messages when "new message" task is tapped',
        (widgetTester) async {
      clientSettingsNotifier.value = _emptyClientSettings.copyWith(
        plugins: [
          PluginResponse(type: PluginType.conversation.value, icon: '_action'),
        ],
      );
      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [messageTask],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );

      await widgetTester.tap(find.byType(BiIconListItem));
      // fire tap animation
      await widgetTester.pump();
      // let the tab change
      await widgetTester.pumpAndSettle();

      verify(dashboardCubit.navigateToTab(PluginType.conversation)).called(
        1,
      );
    });

    testWidgets('opens app settings when enable in settings action is tapped',
        (widgetTester) async {
      clientSettingsNotifier.value = _emptyClientSettings.copyWith(
        plugins: [
          PluginResponse(type: PluginType.smartband.value),
        ],
      );

      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [TaskType.bluetoothDisabled],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );
      await widgetTester.tap(find.text('Enable in Settings'));

      await widgetTester.pump();
      await widgetTester.pumpAndSettle();

      verify(biBluetoothApi.openSettings()).called(1);
    });

    testWidgets('navigates to calendar when "new calendar" task is tapped',
        (widgetTester) async {
      clientSettingsNotifier.value = _emptyClientSettings.copyWith(
        plugins: [
          PluginResponse(type: PluginType.calendar.value, icon: '_action'),
        ],
      );
      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [TaskType.calendarItem],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );
      await widgetTester.tap(find.byType(BiIconListItem));
      // fire tap animation
      await widgetTester.pump();
      // let the tab change
      await widgetTester.pumpAndSettle();

      verify(dashboardCubit.navigateToTab(PluginType.calendar)).called(1);
    });

    testWidgets(
        'navigates to self report when "self report" task is tapped and reload tasks when pop from self report',
        (widgetTester) async {
      setUpSelfReportPage(
        connectivityStream,
        clientSettingsRepository,
        selfReportCubit,
      );

      clientSettingsNotifier.value = _emptyClientSettings.copyWith(
        plugins: [
          PluginResponse(type: PluginType.selfReport.value, icon: '_action'),
        ],
      );

      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [TaskType.selfReport],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );
      await widgetTester.tap(find.byType(BiIconListItem));
      await widgetTester.pump(const Duration(seconds: 1));
      await widgetTester.pumpAndSettle();

      expect(
        navigatorObserver.currentRoute?.settings.name,
        EmbarkRoutes.selfReportRouteName,
      );
      Navigator.of(widgetTester.element(find.text(l10n.selfReport))).pop();
      await widgetTester.pump(const Duration(seconds: 1));
      expect(cubit.state.todoTasksLoading, isTrue);
    });

    testWidgets(
        'navigates to check in when "check in" task is tapped and reload tasks when pop from "check in"',
        (widgetTester) async {
      setUpCheckInPage(
        appRepository,
        connectivityStream,
        faceDetector,
        biometricRepository,
        imageCompression,
        blinkDetector,
        faceDetectionUtil,
        cameraUtil,
        deviceSetupRepository,
        sharedPreferencesService,
      );
      clientSettingsNotifier.value = _emptyClientSettings.copyWith(
        plugins: [
          PluginResponse(type: PluginType.checkInFacial.value, icon: '_action'),
        ],
      );
      when(appRepository.getStoredLocale()).thenAnswer(
        (realInvocation) => const Locale('en'),
      );

      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [TaskType.checkIn],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );
      await widgetTester.tap(find.byType(BiIconListItem));
      await widgetTester.pump();
      await widgetTester.pumpAndSettle();
      expect(
        navigatorObserver.currentRoute?.settings.name,
        EmbarkRoutes.checkInRouteName,
      );
      Navigator.of(widgetTester.element(find.text(l10n.checkIn))).pop();
      await widgetTester.pump(const Duration(seconds: 1));
      expect(cubit.state.todoTasksLoading, isTrue);
    });

    testWidgets('navigates to documents when document task is tapped',
        (widgetTester) async {
      clientSettingsNotifier.value = _emptyClientSettings.copyWith(
        plugins: [
          PluginResponse(type: PluginType.myDocuments.value, icon: '_action'),
        ],
      );
      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [TaskType.document],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );
      await widgetTester.tap(find.byType(BiIconListItem));
      await widgetTester.pump();
      await widgetTester.pumpAndSettle();
      verify(dashboardCubit.navigateToTab(PluginType.myDocuments)).called(
        1,
      );
    });

    testWidgets('Pull to refresh on tasks', (widgetTester) async {
      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [TaskType.document],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );

      final finder = find.ancestor(
        of: find.byType(TaskList),
        matching: find.byType(RefreshIndicator),
      );

      verifyNever(
        clientSettingsRepository.reloadClientSettings(
          calendarLastCheck: anyNamed('calendarLastCheck'),
          after: anyNamed('after'),
        ),
      );

      final widget = widgetTester.widget<RefreshIndicator>(finder);
      await widget.onRefresh();

      verify(
        clientSettingsRepository.reloadClientSettings(
          calendarLastCheck: anyNamed('calendarLastCheck'),
          after: null,
        ),
      ).called(1);
    });

    testWidgets('opens app settings when enable location tile tapped',
        (widgetTester) async {
      clientSettingsNotifier.value = _emptyClientSettings.copyWith(
        plugins: [
          PluginResponse(type: PluginType.tracking.value),
        ],
      );
      await loadWidgetTest(
        widgetTester,
        HomeState.initial().copyWith(
          todoTasks: [TaskType.alwaysLocationPermissionNeeded],
          todoTasksLoading: false,
        ),
        const HomeTabView(),
      );
      await widgetTester.tap(find.text(l10n.enableInSettings));
      await widgetTester.pump();
      await widgetTester.pumpAndSettle();
      verify(locationRepository.openLocationSettings()).called(1);
    });
  });
}

void setUpSelfReportPage(
  BiStreamController<List<ConnectivityResult>> connectivityStream,
  MockClientSettingsRepository clientSettingsRepository,
  MockSelfReportCubit selfReportCubit,
) {
  getIt.registerSingleton(connectivityStream.stream);
  getIt.registerSingleton<SelfReportCubit>(selfReportCubit);
  when(selfReportCubit.state).thenReturn(
    SelfReportState(
      stateMachine: SelfReportStateMachine(List.empty()),
      status: SelfReportStatus.loaded,
    ),
  );
}

void setUpCheckInPage(
  MockAppRepository appRepository,
  BiStreamController<List<ConnectivityResult>> connectivityStream,
  MockFaceDetector faceDetector,
  MockBiometricRepository biometricRepository,
  MockImageCompression imageCompression,
  BlinkDetector blinkDetector,
  FaceDetectionUtil faceDetectionUtil,
  CameraUtil cameraUtil,
  DeviceSetupRepository deviceSetupRepository,
  SharedPreferencesService sharedPreferencesService,
) {
  getIt.registerSingleton<AppRepository>(
    appRepository,
    instanceName: 'AppRepository',
  );
  getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
    connectivityStream.stream,
  );
  getIt.registerSingleton<FaceDetector>(faceDetector);
  getIt.registerSingleton<BiometricRepository>(biometricRepository);
  getIt.registerSingleton<ImageCompression>(imageCompression);
  getIt.registerSingleton<BlinkDetector>(blinkDetector);
  faceDetectionUtil = MockFaceDetectionUtil();
  getIt.registerFactory<FaceDetectionUtil>(() => faceDetectionUtil);
  getIt.registerFactory<CameraUtil>(() => cameraUtil);
  getIt.registerSingleton<DeviceSetupRepository>(deviceSetupRepository);
  getIt.registerSingleton<SharedPreferencesService>(sharedPreferencesService);
  getIt.registerSingleton<PushNotificationListenerCubit>(
    MockPushNotificationListenerCubit(),
  );
  getIt.registerSingleton(
    CheckinTimestampRepository(
      sharedPreferencesService: sharedPreferencesService,
    ),
  );
}
