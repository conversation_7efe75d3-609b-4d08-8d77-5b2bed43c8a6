import 'dart:async';

import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_login_widget/authentication/models/user.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/dashboard/cubit/video_conference_task/video_conference_task_cubit.dart';
import 'package:embark/dashboard/views/video_conference_task/_video_conference_task_provider.dart';
import 'package:embark/navigation_observers/_current_route_observer.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/push_notification_listener/push_notification_listener.dart';
import 'package:embark/push_notifications/models/push_notification_type.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/starter.dart';
import 'package:embark/video_conference/cubit/video_conference_cubit.dart';
import 'package:embark/video_conference/cubit/video_conference_state.dart';
import 'package:embark/video_conference/views/_video_conference_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';
import 'package:twilio_programmable_video/twilio_programmable_video.dart';

import '../../../unit_test.mocks.dart';
import '../../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';
import '../../../unit_test_helpers/stubs/auth_manager.dart';
import '../../widget_test_helpers/video_conference_task_provider_page_object.dart';

void main() {
  late PushNotificationListenerCubit notificationCubit;
  late MockEmbarkKeyValueDatabase keyValueDatabase;
  late MockAppRepository appRepository;
  late MockDeviceSetupRepository deviceSetupRepository;
  late MockVoIPCallRepository voipCallRepository;
  late MockPushNotificationsTokenSerivce tokenService;
  late MockDeviceInfoRepository deviceInfoRepository;
  late MockBiTokenManager tokenManager;
  late MockSharedPreferencesService sharedPreferencesService;
  late MockEmbarkAuthenticationRepository authenticationRepository;
  late MockClientSettingsRepository clientSettingsRepository;
  late MockRemoteNotificationsService remoteNotificationsService;
  late MockTwilioDialer twilioDialer;
  late MockPushNotificationsInfoBox pushNotificationsInfoBox;

  late MockVideoConferenceRepository videoConferenceRepository;
  late FakePermissionHandlerPlatform permissionHandlerPlatform;
  late StreamController<PushPayload> foregroundNotifications;
  late VideoConferenceTaskProviderPageObject taskProviderPageObject;
  late AppLocalizations l10n;
  late CurrentRouteObserver observer;

  void wireUpMocks() {
    when(appRepository.loadL10n()).thenAnswer(
      (_) async => AppLocalizationsEn(),
    );
    when(keyValueDatabase.pushNotificationsInfoBox)
        .thenReturn(pushNotificationsInfoBox);

    when(appRepository.foregroundNotificationsStream)
        .thenAnswer((_) => foregroundNotifications.stream);

    when(sharedPreferencesService.saveAsJson(any, any)).thenAnswer(
      (_) => Future.value(),
    );

    when(
      remoteNotificationsService.showNotification(
        any,
        isInAppNotification: anyNamed('isInAppNotification'),
      ),
    ).thenAnswer((_) => Future.value());
  }

  void createInstances() {
    l10n = AppLocalizationsEn();
    observer = CurrentRouteObserver();

    permissionHandlerPlatform = FakePermissionHandlerPlatform.init();
    keyValueDatabase = MockEmbarkKeyValueDatabase();
    appRepository = MockAppRepository();
    twilioDialer = MockTwilioDialer();
    deviceInfoRepository = MockDeviceInfoRepository();
    deviceSetupRepository = MockDeviceSetupRepository();
    voipCallRepository = MockVoIPCallRepository();
    tokenService = MockPushNotificationsTokenSerivce();
    tokenManager = MockBiTokenManager();
    sharedPreferencesService = MockSharedPreferencesService();
    authenticationRepository = MockEmbarkAuthenticationRepository();
    clientSettingsRepository = MockClientSettingsRepository();
    remoteNotificationsService = MockRemoteNotificationsService();
    videoConferenceRepository = MockVideoConferenceRepository();
    pushNotificationsInfoBox = MockPushNotificationsInfoBox();

    foregroundNotifications = StreamController();

    taskProviderPageObject = VideoConferenceTaskProviderPageObject();

    wireUpMocks();

    notificationCubit = PushNotificationListenerCubit(
      keyValueDatabase,
      appRepository,
      twilioDialer,
      deviceSetupRepository,
      voipCallRepository,
      tokenService,
      deviceInfoRepository,
      tokenManager,
      sharedPreferencesService,
      authenticationRepository,
      clientSettingsRepository,
      remoteNotificationsService,
    );
  }

  void setupGetItDependencies() {
    // dependencies for all of the push notification listener
    getIt.registerSingleton<PushNotificationListenerCubit>(
      notificationCubit,
    );
    getIt.registerSingleton<SharedPreferencesService>(sharedPreferencesService);
    getIt.registerSingleton<EmbarkAuthenticationRepository>(
      authenticationRepository,
    );
    getIt.registerSingleton<AuthManager>(
      AuthManagerTestHelper.loggedInManager(
        authenticationRepository: authenticationRepository,
      ),
    );
    getIt.registerSingleton<DeviceSetupRepository>(deviceSetupRepository);

    // dependencies for just our widget
    getIt.registerSingleton(observer);
    getIt.registerSingleton<VideoConferenceRepository>(
      videoConferenceRepository,
    );
  }

  void prepareVideoConferencePageDependencies() {
    getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
      Stream.value([ConnectivityResult.wifi]),
    );

    final fakeVideoConferencePageCubit = MockVideoConferenceCubit();
    when(fakeVideoConferencePageCubit.state)
        .thenReturn(VideoConferenceState.initial());

    getIt.registerSingleton<VideoConferenceCubit>(fakeVideoConferencePageCubit);
    getIt.registerSingletonAsync<CameraCapturer>(
      () async => MockCameraCapturer(),
    );
  }

  setUp(() {
    createInstances();
    setupGetItDependencies();
  });

  tearDown(() {
    foregroundNotifications.close();
    getIt.reset();
  });

  void login() {
    when(tokenManager.isTokenExpired()).thenAnswer((_) async => false);
    when(authenticationRepository.getCurrentUserSession()).thenReturn(
      const User('id', 'token'),
    );
  }

  void grantAllRequiredPermissions() {
    permissionHandlerPlatform.setStatuses({
      Permission.camera: PermissionStatus.granted,
      Permission.microphone: PermissionStatus.granted,
      Permission.bluetoothConnect: PermissionStatus.granted,
    });
  }

  Future<void> loadWidget(
    WidgetTester tester,
    Widget child,
  ) async {
    await tester.pumpWidget(
      MaterialApp(
        navigatorObservers: [observer],
        localizationsDelegates: const [
          ...AppLocalizations.localizationsDelegates,
          ...BiWidgetLocalizations.localizationsDelegates,
        ],
        initialRoute: '/',
        routes: {
          '/': (context) => PushNotificationListener(
                child: VideoConferenceTaskProvider(
                  child: child,
                ),
              ),
        },
      ),
    );
  }

  testWidgets('renders child', (WidgetTester tester) async {
    login();
    await loadWidget(tester, const Text('test'));

    expect(find.text('test'), findsOneWidget);
  });

  group('push notification', () {
    Future<PushPayload> receiveVideoConferencePushNotification(
      WidgetTester tester,
    ) async {
      final payload = PushPayload(
        action: PushNotificationType.video.value,
      );

      await tester.runAsync(() async {
        foregroundNotifications.add(payload);
        await pumpEventQueue(times: 1);
      });

      return payload;
    }

    Future<void> startNotificationTest(WidgetTester tester) async {
      await loadWidget(tester, const Text('test'));

      await receiveVideoConferencePushNotification(tester);

      await tester.pumpAndSettle();
    }

    Future<void> startJoinConferenceTappedTest(WidgetTester tester) async {
      await startNotificationTest(tester);

      await tester.tap(taskProviderPageObject.joinCallDialog.acceptButton);
      await tester.pump();
    }

    testWidgets(
        'Shows join video conference dialog when push notification received',
        (WidgetTester tester) async {
      login();
      await startNotificationTest(tester);

      expect(taskProviderPageObject.joinCallDialog, findsOneWidget);
    });

    testWidgets('immediately marks push notification as handled',
        (WidgetTester tester) async {
      login();
      await startNotificationTest(tester);

      expect(
        notificationCubit.state,
        equals(PushNotificationListenerState.initial(false)),
      );
    });

    testWidgets('Shows permission dialog after join tapped',
        (WidgetTester tester) async {
      login();

      await startJoinConferenceTappedTest(tester);

      final dialogTitle = tester
          .widget<Text>(taskProviderPageObject.permissionDialog.title)
          .data;

      final permissionList =
          [l10n.camera, l10n.microphone, l10n.nearByDevices].join(' - ');

      final formattedPermissionText =
          '${l10n.permissionDialogTitle}($permissionList)';

      expect(taskProviderPageObject.joinCallDialog, findsNothing);
      expect(dialogTitle, equals(formattedPermissionText));
    });

    testWidgets(
      'Shows permission dialog after join tapped on iOS correctly',
      (WidgetTester tester) async {
        login();
        await startJoinConferenceTappedTest(tester);

        final dialogTitle = tester
            .widget<Text>(taskProviderPageObject.permissionDialog.title)
            .data;

        final permissionList = [l10n.camera, l10n.microphone].join(' - ');

        final formattedPermissionText =
            '${l10n.permissionDialogTitle}($permissionList)';

        expect(taskProviderPageObject.joinCallDialog, findsNothing);
        expect(dialogTitle, equals(formattedPermissionText));
      },
      variant: TargetPlatformVariant.only(TargetPlatform.iOS),
    );

    testWidgets(
        'shows dialogs correctly when joining conference from push notification',
        (WidgetTester tester) async {
      login();
      grantAllRequiredPermissions();

      final apiResponseCompleter = Completer<VideoConferenceConnectResponse>();

      when(videoConferenceRepository.prepareConnection()).thenAnswer(
        (_) => apiResponseCompleter.future,
      );

      await startJoinConferenceTappedTest(tester);

      // next dialog should be "Joining..."
      expect(taskProviderPageObject.joinCallDialog, findsNothing);
      expect(taskProviderPageObject.hungUpDialog, findsNothing);
      expect(taskProviderPageObject.joiningDialog, findsOneWidget);

      // caller hangs up while joining
      apiResponseCompleter.complete(
        const VideoConferenceConnectResponse(
          status: VideoConferenceConnectionStatus.unavailable,
        ),
      );
      await tester.pump();

      // joining dialog should be gone, but hung up dialog is showing.
      expect(taskProviderPageObject.joinCallDialog, findsNothing);
      expect(taskProviderPageObject.joiningDialog, findsNothing);
      expect(taskProviderPageObject.hungUpDialog, findsOneWidget);
    });

    testWidgets('navigates to conference when joining from push notification',
        (WidgetTester tester) async {
      login();
      grantAllRequiredPermissions();

      final apiResponseCompleter = Completer<VideoConferenceConnectResponse>();

      when(videoConferenceRepository.prepareConnection()).thenAnswer(
        (_) => apiResponseCompleter.future,
      );

      await startJoinConferenceTappedTest(tester);
      // next dialog should be "Joining..."
      // caller hangs up while joining
      apiResponseCompleter.complete(
        VideoConferenceConnectResponse(
          status: VideoConferenceConnectionStatus.ready,
          response: PostVideoConferenceResponseInfo(
            duration: 10,
            roomName: 'name',
            token: 'token',
            userName: 'userName',
          ),
        ),
      );

      // before the VC page shows, setup the dependencies
      prepareVideoConferencePageDependencies();

      // trigger the page to show. Don't use settle here because it will hang
      // due to other VC page dependency setups we don't care about in these tests.
      await tester.pump();

      // joining dialog should be gone, but hung up dialog is showing.
      expect(taskProviderPageObject, findsNothing);
      expect(
        observer.currentRoute?.settings.name,
        equals(VideoConferencePageView.routeName),
      );
    });
  });

  Future<void> startConnectTest(WidgetTester tester) async {
    await loadWidget(
      tester,
      Builder(
        builder: (context) => TextButton(
          onPressed: () async {
            await context.read<VideoConferenceTaskCubit>().connect();
          },
          child: const Text('test'),
        ),
      ),
    );
    await tester.pumpAndSettle();

    await tester.tap(find.text('test'));
  }

  testWidgets('Shows permission dialog when connecting',
      (WidgetTester tester) async {
    login();

    await startConnectTest(tester);

    await tester.pumpAndSettle();

    final dialogTitle =
        tester.widget<Text>(taskProviderPageObject.permissionDialog.title).data;

    final permissionList =
        [l10n.camera, l10n.microphone, l10n.nearByDevices].join(' - ');

    final formattedPermissionText =
        '${l10n.permissionDialogTitle}($permissionList)';

    expect(taskProviderPageObject.joinCallDialog, findsNothing);
    expect(dialogTitle, equals(formattedPermissionText));
  });

  testWidgets(
    'Shows permission dialog when connecting on iOS correctly',
    (WidgetTester tester) async {
      login();

      await startConnectTest(tester);
      await tester.pumpAndSettle();

      final dialogTitle = tester
          .widget<Text>(taskProviderPageObject.permissionDialog.title)
          .data;

      final permissionList = [l10n.camera, l10n.microphone].join(' - ');

      final formattedPermissionText =
          '${l10n.permissionDialogTitle}($permissionList)';

      expect(taskProviderPageObject.joinCallDialog, findsNothing);
      expect(dialogTitle, equals(formattedPermissionText));
    },
    variant: TargetPlatformVariant.only(TargetPlatform.iOS),
  );

  testWidgets('shows dialogs correctly when joining conference',
      (WidgetTester tester) async {
    login();
    grantAllRequiredPermissions();

    final apiResponseCompleter = Completer<VideoConferenceConnectResponse>();

    when(videoConferenceRepository.prepareConnection()).thenAnswer(
      (_) => apiResponseCompleter.future,
    );

    await startConnectTest(tester);
    await tester.pump();

    // first dialog should be "Joining..."
    expect(taskProviderPageObject.hungUpDialog, findsNothing);
    expect(taskProviderPageObject.joiningDialog, findsOneWidget);

    // caller hangs up while joining
    apiResponseCompleter.complete(
      const VideoConferenceConnectResponse(
        status: VideoConferenceConnectionStatus.unavailable,
      ),
    );
    await tester.pumpAndSettle();

    // joining dialog should be gone, but hung up dialog is showing.
    expect(taskProviderPageObject.joiningDialog, findsNothing);
    expect(taskProviderPageObject.hungUpDialog, findsOneWidget);
  });

  testWidgets('navigates to conference page correctly',
      (WidgetTester tester) async {
    login();
    grantAllRequiredPermissions();

    final apiResponseCompleter = Completer<VideoConferenceConnectResponse>();

    when(videoConferenceRepository.prepareConnection()).thenAnswer(
      (_) => apiResponseCompleter.future,
    );

    await startConnectTest(tester);
    await tester.pump();

    // first dialog should be "Joining..."
    apiResponseCompleter.complete(
      VideoConferenceConnectResponse(
        status: VideoConferenceConnectionStatus.ready,
        response: PostVideoConferenceResponseInfo(
          duration: 10,
          roomName: 'name',
          token: 'token',
          userName: 'userName',
        ),
      ),
    );

    // before the VC page shows, setup the dependencies
    prepareVideoConferencePageDependencies();

    // trigger the page to show. Don't use settle here because it will hang
    // due to other VC page dependency setups we don't care about in these tests.
    await tester.pump();
    await tester.pump();

    // joining dialog should be gone, but hung up dialog is showing.
    expect(taskProviderPageObject, findsNothing);
    expect(
      observer.currentRoute?.settings.name,
      equals(VideoConferencePageView.routeName),
    );
  });
}
