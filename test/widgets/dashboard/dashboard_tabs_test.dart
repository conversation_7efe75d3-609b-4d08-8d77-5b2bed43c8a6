import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/calendar/tab-page/cubit/calendar_cubit.dart';
import 'package:embark/calendar/tab-page/views/calendar_page.dart';
import 'package:embark/dashboard/cubit/_dashboard_cubit.dart';
import 'package:embark/dashboard/cubit/_dashboard_state.dart';
import 'package:embark/dashboard/dashboard.dart';
import 'package:embark/dashboard/views/tabs/_calendar_view_action.dart';
import 'package:embark/dashboard/views/tabs/tabs.dart';
import 'package:embark/dashboard/views/video_conference_task/_video_conference_task_provider.dart';
import 'package:embark/documents/cubit/_document_state.dart';
import 'package:embark/documents/cubit/_documents_repository.dart';
import 'package:embark/documents/cubit/cubit.dart';
import 'package:embark/documents/views/_documents_page.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/home/<USER>/home_state.dart';
import 'package:embark/home/<USER>';
import 'package:embark/messages/cubit/messages_cubit.dart';
import 'package:embark/messages/views/messages_page.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/resources/cubit/resources_cubit.dart';
import 'package:embark/resources/resources.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:widgets/widgets.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_smart_band_settings_cubit.dart';
import '../widget_test_helpers/stub_repositories.dart';

void main() {
  group('DashboardTabs', () {
    late AppLocalizations l10n;
    late MockHomeCubit homeCubit;
    late MockBiLogger logger;
    late MockDocumentsCubit documentsCubit;
    late MockMessagesCubit messagesCubit;
    late MockResourcesCubit resourcesCubit;
    late MockCalendarCubit calendarCubit;
    late MockDashboardCubit dashboardCubit;
    late MockAppRepository appRepository;
    late MockCommunityReferralRepository referralRepository;
    late BiStreamController<List<ConnectivityResult>> iCSController;
    late MockPushNotificationListenerCubit pushNotificationListenerCubit;
    late MockClientSettingsRepository clientSettingsRepository;
    late ValueNotifier<GetClientSettingsResponse?> clientSettingsNotifier;

    const drawer = Text('drawer');
    late Map<PluginType, PluginResponse> plugins;

    const genericPlugin = PluginResponse(icon: '', title: '', type: -1);

    setUp(() {
      clientSettingsNotifier = ValueNotifier(null);
      l10n = AppLocalizationsEn();
      plugins = {
        PluginType.calendar: genericPlugin.copyWith(
          type: PluginType.calendar.value,
        ),
        PluginType.resources: genericPlugin.copyWith(
          type: PluginType.resources.value,
        ),
        PluginType.myDocuments: genericPlugin.copyWith(
          type: PluginType.myDocuments.value,
        ),
        PluginType.conversation: genericPlugin.copyWith(
          type: PluginType.conversation.value,
        ),
      };
      logger = MockBiLogger();
      appRepository = MockAppRepository();
      homeCubit = MockHomeCubit();
      documentsCubit = MockDocumentsCubit();
      messagesCubit = MockMessagesCubit();
      resourcesCubit = MockResourcesCubit();
      calendarCubit = MockCalendarCubit();
      dashboardCubit = MockDashboardCubit();
      referralRepository = MockCommunityReferralRepository();
      pushNotificationListenerCubit = MockPushNotificationListenerCubit();
      clientSettingsRepository = MockClientSettingsRepository();

      when(homeCubit.state).thenReturn(HomeState.initial());
      when(documentsCubit.state).thenReturn(
        DocumentsState(
          unFilteredMediaCollection: [],
          status: DocumentsStatus.initial,
          mediaCollection: [],
          filterList: [],
          selectedFilter: allFilter,
        ),
      );
      when(calendarCubit.state).thenReturn(const CalendarInitial());
      when(messagesCubit.state).thenReturn(const MessagesState([], '', '', -1));
      when(resourcesCubit.state)
          .thenReturn(const ResourcesState(resources: []));
      when(dashboardCubit.state).thenReturn(
        DashboardState(plugins: plugins, status: DashboardStatus.loaded),
      );
      when(pushNotificationListenerCubit.startingDashBoardTabId).thenReturn(-1);
      when(pushNotificationListenerCubit.state).thenReturn(
        PushNotificationListenerState.initial(false),
      );

      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );
      getIt.registerSingleton<BiLogger>(logger);
      getIt.registerSingleton<DashboardCubit>(dashboardCubit);
      getIt.registerSingleton<HomeCubit>(homeCubit);
      getIt.registerSingleton<DocumentsCubit>(documentsCubit);
      getIt.registerSingleton<CalendarCubit>(calendarCubit);
      getIt.registerSingleton<ResourcesCubit>(resourcesCubit);
      getIt.registerSingleton<MessagesCubit>(messagesCubit);
      getIt.registerSingleton<LocaleStreamRepository>(
        StubLocaleStreamRepository(),
      );
      getIt.registerSingleton<CommunityReferralRepository>(
        referralRepository,
      );
      getIt.registerSingleton<DocumentsRepository>(MockDocumentsRepository());

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<PushNotificationListenerCubit>(
        pushNotificationListenerCubit,
      );
      getIt.registerSingleton<ClientSettingsRepository>(
        clientSettingsRepository,
      );
      getIt.registerSingleton<VideoConferenceRepository>(
        MockVideoConferenceRepository(),
      );

      when(clientSettingsRepository.clientSettingsListener)
          .thenReturn(clientSettingsNotifier);
    });

    tearDown(() async {
      await getIt.reset();
      await iCSController.close();
    });

    Future<void> loadWidgetUnderTest(
      WidgetTester tester, {
      Text drawer = drawer,
    }) async {
      await tester.load(
        widget: MultiBlocProvider(
          providers: [
            BlocProvider<DashboardCubit>(
              create: (context) => dashboardCubit,
            ),
            BlocProvider<PushNotificationListenerCubit>(
              create: (context) => pushNotificationListenerCubit,
            ),
            BlocProvider<CalendarCubit>(
              create: (context) => calendarCubit,
            ),
            BlocProvider<SmartBandSettingsCubit>(
              create: (_) => FakeSmartBandSettingsCubit.disabled(),
            ),
          ],
          child: VideoConferenceTaskProvider(
            child: DashboardTabs(
              plugins: plugins,
              drawer: drawer,
            ),
          ),
        ),
      );
      // run animations at startup
      await tester.pump(const Duration(milliseconds: 300));
    }

    void scaffoldIsCorrect<TBody>(
      WidgetTester tester, {
      required String title,
      bool showBackButton = false,
      List<Widget>? actions,
    }) {
      final scaffoldFinder = find.byType(EmbarkScaffold);
      final scaffold = tester.widget<EmbarkScaffold>(scaffoldFinder);

      expect(scaffold.title, equals(title));
      for (final action in actions ?? <Widget>[]) {
        expect(find.byType(action.runtimeType), findsOneWidget);
      }

      expect(
        find.descendant(
          of: scaffoldFinder,
          matching: find.byType(TBody),
        ),
        findsOneWidget,
      );
    }

    Future<void> openTab(WidgetTester tester, {required int index}) async {
      assert(index > -1 && index < plugins.length + 1, 'Invalid tab index.');

      final toTap = find.byType(BiTab).at(index);

      await tester.tap(toTap);

      // let tab animation run
      await tester.pump(const Duration(milliseconds: 350));
    }

    testWidgets('renders home tab when no plugins', (tester) async {
      plugins.clear();

      await loadWidgetUnderTest(tester);

      scaffoldIsCorrect<HomeTabPage>(tester, title: l10n.toDoTasks(0));
    });
    testWidgets('renders calendar tab', (tester) async {
      await loadWidgetUnderTest(tester);

      await openTab(tester, index: 0);
      scaffoldIsCorrect<CalendarPage>(
        tester,
        title: l10n.calendar,
        actions: const [CalendarViewAction()],
      );
      expect(
        find.byKey(const Key('CalendarViewSwitcher')),
        findsOneWidget,
      );
    });
    testWidgets(
        'doesnt call clientSettings on initial load, calls on additional loads',
        (tester) async {
      await loadWidgetUnderTest(tester);
      scaffoldIsCorrect<HomeTabPage>(tester, title: l10n.toDoTasks(0));
      verify(homeCubit.load(reloadClientSettings: false)).called(1);
      verifyNever(homeCubit.load(reloadClientSettings: true));

      await openTab(tester, index: 0);
      await openTab(tester, index: 2);
      scaffoldIsCorrect<HomeTabPage>(tester, title: l10n.toDoTasks(0));
      verify(homeCubit.load(reloadClientSettings: true)).called(1);
      verifyNever(homeCubit.load(reloadClientSettings: false));
    });

    testWidgets('renders resources tab', (tester) async {
      await loadWidgetUnderTest(tester);

      await openTab(tester, index: 1);
      scaffoldIsCorrect<ResourcesPage>(tester, title: l10n.resources);
    });

    testWidgets('renders home tab', (tester) async {
      await loadWidgetUnderTest(tester);

      await openTab(tester, index: 2);
      scaffoldIsCorrect<HomeTabPage>(tester, title: l10n.toDoTasks(0));
    });

    testWidgets('renders documents tab', (tester) async {
      await loadWidgetUnderTest(tester);

      await openTab(tester, index: 3);
      scaffoldIsCorrect<DocumentsPage>(tester, title: l10n.documents);
    });

    testWidgets('renders messages tab', (tester) async {
      await loadWidgetUnderTest(tester);

      await openTab(tester, index: 4);

      scaffoldIsCorrect<MessagesPage>(
        tester,
        title: l10n.messages,
        actions: const [
          TranslationSwitchAction(),
        ],
      );
    });
  });
}
