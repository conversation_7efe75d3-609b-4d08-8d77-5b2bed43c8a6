import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/models/user.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/dashboard/views/_dashboard_listener.dart';
import 'package:embark/dashboard/views/_dashboard_view.dart';
import 'package:embark/dashboard/views/video_conference_task/_video_conference_task_provider.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/home/<USER>/home_state.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/_tracking/_tracking_repository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/repositories/smart_band/_smart_band_ui_repository.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bi_bluetooth_api.dart';
import '../../unit_test_helpers/fakes/fake_bluetooth_frontend.dart';
import '../../unit_test_helpers/fakes/fake_smart_band_settings_cubit.dart';
import '../../unit_test_helpers/fakes/fake_service_instance.dart';
import '../../unit_test_helpers/fakes/fake_tracking_repository.dart';
import '../widget_test_helpers/stub_repositories.dart';

final l10n = AppLocalizationsEn();

Future<void> _openDrawer(WidgetTester tester) async {
  await tester.tap(find.byIcon(Icons.menu_rounded));
  await tester.pumpAndSettle();
}

void main() {
  late MockEmbarkAuthenticationBloc authBloc;
  late DashboardCubit dashboardCubit;
  late MockHomeCubit homeCubit;
  late BiStreamController<DashboardState> dashboardStateStream;
  late BiStreamController<EmbarkAuthenticationState> authStateStream;
  late BiStreamController<List<ConnectivityResult>> connectivityStream;
  late BiStreamController<HomeState> homeStateStream;
  late MockPushNotificationListenerCubit pushNotificationListenerCubit;
  late MockAppRepository appRepository;
  late StubBannerMessgeRepository messageRepository;
  late BiStreamController<Locale> localeChanges;
  late BiStreamController<GetClientSettingsResponse> clientSettingsChanges;
  late MockEmbarkAuthenticationRepository authenticationRepository;
  late MockClientSettingsRepository clientSettingsRepository;
  late MockAppSettingsRepository appSettingsRepository;
  late AppLocalizations l10n;
  late BiStreamController<PushPayload> pushNotifications;
  late MockDeviceSetupRepository deviceSetupRepository;
  late MockSharedPreferencesService sharedPreferencesService;
  late MockSmartBandPreferences smartBandPreferences;
  late BiStreamController<AppLifecycleState> appLifecycleStateChanges;
  late MockBiTokenManager biTokenManager;
  late MockBiLogger logger;
  late MockFlutterSecureStorage flutterSecureStorage;
  late TrackingRepository trackingSetup;
  late FakeBluetoothFrontend bluetoothFrontend;
  late MockLocationRepository locationRepository;
  late MockSmartLinkFirmwareInfoHttpClient mockSmartLinkFirmwareInfoHttpClient;
  late FakeFlutterBackgroundService backgroundService;

  Future<void> loadWidget(
    WidgetTester widgetTester, {
    required AuthorizationType authType,
    required Widget child,
  }) async {
    const size = Size(500, 800);
    await widgetTester.binding.setSurfaceSize(size);
    widgetTester.view.physicalSize = size;
    widgetTester.view.devicePixelRatio = 1.0;
    widgetTester.view.platformDispatcher.textScaleFactorTestValue = 1;
    final clientSettingsValue = ValueNotifier(null);

    when(clientSettingsRepository.clientSettingsListener)
        .thenReturn(clientSettingsValue);

    await widgetTester.load(
      widget: RepositoryProvider(
        create: (_) => authType,
        child: MultiBlocProvider(
          providers: [
            BlocProvider<EmbarkAuthenticationBloc>(
              create: (_) => authBloc,
            ),
            BlocProvider<DashboardCubit>(create: (_) => dashboardCubit),
            BlocProvider<PushNotificationListenerCubit>(
              create: (_) => pushNotificationListenerCubit,
            ),
            BlocProvider<HomeCubit>(create: (_) => homeCubit),
            BlocProvider<SmartBandSettingsCubit>(
              create: (_) => FakeSmartBandSettingsCubit.disabled(),
            ),
          ],
          child: Provider.value(
            value: ResolvedDeviceType.byod,
            child: VideoConferenceTaskProvider(
              child: child,
            ),
          ),
        ),
      ),
    );
  }

  setUp(() {
    VisibilityDetectorController.instance.updateInterval = Duration.zero;

    // use sync streams to make testing easier.
    clientSettingsChanges =
        BiStreamController<GetClientSettingsResponse>.broadcast(sync: true);
    locationRepository = MockLocationRepository();
    pushNotifications = BiStreamController<PushPayload>(sync: true);
    localeChanges = BiStreamController<Locale>(sync: true);
    l10n = AppLocalizationsEn();
    appRepository = MockAppRepository();
    authenticationRepository = MockEmbarkAuthenticationRepository();
    clientSettingsRepository = MockClientSettingsRepository();
    appSettingsRepository = MockAppSettingsRepository();
    deviceSetupRepository = MockDeviceSetupRepository();
    sharedPreferencesService = MockSharedPreferencesService();
    smartBandPreferences = MockSmartBandPreferences();
    appLifecycleStateChanges =
        BiStreamController<AppLifecycleState>(sync: true);

    bluetoothFrontend = FakeBluetoothFrontend();
    authBloc = MockEmbarkAuthenticationBloc();
    authStateStream = BiStreamController.broadcast();
    logger = MockBiLogger();
    flutterSecureStorage = MockFlutterSecureStorage();
    trackingSetup = FakeTrackingRepository();
    mockSmartLinkFirmwareInfoHttpClient = MockSmartLinkFirmwareInfoHttpClient();
    backgroundService = FakeFlutterBackgroundService();

    when(authBloc.stream)
        .thenAnswer((realInvocation) => authStateStream.stream);
    when(authBloc.state)
        .thenReturn(AuthenticationInitial.authenticated(User.empty, false));

    when(appRepository.foregroundNotificationsStream).thenAnswer(
      (realInvocation) {
        return pushNotifications.stream;
      },
    );
    when(appRepository.localeStream)
        .thenAnswer((realInvocation) => localeChanges.stream);

    when(clientSettingsRepository.clientSettings)
        .thenAnswer((realInvocation) => clientSettingsChanges.stream);

    when(appSettingsRepository.storeAndSetAppLocaleIfChanged(any))
        .thenAnswer((realInvocation) async => null);

    when(deviceSetupRepository.lastKnownDeviceType)
        .thenReturn(ResolvedDeviceType.unknown);

    when(appRepository.appLifecycleStateStream)
        .thenAnswer((realInvocation) => appLifecycleStateChanges.stream);

    getIt.registerSingleton<DeviceSetupRepository>(deviceSetupRepository);
    getIt.registerSingleton<LocationRepository>(locationRepository);

    connectivityStream = BiStreamController.broadcast();
    pushNotificationListenerCubit = MockPushNotificationListenerCubit();
    dashboardStateStream = BiStreamController.broadcast();
    homeCubit = MockHomeCubit();
    homeStateStream = BiStreamController.broadcast();
    biTokenManager = MockBiTokenManager();

    final bluetoothApi = FakeBiBluetoothApi.init();

    dashboardCubit = DashboardCubit(
      appRepository,
      authenticationRepository,
      clientSettingsRepository,
      appSettingsRepository,
      smartBandPreferences,
      logger,
      CheckinTimestampRepository(
        sharedPreferencesService: sharedPreferencesService,
      ),
      SmartBandUiRepository(
        bluetoothApi: bluetoothApi,
        frontend: bluetoothFrontend,
        sharedPreferencesService: smartBandPreferences,
        sensorEventInfoRepository: MockSensorEventInfoRepository(),
        smartLinkFirmwareInfoHttpClient: mockSmartLinkFirmwareInfoHttpClient,
        backgroundIsolateStarterRepository:
            MockBackgroundIsolateStarterRepository(),
        permissionRequestRepository: BiPermissionRequestRepository(
          bluetoothApi: bluetoothApi,
        ),
      ),
      flutterSecureStorage,
      trackingSetup,
      backgroundService,
    );

    when(homeCubit.state)
        .thenReturn(HomeState.initial().copyWith(todoTasksLoading: false));
    when(homeCubit.stream)
        .thenAnswer((realInvocation) => homeStateStream.stream);
    when(pushNotificationListenerCubit.startingDashBoardTabId).thenReturn(-1);
    when(pushNotificationListenerCubit.state).thenReturn(
      PushNotificationListenerState.initial(false),
    );
    when(biTokenManager.isTokenExpired()).thenAnswer((_) async => false);
    messageRepository = StubBannerMessgeRepository();
    getIt.registerSingleton<HomeCubit>(homeCubit);
    getIt.registerSingleton(connectivityStream.stream);
    getIt.registerSingleton<BannerMessageRepository>(messageRepository);
    getIt.registerSingleton<VideoConferenceRepository>(
      MockVideoConferenceRepository(),
    );
  });

  tearDown(() async {
    await dashboardStateStream.close();
    await authStateStream.close();
    await connectivityStream.close();
    await homeStateStream.close();
    await localeChanges.close();
    await pushNotifications.close();
    await clientSettingsChanges.close();
    await appLifecycleStateChanges.close();
    await getIt.reset();
  });

  for (final authType in [
    AuthorizationType.standard,
    AuthorizationType.standardWithFingerprint,
  ]) {
    testWidgets('shows logout button when auth type is "${authType.name}',
        (widgetTester) async {
      dashboardCubit.emit(const DashboardState(status: DashboardStatus.loaded));
      await loadWidget(
        widgetTester,
        authType: authType,
        child: const DashboardView(),
      );
      await _openDrawer(widgetTester);
      expect(find.text(l10n.logOut), findsOneWidget);
    });
  }

  testWidgets('does not show logout button when auth type is auto',
      (widgetTester) async {
    dashboardCubit.emit(const DashboardState(status: DashboardStatus.loaded));
    await loadWidget(
      widgetTester,
      authType: AuthorizationType.autoFingerprint,
      child: const DashboardView(),
    );

    await _openDrawer(widgetTester);
    expect(find.text(l10n.settings), findsOneWidget);
    expect(find.text(l10n.notificationHistory), findsOneWidget);
    expect(find.text(l10n.logOut), findsNothing);
  });

  group('check in', () {
    GetClientSettingsResponse? settings;
    setUp(() {
      settings = const GetClientSettingsResponse(
        enrolledFacial: true,
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: '',
          cultureName: '',
          languageCodeId: 1,
          languageId: 1,
        ),
      );
    });
    tearDown(() {
      settings = null;
    });
    testWidgets('check in does not show up if disabled', (tester) async {
      dashboardCubit.emit(
        DashboardState(
          status: DashboardStatus.loaded,
          clientSettings: settings!.copyWith(enrolledFacial: null),
        ),
      );
      await loadWidget(
        tester,
        authType: AuthorizationType.autoFingerprint,
        child: const DashboardView(),
      );
      await tester.pump();
      await _openDrawer(tester);

      expect(find.byKey(const Key('CheckIn')), findsNothing);
      expect(find.byKey(const Key('Settings')), findsOneWidget);
    });
    testWidgets('check in takes you to check in screen', (tester) async {
      dashboardCubit.emit(
        DashboardState(
          status: DashboardStatus.loaded,
          clientSettings: settings,
        ),
      );
      var launchedCheckIn = false;
      await loadWidget(
        tester,
        authType: AuthorizationType.autoFingerprint,
        child: DashboardListener(
          showCheckin: (context) {
            launchedCheckIn = true;
          },
          child: const DashboardView(),
        ),
      );
      await _openDrawer(tester);
      await tester.tap(find.byKey(const Key('CheckIn')));
      await tester.idle();
      await tester.pumpAndSettle();

      expect(launchedCheckIn, true);
    });
    testWidgets('shows message when checkin is not allowed because of too many',
        (tester) async {
      dashboardCubit.emit(
        DashboardState(
          status: DashboardStatus.loaded,
          clientSettings: settings,
        ),
      );
      final dates = List.generate(
        3,
        (index) => DateTime.now()
            .subtract(Duration(minutes: 5, seconds: index))
            .toIso8601String(),
      );
      when(
        sharedPreferencesService
            .getStringList(CheckinTimestampRepository.adHocCheckInsListKey),
      ).thenReturn(dates);
      await loadWidget(
        tester,
        authType: AuthorizationType.autoFingerprint,
        child: const DashboardListener(child: DashboardView()),
      );
      final messageFuture = messageRepository.bannerMessageStream.first;

      await tester.pump();
      await _openDrawer(tester);
      await tester.tap(find.byKey(const Key('CheckIn')));
      await tester.idle();
      tester.binding.scheduleWarmUpFrame();
      await tester.pumpAndSettle();
      final message = await messageFuture;
      expect(message.text, contains('No check-ins allowed until'));
    });
  });
}
