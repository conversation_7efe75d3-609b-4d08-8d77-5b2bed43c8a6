import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/authentication/models/user.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_flutter_ui_test/helpers/_local_file_comparator_with_threshold.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/dashboard/cubit/_dashboard_cubit.dart';
import 'package:embark/dashboard/cubit/_dashboard_state.dart';
import 'package:embark/dashboard/views/_dashboard_view.dart';
import 'package:embark/dashboard/views/video_conference_task/_video_conference_task_provider.dart';
import 'package:embark/home/<USER>/home_cubit.dart';
import 'package:embark/home/<USER>/home_state.dart';
import 'package:embark/internet_connection/internet_connection_listener.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';
import 'package:provider/provider.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/data/bluetooth_devices.dart';
import '../../unit_test_helpers/data/client_settings.dart';
import '../../unit_test_helpers/fakes/fake_bi_bluetooth_api.dart';
import '../../unit_test_helpers/fakes/fake_bluetooth_frontend.dart';
import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';
import '../../unit_test_helpers/fakes/fake_smart_band_settings_cubit.dart';
import '../widget_test_helpers/golden_wrapper.dart';

void main() {
  late MockEmbarkAuthenticationBloc authBloc;
  late MockDashboardCubit dashboardCubit;
  late MockHomeCubit homeCubit;
  late BiStreamController<DashboardState> dashboardStateStream;
  late BiStreamController<EmbarkAuthenticationState> authStateStream;
  late BiStreamController<List<ConnectivityResult>> connectivityStream;
  late BiStreamController<HomeState> homeStateStream;
  late MockPushNotificationListenerCubit pushNotificationListenerCubit;
  late MockPinRepository pinRepository;
  late MockBiLogger logger;
  late BiStreamController<BannerMessage> bannerMessages;
  late MockAppRepository appRepository;
  late MockBiometricRepository biometricRepository;
  late MockClientSettingsRepository clientSettingsRepository;
  late BiStreamController<GetClientSettingsResponse> clientSettingsStream;
  late MockSharedPreferencesService sharedPreferencesService;
  late MockSmartBandPreferences smartBandPreferences;
  late FakeBiBluetoothApi bluetoothApi;
  late FakeBluetoothFrontend bluetoothFrontend;
  late ValueNotifier<GetClientSettingsResponse?> clientSettingsValue;
  late SmartBandSettingsCubit smartBandSettingsCubit;
  late FakePermissionHandlerPlatform permissionHandler;

  setUp(() {
    VisibilityDetectorController.instance.updateInterval = Duration.zero;
    smartBandPreferences = MockSmartBandPreferences();
    connectivityStream = BiStreamController.broadcast();
    pushNotificationListenerCubit = MockPushNotificationListenerCubit();
    dashboardCubit = MockDashboardCubit();
    dashboardStateStream = BiStreamController.broadcast();
    when(dashboardCubit.state).thenReturn(
      const DashboardState(status: DashboardStatus.initial),
    );
    when(dashboardCubit.stream)
        .thenAnswer((realInvocation) => dashboardStateStream.stream);
    when(pushNotificationListenerCubit.state)
        .thenReturn(PushNotificationListenerState.initial(false));

    authBloc = MockEmbarkAuthenticationBloc();
    authStateStream = BiStreamController.broadcast();
    when(authBloc.stream)
        .thenAnswer((realInvocation) => authStateStream.stream);
    when(authBloc.state)
        .thenReturn(AuthenticationInitial.authenticated(User.empty, false));

    homeCubit = MockHomeCubit();
    homeStateStream = BiStreamController.broadcast();
    when(homeCubit.state).thenReturn(HomeState.initial());
    when(homeCubit.stream)
        .thenAnswer((realInvocation) => homeStateStream.stream);
    when(pushNotificationListenerCubit.startingDashBoardTabId).thenReturn(-1);

    pinRepository = MockPinRepository();
    logger = MockBiLogger();
    bannerMessages = BiStreamController();
    appRepository = MockAppRepository();
    biometricRepository = MockBiometricRepository();

    clientSettingsRepository = MockClientSettingsRepository();
    clientSettingsStream = BiStreamController.broadcast();

    sharedPreferencesService = MockSharedPreferencesService();

    when(clientSettingsRepository.clientSettings)
        .thenAnswer((_) => clientSettingsStream.stream);

    clientSettingsStream.add(
      const GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: '',
          cultureName: '',
          languageCodeId: 0,
          languageId: 0,
        ),
        enrolledFacial: false,
      ),
    );

    clientSettingsValue = ValueNotifier(null);

    when(clientSettingsRepository.clientSettingsListener)
        .thenReturn(clientSettingsValue);

    bluetoothApi = FakeBiBluetoothApi.init();
    bluetoothFrontend = FakeBluetoothFrontend();
    permissionHandler = FakePermissionHandlerPlatform.init();
    smartBandSettingsCubit = FakeSmartBandSettingsCubit.disabled();

    when(appRepository.bannerMessageStream)
        .thenAnswer((realInvocation) => bannerMessages.stream);
    when(appRepository.bannerMessageSink)
        .thenAnswer((realInvocation) => bannerMessages.sink);

    getIt.registerSingleton<BiLogger>(logger);
    getIt.registerSingleton<PinRepository>(pinRepository);
    getIt.registerSingleton<HomeCubit>(homeCubit);
    getIt.registerSingleton(connectivityStream.stream);
    getIt.registerSingleton<AppRepository>(
      appRepository,
      instanceName: 'AppRepository',
    );
    getIt.registerSingleton<BiometricRepository>(biometricRepository);
    getIt.registerSingleton<ClientSettingsRepository>(clientSettingsRepository);
    getIt.registerSingleton<SharedPreferencesService>(sharedPreferencesService);
    getIt.registerSingleton<SmartBandPreferences>(smartBandPreferences);
    getIt.registerSingleton<VideoConferenceRepository>(
      MockVideoConferenceRepository(),
    );
  });

  tearDown(() async {
    await clientSettingsStream.close();
    await dashboardStateStream.close();
    await authStateStream.close();
    await connectivityStream.close();
    await homeStateStream.close();
    await bannerMessages.close();
    await getIt.reset();
  });

  group('DashboardView', () {
    const someSmartBandId = 'db2fcf0b-6a00-4ebf-a594-f1c25207cd2a';
    const someSmartBandSerialNumber = '1234567';
    const someSmartBandFirmwareVersion = '1.2.3';

    const someSmartBandDevice = TestBluetoothDevice(
      id: someSmartBandId,
      name: 'SB-$someSmartBandSerialNumber',
    );

    void withSmartBandPlugin({
      String? serialNumber,
    }) {
      clientSettingsValue.value = someClientSettings.copyWith(
        plugins: [
          PluginResponse(
            type: PluginType.smartband.value,
            extraInfo: {
              'serialNumber': serialNumber,
            },
          ),
        ],
      );
    }

    setUp(() {
      permissionHandler.setStatuses({
        Permission.bluetoothScan: PermissionStatus.granted,
        Permission.bluetoothConnect: PermissionStatus.granted,
      });

      withSmartBandPlugin(serialNumber: someSmartBandSerialNumber);
      bluetoothFrontend.backendRunnning = true;
    });

    Future<void> setDeviceConnected(WidgetTester tester) async {
      bluetoothApi.setPairedSmartBand(someSmartBandDevice);

      when(smartBandPreferences.getSbFirmwareVersion())
          .thenReturn(someSmartBandFirmwareVersion);

      when(smartBandPreferences.getSbSerial())
          .thenReturn(someSmartBandSerialNumber);

      bluetoothFrontend.deviceConnected(someSmartBandDevice);

      await tester.pump(const Duration(seconds: 2));
    }

    testGoldens('Non mdm Dashboard view renders correctly', (tester) async {
      LocalFileComparatorWithThreshold.configure(threshold: .05);
      when(dashboardCubit.state).thenReturn(
        const DashboardState(status: DashboardStatus.loaded),
      );

      final widget = _repositoryProvider(
        authBloc,
        dashboardCubit,
        pushNotificationListenerCubit,
        homeCubit,
        ResolvedDeviceType.byod,
        smartBandSettingsCubit,
      );

      final builder = DeviceBuilder()
        ..addScenario(
          widget: widget,
          name: 'Dashboard view',
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);
      await screenMatchesGolden(
        tester,
        'dashboard_view_non_mdm',
        customPump: (tester) async {
          await tester.pump();
        },
      );
    });
    testGoldens('No network connection Dashboard view renders correctly',
        (tester) async {
      LocalFileComparatorWithThreshold.configure(threshold: .05);
      when(dashboardCubit.state).thenReturn(
        const DashboardState(status: DashboardStatus.loaded),
      );

      final widget = _repositoryProvider(
        authBloc,
        dashboardCubit,
        pushNotificationListenerCubit,
        homeCubit,
        ResolvedDeviceType.byod,
        smartBandSettingsCubit,
      );

      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: widget,
          name: 'Dashboard view',
          onCreate: (key) async {
            connectivityStream.add([ConnectivityResult.none]);
            await tester.pump(const Duration(seconds: 2));
            expect(find.byType(AlertDialog), findsOneWidget);
          },
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);
      await tester.pump(const Duration(seconds: 2));

      await screenMatchesGolden(
        tester,
        'dashboard_view_no_network',
        customPump: (tester) async {
          await tester.pump();
        },
      );
    });
    testGoldens('Dashboard view renders correctly with smartband connected',
        (tester) async {
      LocalFileComparatorWithThreshold.configure(threshold: .05);
      when(dashboardCubit.state).thenReturn(
        const DashboardState(status: DashboardStatus.loaded),
      );
      final widget = _repositoryProvider(
        authBloc,
        dashboardCubit,
        pushNotificationListenerCubit,
        homeCubit,
        ResolvedDeviceType.byod,
        smartBandSettingsCubit,
      );

      final builder = DeviceBuilder()
        ..addScenario(
          widget: widget,
          name: 'Dashboard view',
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);
      await setDeviceConnected(tester);
      await screenMatchesGolden(
        tester,
        'dashboard_view_with_smartband',
        customPump: (tester) async {
          await tester.pump();
          await tester.pump();
          await tester.pump();
        },
      );
    });

    testGoldens('Dashboard view renders correctly with smartband not connected',
        (tester) async {
      LocalFileComparatorWithThreshold.configure(threshold: .05);
      when(dashboardCubit.state).thenReturn(
        const DashboardState(
          status: DashboardStatus.loaded,
        ),
      );

      final widget = _repositoryProvider(
        authBloc,
        dashboardCubit,
        pushNotificationListenerCubit,
        homeCubit,
        ResolvedDeviceType.byod,
        smartBandSettingsCubit,
      );

      final builder = DeviceBuilder()
        ..addScenario(
          widget: widget,
          name: 'Dashboard view',
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);
      await screenMatchesGolden(
        tester,
        'dashboard_view_with_smartband_not_connected',
        customPump: (tester) async {
          await tester.pump();
        },
      );
    });

    testGoldens('mdm Dashboard initial view renders empty screen correctly',
        (tester) async {
      when(dashboardCubit.state).thenReturn(
        const DashboardState(status: DashboardStatus.initial),
      );

      final widget = _repositoryProvider(
        authBloc,
        dashboardCubit,
        pushNotificationListenerCubit,
        homeCubit,
        ResolvedDeviceType.mdm,
        smartBandSettingsCubit,
      );

      final builder = DeviceBuilder()
        ..addScenario(
          widget: widget,
          name: 'Dashboard view',
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);
      await screenMatchesGolden(
        tester,
        'dashboard_view_mdm_initial',
      );
    });
  });
}

Widget _repositoryProvider(
  MockEmbarkAuthenticationBloc authBloc,
  MockDashboardCubit dashboardCubit,
  MockPushNotificationListenerCubit pushNotificationListenerCubit,
  MockHomeCubit homeCubit,
  ResolvedDeviceType deviceType,
  SmartBandSettingsCubit smartBandSettingsCubit,
) {
  final widget = MaterialApp(
    localizationsDelegates: const [
      BiWidgetLocalizations.delegate,
      ...AppLocalizations.localizationsDelegates,
    ],
    home: Scaffold(
      body: InternetConnectionListener(
        child: RepositoryProvider(
          create: (_) => AuthorizationType.none,
          child: MultiBlocProvider(
            providers: [
              BlocProvider<EmbarkAuthenticationBloc>(
                create: (_) => authBloc,
              ),
              BlocProvider<DashboardCubit>(create: (_) => dashboardCubit),
              BlocProvider<PushNotificationListenerCubit>(
                create: (_) => pushNotificationListenerCubit,
              ),
              BlocProvider<HomeCubit>(create: (_) => homeCubit),
              BlocProvider<SmartBandSettingsCubit>(
                create: (_) => smartBandSettingsCubit..initialize(),
              ),
            ],
            child: Provider.value(
              value: deviceType,
              child: const VideoConferenceTaskProvider(
                child: DashboardView(),
              ),
            ),
          ),
        ),
      ),
    ),
  );
  return widget;
}
