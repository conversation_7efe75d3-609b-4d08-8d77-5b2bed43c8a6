import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/pin/reset/cubit/pin_reset_cubit.dart';
import 'package:embark/pin/reset/view/pin_reset_page.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/starter.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';

import '../../../unit_test.mocks.dart';
import '../../widget_test_helpers/golden_wrapper.dart';
import '../../widget_test_helpers/stub_repositories.dart';

void main() {
  late MockAppRepository appRepository;
  late SharedPreferencesService sharedPreferencesService;
  late BiStreamController<PushPayload> pushNotificationController;
  late PinResetCubit pinResetCubit;
  late StubBannerMessgeRepository bannerMessageRepository;
  late MockPinRepository mockPinRepository;
  late MockBiLogger logger;
  late MockBiometricRepository biometricRepository;
  late MockImageCompression imageCompression;
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late MockLocationRepository locationRepository;
  late MockFaceDetectionUtil faceDetectionUtil;
  late MockCameraUtil cameraUtil;
  late MockDeviceSetupRepository deviceSetupRepository;
  late MockClientSettingsRepository clientSettingsRepository;
  late MdmShowAuth showAuth;
  group('PinResetView', () {
    setUp(() {
      showAuth = MdmShowAuth();
      appRepository = MockAppRepository();
      sharedPreferencesService = MockSharedPreferencesService();
      clientSettingsRepository = MockClientSettingsRepository();
      pinResetCubit = PinResetCubit(
        appRepository,
        sharedPreferencesService,
        showAuth,
        clientSettingsRepository,
      );
      bannerMessageRepository = StubBannerMessgeRepository();
      mockPinRepository = MockPinRepository();
      logger = MockBiLogger();
      biometricRepository = MockBiometricRepository();
      imageCompression = MockImageCompression();
      iCSController = BiStreamController.broadcast(sync: true);
      locationRepository = MockLocationRepository();
      // blinkDetector = BlinkDetector(faceDetectionService);
      faceDetectionUtil = MockFaceDetectionUtil();
      cameraUtil = MockCameraUtil();
      showAuth = MdmShowAuth();
      deviceSetupRepository = MockDeviceSetupRepository();

      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<ImageCompression>(imageCompression);
      getIt.registerSingleton<BiLogger>(logger);
      getIt.registerSingleton(showAuth);
      getIt.registerSingleton<PinRepository>(mockPinRepository);
      getIt.registerSingleton<BannerMessageRepository>(bannerMessageRepository);
      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );
      getIt.registerSingleton<SharedPreferencesService>(
        sharedPreferencesService,
      );
      getIt.registerSingleton<BiometricRepository>(biometricRepository);
      getIt.registerSingleton<LocationRepository>(locationRepository);
      // getIt.registerSingleton<BlinkDetector>(blinkDetector);
      getIt.registerFactoryAsync<FaceDetectionUtil>(
        () async => faceDetectionUtil,
      );
      getIt.registerSingleton<CameraUtil>(cameraUtil);
      getIt.registerSingleton<DeviceSetupRepository>(deviceSetupRepository);
      getIt.registerFactory<PinResetCubit>(() => pinResetCubit);

      pushNotificationController = BiStreamController.broadcast();
      when(appRepository.foregroundNotificationsStream)
          .thenAnswer((realInvocation) => pushNotificationController.stream);
    });

    tearDown(() {
      pushNotificationController.close();
      iCSController.close();
      getIt.reset();
    });
    testGoldens('pin reset view renders correctly', (tester) async {
      const widget = PinResetPage();

      final builder = DeviceBuilder()
        ..addScenario(
          widget: widget,
          name: 'initial',
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

      await screenMatchesGolden(
        tester,
        'pin_reset_view_initial',
      );
    });
    testGoldens('pin reset success', (tester) async {
      const widget = PinResetPage();
      final builder = DeviceBuilder()
        ..addScenario(
          widget: widget,
          name: 'on error',
          onCreate: (key) async {
            pinResetCubit.resetSuccess();
            await tester.pump(const Duration(seconds: 3));
          },
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

      await screenMatchesGolden(
        tester,
        'pin_reset_view_success',
      );
    });

    testGoldens('pin reset error', (tester) async {
      const widget = PinResetPage();
      final builder = DeviceBuilder()
        ..addScenario(
          widget: widget,
          name: 'on error',
          onCreate: (key) async {
            pinResetCubit.resetError();
            await tester.pump(const Duration(seconds: 3));
          },
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

      await screenMatchesGolden(
        tester,
        'pin_reset_view_error',
      );
    });
  });
}
