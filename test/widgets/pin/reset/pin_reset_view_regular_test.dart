import 'dart:async';

import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/check_in/blink_detector.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/check_in/views/check_in_view.dart';
import 'package:embark/pin/reset/cubit/pin_reset_cubit.dart';
import 'package:embark/pin/reset/view/pin_reset_view.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:embark/repositories/_biometric_repository.dart';
import 'package:embark/repositories/_client_settings_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/_image_compression.dart';
import 'package:embark/repositories/_location_repository.dart';
import 'package:embark/repositories/_pin_repository.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:mockito/mockito.dart';

import '../../../tester_extensions.dart';
import '../../../unit_test.mocks.dart';
import '../../../unit_test_helpers/fakes/image_compression.dart';
import '../../../unit_test_helpers/stubs/auth_manager.dart';
import '../../widget_test_helpers/stub_repositories.dart';

void main() {
  final AppLocalizationsEn l10n = AppLocalizationsEn();
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late AppRepository appRepository;
  late BiometricRepository biometricRepository;
  // ignore: deprecated_member_use_from_same_package
  late PermissionHandler permissionHandler;
  late FaceDetector faceDetector;
  late FakeImageCompression imageCompression;
  late LocationRepository locationRepository;
  late BlinkDetector blinkDetector;
  late MockFaceDetectionUtil faceDetectionUtil;
  late CameraUtil cameraUtil;
  late StubBannerMessgeRepository bannerMessageRepository;
  late PinResetCubit pinResetCubit;
  late MockDeviceSetupRepository deviceSetupRepository;
  late SharedPreferencesService sharedPreferencesService;
  late MockAppRepository mockAppRepository;
  late BiStreamController<PushPayload> pushNotificationController;
  late MockPinRepository mockPinRepository;
  late MockBiLogger logger;
  late MdmShowAuth mdmShowAuth;
  late MockClientSettingsRepository clientSettingsRepository;

  Future<void> buildWidget(WidgetTester tester) async {
    await tester.load(
      widget: MaterialApp(
        localizationsDelegates: const [
          BiWidgetLocalizations.delegate,
          ...AppLocalizations.localizationsDelegates,
        ],
        home: BlocProvider(
          create: (context) => pinResetCubit,
          child: const PinResetView(),
        ),
      ),
    );
  }

  group('PinResetView', () {
    setUp(() {
      mdmShowAuth = MdmShowAuth();
      iCSController = BiStreamController.broadcast(sync: true);
      appRepository = MockAppRepository();
      biometricRepository = MockBiometricRepository();
      // ignore: deprecated_member_use_from_same_package
      permissionHandler = const PermissionHandler();
      faceDetector = MockFaceDetector();
      imageCompression = FakeImageCompression();
      locationRepository = MockLocationRepository();
      blinkDetector = BlinkDetector(
        MockFaceDetectionService(),
      );
      faceDetectionUtil = MockFaceDetectionUtil();
      cameraUtil = MockCameraUtil();
      bannerMessageRepository = StubBannerMessgeRepository();
      mockAppRepository = MockAppRepository();
      deviceSetupRepository = MockDeviceSetupRepository();
      sharedPreferencesService = MockSharedPreferencesService();
      mockPinRepository = MockPinRepository();
      logger = MockBiLogger();
      clientSettingsRepository = MockClientSettingsRepository();

      pushNotificationController = BiStreamController.broadcast();
      when(appRepository.foregroundNotificationsStream)
          .thenAnswer((realInvocation) => pushNotificationController.stream);
      pinResetCubit = PinResetCubit(
        mockAppRepository,
        sharedPreferencesService,
        mdmShowAuth,
        clientSettingsRepository,
      );

      getIt.registerSingleton<ClientSettingsRepository>(
        clientSettingsRepository,
      );
      getIt.registerFactory<FaceDetector>(
        () => faceDetector,
      );
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<BiometricRepository>(
        biometricRepository,
      );
      getIt.registerSingleton<ImageCompression>(
        imageCompression,
      );
      // ignore: deprecated_member_use_from_same_package
      getIt.registerSingleton<PermissionHandler>(
        permissionHandler,
      );
      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );
      getIt.registerSingleton<LocationRepository>(locationRepository);
      getIt.registerSingleton(MdmShowAuth());
      getIt.registerSingleton(
        AuthManagerTestHelper.loggedInManager(
          authenticationRepository: MockEmbarkAuthenticationRepository(),
        ),
      );
      getIt.registerSingleton<CameraUtil>(cameraUtil);
      getIt.registerSingletonAsync<FaceDetectionUtil>(
        () => Future.value(faceDetectionUtil),
      );
      getIt.registerSingleton<BlinkDetector>(blinkDetector);

      getIt.registerSingleton<BannerMessageRepository>(
        bannerMessageRepository,
      );
      getIt.registerSingleton<DeviceSetupRepository>(
        deviceSetupRepository,
      );
      getIt.registerSingleton<SharedPreferencesService>(
        sharedPreferencesService,
      );
      getIt.registerSingleton(
        CheckinTimestampRepository(
          sharedPreferencesService: sharedPreferencesService,
        ),
      );
      getIt.registerSingleton<PinRepository>(
        mockPinRepository,
      );
      getIt.registerSingleton<BiLogger>(
        logger,
      );
    });

    tearDown(() async {
      await pushNotificationController.close();
      await iCSController.close();
      await getIt.reset();
    });

    testWidgets('PinResetView renders correctly', (widgetTester) async {
      await buildWidget(widgetTester);
      expect(find.byType(PinResetView), findsOneWidget);
    });

    testWidgets('PinResetView renders shows the continue button',
        (widgetTester) async {
      await buildWidget(widgetTester);
      expect(find.byType(BiPositiveButton), findsOneWidget);
    });

    testWidgets('PinResetView clicking continue takes you to check-in page', (
      widgetTester,
    ) async {
      await buildWidget(widgetTester);
      await widgetTester.pumpAndSettle();
      await widgetTester.tap(find.byType(BiPositiveButton));
      await widgetTester.pumpAndSettle();
      expect(find.byType(CheckInView), findsOneWidget);
    });

    testWidgets('PinResetView loading state', (
      widgetTester,
    ) async {
      await buildWidget(widgetTester);
      await widgetTester.pumpAndSettle();
      pinResetCubit.resetPin();
      await widgetTester.pumpAndSettle();
      expect(find.byType(CheckInView), findsOneWidget);
    });

    testWidgets('PinResetView error state', (
      widgetTester,
    ) async {
      await buildWidget(widgetTester);
      await widgetTester.pumpAndSettle();
      pinResetCubit.resetError();
      await widgetTester.pump(const Duration(seconds: 3));
      expect(find.byType(BiPositiveButton), findsOneWidget);
    });

    testWidgets('PinResetView error state, shows banner message after delay', (
      widgetTester,
    ) async {
      await buildWidget(widgetTester);
      await widgetTester.pumpAndSettle();

      final messageFuture = bannerMessageRepository.bannerMessageStream.first;
      pinResetCubit.resetError();
      await widgetTester.pump(const Duration(seconds: 3));
      final message = await messageFuture;
      expect(message.text, contains(l10n.pinResetRequestFailed));
    });

    testWidgets('PinResetView error state, tapping takes you to check-in', (
      widgetTester,
    ) async {
      await buildWidget(widgetTester);
      await widgetTester.pumpAndSettle();
      pinResetCubit.resetError();
      await widgetTester.pump(const Duration(seconds: 3));
      final button = find.byType(BiPositiveButton);
      await widgetTester.tap(button);
      await widgetTester.pumpAndSettle();
      expect(find.byType(CheckInView), findsOneWidget);
    });

    testWidgets('PinResetView success state', (
      widgetTester,
    ) async {
      await buildWidget(widgetTester);
      await widgetTester.pumpAndSettle();
      pinResetCubit.resetSuccess();
      await widgetTester.pump(const Duration(seconds: 3000));
      expect(find.byType(PinResetView), findsOneWidget);
    });
  });
}
