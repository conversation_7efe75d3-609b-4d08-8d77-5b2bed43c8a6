import 'package:async/async.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/pin/validate/bloc/_pin_validate_page.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:embark/repositories/_biometric_repository.dart';
import 'package:embark/repositories/_client_settings_repository.dart';
import 'package:embark/repositories/_device_info_repository.dart';
import 'package:embark/repositories/_embark_authentication_repository.dart';
import 'package:embark/repositories/_pin_repository.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/starter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../unit_test.mocks.dart';
import '../../../unit_test_helpers/stubs/auth_manager.dart';
import '../../widget_test_helpers/golden_wrapper.dart';
import 'pin_validate_page_object.dart';

PinValidatePageObject _getPinValidatePageObject(Key key) =>
    PinValidatePageObject(find.byKey(key));

void main() {
  const String pin = '123456';
  late MockPinRepository pinRepository;
  late MockEmbarkAuthenticationRepository authenticationRepository;
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late MockBiLogger logger;
  late MockAppRepository appRepository;
  late BiStreamController<BannerMessage> bannerMessage;
  late CancelableOperation<void> delayTimer;
  late MockBiometricRepository biometricRepository;
  late MockClientSettingsRepository clientSettingsRepository;
  late BiStreamController<GetClientSettingsResponse> clientSettingsStream;
  late MockDeviceInfoRepository deviceInfoRepository;
  late MockSharedPreferencesService sharedPreferencesService;
  const GetPinValidateResponseInfo responseValid = GetPinValidateResponseInfo(
    isValid: true,
  );
  const GetPinValidateResponseInfo responseInvalid = GetPinValidateResponseInfo(
    isValid: false,
  );
  late AuthManager authManager;
  late MdmShowAuth authHandler;
  setUp(() {
    VisibilityDetectorController.instance.updateInterval = Duration.zero;
    iCSController = BiStreamController.broadcast(sync: true);
    pinRepository = MockPinRepository();
    authenticationRepository = MockEmbarkAuthenticationRepository();
    logger = MockBiLogger();
    appRepository = MockAppRepository();
    bannerMessage = BiStreamController();
    biometricRepository = MockBiometricRepository();
    delayTimer = CancelableOperation.fromFuture(
      Future.delayed(const Duration(seconds: 1), () => debugPrint('Canceled')),
    );

    clientSettingsRepository = MockClientSettingsRepository();
    deviceInfoRepository = MockDeviceInfoRepository();
    clientSettingsStream = BiStreamController.broadcast();
    sharedPreferencesService = MockSharedPreferencesService();
    authHandler = MdmShowAuth();
    authManager = AuthManagerTestHelper.loggedInManager(
      authenticationRepository: authenticationRepository,
    );
    when(clientSettingsRepository.clientSettings)
        .thenAnswer((_) => clientSettingsStream.stream);
    clientSettingsStream.add(
      const GetClientSettingsResponse(
        preferredLanguage: PreferredLanguageResponse(
          cultureCode: '',
          cultureName: '',
          languageCodeId: 0,
          languageId: 0,
        ),
        enrolledFacial: false,
      ),
    );

    when(appRepository.bannerMessageSink)
        .thenAnswer((realInvocation) => bannerMessage.sink);

    getIt
      ..registerSingleton<BiLogger>(logger)
      ..registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      )
      ..registerSingleton<PinRepository>(pinRepository)
      ..registerSingleton(authManager)
      ..registerSingleton<EmbarkAuthenticationRepository>(
        authenticationRepository,
      )
      ..registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      )
      ..registerSingleton<BiometricRepository>(biometricRepository)
      ..registerSingleton<DeviceInfoRepository>(deviceInfoRepository)
      ..registerSingleton(authHandler)
      ..registerSingleton<ClientSettingsRepository>(clientSettingsRepository)
      ..registerSingleton<SharedPreferencesService>(sharedPreferencesService);
  });

  tearDown(() async {
    await clientSettingsStream.close();
    await bannerMessage.close();
    await iCSController.close();
    await getIt.reset();
  });

  group('Pin Validate Page', () {
    testGoldens('renders correctly', (tester) async {
      final widget = PinValidatePage(onValidated: (_) => {});

      final builder = DeviceBuilder()
        ..addScenario(
          name: 'initial state',
          widget: widget,
        )
        ..addScenario(
          name: 'entering invalid pin',
          widget: widget,
          onCreate: (key) async {
            when(pinRepository.validatePin(pin)).thenAnswer((_) async {
              return responseInvalid;
            });
            final form = _getPinValidatePageObject(key);
            await tester.enterText(form.pinEntry, pin);
          },
        )
        ..addScenario(
          name: 'entering valid pin with n/w delay shows progress indicator',
          widget: widget,
          onCreate: (key) async {
            when(pinRepository.validatePin(pin)).thenAnswer((_) async {
              delayTimer.then((p0) => debugPrint('Delayed'));
              await delayTimer.value;
              return responseValid;
            });
            final form = _getPinValidatePageObject(key);
            await tester.enterText(form.pinEntry, pin);
          },
        )
        ..addScenario(
          name: 'entering valid pin without n/w delay',
          widget: widget,
          onCreate: (key) async {
            when(pinRepository.validatePin(pin)).thenAnswer((_) async {
              return responseValid;
            });
            final form = _getPinValidatePageObject(key);
            await tester.enterText(form.pinEntry, pin);
          },
        )
        ..addScenario(
          name:
              'entering 3 invalid pins does not show forgot pin button if not enrolled in biometrics',
          widget: widget,
          onCreate: (key) async {
            when(pinRepository.validatePin(pin)).thenAnswer((_) async {
              return responseInvalid;
            });
            final form = _getPinValidatePageObject(key);
            // Enter 3 invalid pins
            for (int i = 0; i < 3; i++) {
              await tester.enterText(form.pinEntry, pin);
              await tester.pump();
            }
          },
        )
        ..addScenario(
          name:
              'entering 3 invalid pins shows forgot pin button if enrolled in biometrics',
          widget: widget,
          onCreate: (key) async {
            when(pinRepository.validatePin(pin)).thenAnswer((_) async {
              return responseInvalid;
            });

            clientSettingsStream.add(
              const GetClientSettingsResponse(
                preferredLanguage: PreferredLanguageResponse(
                  cultureCode: '',
                  cultureName: '',
                  languageCodeId: 0,
                  languageId: 0,
                ),
                enrolledFacial: true,
              ),
            );

            final form = _getPinValidatePageObject(key);
            // Enter 3 invalid pins
            for (int i = 0; i < 3; i++) {
              await tester.enterText(form.pinEntry, pin);
              await tester.pump();
            }
          },
        );

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);
      await screenMatchesGolden(
        tester,
        'pin_validate_page',
        customPump: (tester) async {
          await tester.pump(
            const Duration(milliseconds: 100),
          );
        },
      );
    });
  });
}
