// test _pin_validate_page.dart
//  Class to test pin_validate_page

import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/pin/validate/bloc/_pin_validate_page.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:embark/repositories/_biometric_repository.dart';
import 'package:embark/repositories/_client_settings_repository.dart';
import 'package:embark/repositories/_device_info_repository.dart';
import 'package:embark/repositories/_embark_authentication_repository.dart';
import 'package:embark/repositories/_pin_repository.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/starter.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:pinput/pinput.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/stubs/auth_manager.dart';

void main() {
  group('PinValidatePage', () {
    final l10n = AppLocalizationsEn();
    const String pin = '123456';
    late MockPinRepository pinRepository;
    late MockEmbarkAuthenticationRepository authenticationRepository;
    late BiStreamController<List<ConnectivityResult>> iCSController;
    late MockBiLogger logger;
    late MockAppRepository appRepository;
    late MockBiometricRepository biometricRepository;
    late MockDeviceInfoRepository deviceInfoRepository;
    final ErrorBannerMessage message =
        ErrorBannerMessage(text: l10n.incorrectPin);
    late BiStreamController<BannerMessage> bannerMessage;
    late MockClientSettingsRepository clientSettingsRepository;
    late BiStreamController<GetClientSettingsResponse> clientSettingsStream;
    late MockSharedPreferencesService sharedPreferencesService;
    late MdmShowAuth authHandler;
    late AuthManager authManager;
    const GetPinValidateResponseInfo responseValid = GetPinValidateResponseInfo(
      isValid: true,
    );
    const GetPinValidateResponseInfo responseInvalid =
        GetPinValidateResponseInfo(
      isValid: false,
    );

    setUp(() {
      VisibilityDetectorController.instance.updateInterval = Duration.zero;
      iCSController = BiStreamController.broadcast(sync: true);
      pinRepository = MockPinRepository();
      authenticationRepository = MockEmbarkAuthenticationRepository();
      logger = MockBiLogger();
      appRepository = MockAppRepository();
      bannerMessage = BiStreamController();
      biometricRepository = MockBiometricRepository();
      clientSettingsRepository = MockClientSettingsRepository();
      clientSettingsStream = BiStreamController.broadcast();
      sharedPreferencesService = MockSharedPreferencesService();
      deviceInfoRepository = MockDeviceInfoRepository();
      authHandler = MdmShowAuth();
      authManager = AuthManagerTestHelper.loggedInManager(
        authenticationRepository: authenticationRepository,
      );
      when(clientSettingsRepository.clientSettings)
          .thenAnswer((_) => clientSettingsStream.stream);

      clientSettingsStream.add(
        const GetClientSettingsResponse(
          preferredLanguage: PreferredLanguageResponse(
            cultureCode: '',
            cultureName: '',
            languageCodeId: 0,
            languageId: 0,
          ),
          enrolledFacial: false,
        ),
      );

      when(appRepository.bannerMessageSink)
          .thenAnswer((realInvocation) => bannerMessage.sink);

      getIt
        ..registerSingleton<BiLogger>(logger)
        ..registerSingleton(authManager)
        ..registerSingleton<Stream<List<ConnectivityResult>>>(
          iCSController.stream,
        )
        ..registerSingleton<PinRepository>(pinRepository)
        ..registerSingleton(authHandler)
        ..registerSingleton<EmbarkAuthenticationRepository>(
          authenticationRepository,
        )
        ..registerSingleton<AppRepository>(
          appRepository,
          instanceName: 'AppRepository',
        )
        ..registerSingleton<BiometricRepository>(biometricRepository)
        ..registerSingleton<DeviceInfoRepository>(deviceInfoRepository)
        ..registerSingleton<ClientSettingsRepository>(clientSettingsRepository)
        ..registerSingleton<SharedPreferencesService>(sharedPreferencesService);
    });

    tearDown(() async {
      await clientSettingsStream.close();
      await bannerMessage.close();
      await iCSController.close();
      await getIt.reset();
    });

    testWidgets('renders correctly', (tester) async {
      await tester.load(
        widget: PinValidatePage(
          onValidated: (_) => {},
        ),
      );
      await tester.pump();
      expect(find.byType(Pinput), findsOneWidget);
    });

    testWidgets('valid pin entered makes api call', (tester) async {
      when(pinRepository.validatePin(pin)).thenAnswer((_) async {
        return responseValid;
      });
      await tester.load(widget: PinValidatePage(onValidated: (_) => {}));
      expect(authHandler.needsToShow, isTrue);
      await tester.pump();
      await (tester.enterText(find.byType(Pinput), pin));
      await tester.pump();
      expect(authHandler.needsToShow, isFalse);
      verify(pinRepository.validatePin(pin)).called(1);
    });

    testWidgets('invalid pin entered makes api call', (tester) async {
      when(pinRepository.validatePin(pin)).thenAnswer((_) async {
        return responseInvalid;
      });

      await tester.load(widget: PinValidatePage(onValidated: (_) => {}));
      await tester.pump();
      await (tester.enterText(find.byType(Pinput), pin));
      await tester.pump();
      verify(pinRepository.validatePin(pin)).called(1);
    });

    testWidgets('invalid api call shows error banner message', (tester) async {
      when(pinRepository.validatePin(pin)).thenAnswer((_) async {
        return responseInvalid;
      });

      await tester.load(widget: PinValidatePage(onValidated: (_) => {}));
      await tester.pump();
      await (tester.enterText(find.byType(Pinput), pin));
      await tester.pump();

      verify(pinRepository.validatePin(pin)).called(1);
      expect(bannerMessage.stream, emits(message));
    });
  });
}
