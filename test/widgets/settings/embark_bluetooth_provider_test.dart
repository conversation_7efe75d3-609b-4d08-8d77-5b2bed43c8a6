import 'package:bi_flutter_bluetooth_platform_api/plugin/_bi_bluetooth_api.dart';
import 'package:bi_flutter_bluetooth_platform_api/plugin/frontend/_bluetooth_frontend.dart';
import 'package:embark/settings/views/_embark_bluetooth_provider.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../tester_extensions.dart';
import '../../unit_test_helpers/fakes/fake_bluetooth_frontend.dart';

void main() {
  late BiBluetoothApi bluetoothApi;
  late FakeBluetoothFrontend frontend;

  setUp(() {
    bluetoothApi = BiBluetoothApi();
    frontend = FakeBluetoothFrontend();

    getIt.registerSingleton(bluetoothApi);
    getIt.registerSingleton<BluetoothFrontend>(frontend);
  });

  tearDown(() {
    getIt.reset();
  });

  testWidgets('renders child', (tester) async {
    await tester.load(
      widget: const EmbarkBluetoothProvider(child: Text('test')),
    );

    expect(find.text('test'), findsOneWidget);
  });

  testWidgets('provides correct services', (tester) async {
    BiBluetoothApi? actualApi;
    BluetoothFrontend? actualFrontend;

    await tester.load(
      widget: EmbarkBluetoothProvider(
        child: Builder(
          builder: (context) {
            actualApi = Provider.of<BiBluetoothApi>(context);
            actualFrontend = Provider.of<BluetoothFrontend>(context);
            return const Text('test');
          },
        ),
      ),
    );

    expect(actualApi, equals(bluetoothApi));
    expect(actualFrontend, equals(frontend));
  });
}
