import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_flutter_ui_test/helpers/_local_file_comparator_with_threshold.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/home/<USER>/tabs/list/_task_list_loading_view.dart';
import 'package:embark/notification_history/cubit/notification_history_cubit.dart';
import 'package:embark/notification_history/notification_history.dart';
import 'package:embark/starter.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../widget_test_helpers/golden_wrapper.dart';

void main() {
  group('NotificationHistoryPage Golden Test', () {
    late BiStreamController<List<ConnectivityResult>> connectivityStream;
    late MockNotificationHistoryCubit cubit;

    List<NotificationInfo> createMockNotificationHistoryResponse() {
      final constantDateTime = DateTime(2020, 1, 1, 12, 0).toUtc();

      final List<NotificationInfo> mockHistory = List.generate(10, (index) {
        return NotificationInfo(
          action: index,
          date: constantDateTime,
          extraInfo: null,
          id: 1,
          message: 'You received message',
          sound: 'music',
          title: 'Notification 1',
        );
      });
      return mockHistory;
    }

    setUp(() {
      connectivityStream = BiStreamController.broadcast();
      cubit = MockNotificationHistoryCubit();
      getIt.registerSingleton(connectivityStream.stream);
      getIt.registerFactory<NotificationHistoryCubit>(() => cubit);
    });

    tearDown(() async {
      await connectivityStream.close();
      await getIt.reset();
    });

    // TODO: after fixing the test so that it runs successfully on local devices
    // we should stop skipping this tst
    testGoldens(
      'notification history list renders correctly',
      (widgetTester) async {
        LocalFileComparatorWithThreshold.configure(threshold: .00015);
        when(cubit.state).thenReturn(
          NotificationHistoryState(
            notificationHistory: createMockNotificationHistoryResponse(),
            isLastPage: false,
            lastId: 0,
            status: NotificationHistoryStatus.notifications,
          ),
        );
        final builder = DeviceBuilder()
          ..addScenario(
            name: 'list of notification',
            widget: const NotificationHistoryPage(),
          );

        await widgetTester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

        await screenMatchesGolden(
          widgetTester,
          'notification_history_list',
        );
      },
    );

    testGoldens('notification history empty state renders correctly',
        (widgetTester) async {
      when(cubit.state).thenReturn(
        const NotificationHistoryState(
          notificationHistory: [],
          isLastPage: false,
          lastId: 0,
          status: NotificationHistoryStatus.empty,
        ),
      );
      final builder = DeviceBuilder()
        ..addScenario(
          name: 'with empty history list',
          widget: const NotificationHistoryPage(),
        );

      await widgetTester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

      await screenMatchesGolden(
        widgetTester,
        'notification_history_empty_state',
      );
    });

    testWidgets('loading state is present when loading state emitted',
        (widgetTester) async {
      when(cubit.state).thenReturn(
        const NotificationHistoryState(
          notificationHistory: [],
          isLastPage: false,
          lastId: 0,
          status: NotificationHistoryStatus.loading,
        ),
      );

      const widget = NotificationHistoryPage();

      await widgetTester.load(widget: widget);

      expect(find.byType(TaskListLoadingView), findsOneWidget);
    });
  });
}
