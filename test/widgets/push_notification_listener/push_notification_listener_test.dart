import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_font_awesome_pro/font_awesome_flutter.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/authentication/auth_steps/embark_mdm_auth_step.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/calendar/calendar.dart';
import 'package:embark/check_in/blink_detector.dart';
import 'package:embark/check_in/camera_util.dart';
import 'package:embark/check_in/check_in_timestamp_repository.dart';
import 'package:embark/check_in/face_detection_util.dart';
import 'package:embark/check_in/views/check_in_view.dart';
import 'package:embark/facial_enrollment/views/_facial_enrollment_page.dart';
import 'package:embark/login/cubit/embark_login_state.dart';
import 'package:embark/login/cubit/login_cubit.dart';
import 'package:embark/navigation_observers/navigation_observers.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/push_notification_listener/push_notification_listener.dart';
import 'package:embark/push_notifications/models/push_notification_type.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/cubit/_self_report_state.dart';
import 'package:embark/self_report/cubit/state_machine/_self_report_state_machine.dart';
import 'package:embark/self_report/self_report.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:embark/video_conference/cubit/video_conference_cubit.dart';
import 'package:embark/video_conference/cubit/video_conference_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:geolocator/geolocator.dart' as geo;
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:mockito/mockito.dart';
import 'package:twilio_programmable_video/twilio_programmable_video.dart';

import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/data/user.dart';

class FakePushNotificationListenerCubit extends PushNotificationListenerCubit {
  bool wasCheckIfThereIsSavedAppOpenedNotificationCalled = false;

  FakePushNotificationListenerCubit(
    super.embarkKeyValueDatabase,
    super.appRepository,
    super.twilioDialer,
    super.deviceSetupRepository,
    super.voIPCallRepository,
    super.pushNotificationsTokenService,
    super.deviceInfoRepository,
    super.tokenManager,
    super.sharedPreferences,
    super._embarkAuthenticationRepository,
    super._clientSettingsRepository,
    super._pnInfoService,
  );

  @override
  void initPushNotificationsListener() {}

  @override
  void checkIfThereIsSavedAppOpenedNotification() {
    wasCheckIfThereIsSavedAppOpenedNotificationCalled = true;
  }

  @override
  Future<void> getTwilioVoIpToken() async {}
}

void main() {
  late FakePushNotificationListenerCubit cubit;
  final BiStreamController<PushPayload> pushNotifications =
      BiStreamController<PushPayload>.broadcast(sync: true);
  late MockEmbarkKeyValueDatabase embarkKeyValueDatabase;
  late MockVideoConferenceRepository videoConferenceRepository;
  late MockLocationRepository locationRepository;
  late MockAppRepository appRepository;
  late MockTwilioDialer twilioDialer;
  late MockDeviceSetupRepository deviceSetupRepository;
  late MockVoIPCallRepository voIPCallRepository;
  late MockPushNotificationsTokenSerivce pushNotificationsTokenService;
  late MockDeviceInfoRepository deviceInfoRepository;
  late MockPermissionHandler permissionHandler;
  late AppLocalizations l10n;
  late MockPushNotificationsInfoBox pushNotificationsInfoBox;
  late MockBiTokenManager biTokenManager;
  late SharedPreferencesService sharedPreferencesService;
  late BiStreamController<List<ConnectivityResult>> connectivityStream;
  late MockBiometricRepository biometricRepository;
  late FaceDetector faceDetector;
  late ImageCompression imageCompression;
  late FaceDetectionUtil faceDetectionUtil;
  late EmbarkAuthenticationRepository authenticationRepository;
  late MockLoginCubit loginCubit;
  late MockSelfReportCubit selfReportCubit;
  late ClientSettingsRepository clientSettingsRepository;
  late AuthManager authManager;
  late ValueNotifier<AuthenticationState> authState;
  late MockFaceDetectionService faceDetectionService;
  late VideoConferenceCubit videoConferenceCubit;
  late MockCameraCapturer cameraCapturer;
  late MockTwilioProgrammableVideoService twilioProgrammableVideoService;
  late MockRemoteNotificationsService remoteNotificationsService;
  late CurrentRouteObserver navigatorObserver;
  late MockBiLogger logger;

  setUp(() {
    authState = ValueNotifier(
      AuthenticationInitial.authenticated(someLoggedInUser, false),
    );
    embarkKeyValueDatabase = MockEmbarkKeyValueDatabase();
    appRepository = MockAppRepository();
    faceDetectionService = MockFaceDetectionService();
    videoConferenceRepository = MockVideoConferenceRepository();
    locationRepository = MockLocationRepository();
    permissionHandler = MockPermissionHandler();
    deviceSetupRepository = MockDeviceSetupRepository();
    voIPCallRepository = MockVoIPCallRepository();
    twilioDialer = MockTwilioDialer();
    pushNotificationsTokenService = MockPushNotificationsTokenSerivce();
    deviceInfoRepository = MockDeviceInfoRepository();
    pushNotificationsInfoBox = MockPushNotificationsInfoBox();
    l10n = AppLocalizationsEn();
    biTokenManager = MockBiTokenManager();
    sharedPreferencesService = MockSharedPreferencesService();
    connectivityStream = BiStreamController.broadcast();
    biometricRepository = MockBiometricRepository();
    locationRepository = MockLocationRepository();
    permissionHandler = MockPermissionHandler();
    faceDetector = MockFaceDetector();
    imageCompression = MockImageCompression();
    faceDetectionUtil = MockFaceDetectionUtil();
    authenticationRepository = MockEmbarkAuthenticationRepository();
    loginCubit = MockLoginCubit();
    selfReportCubit = MockSelfReportCubit();
    clientSettingsRepository = MockClientSettingsRepository();
    logger = MockBiLogger();
    authManager = AuthManager(
      authHandler: AuthHandler(defaultAuthSteps: []),
      authenticationRepository: authenticationRepository,
      authStatus: authState,
    );
    twilioProgrammableVideoService = MockTwilioProgrammableVideoService();
    videoConferenceCubit = VideoConferenceCubit(
      twilioProgrammableVideoService: twilioProgrammableVideoService,
      cameraSourceService: MockCameraSourceService(),
      wakelockService: MockWakelockService(),
      initialState: VideoConferenceState.initial(),
      logger: logger,
      videoConferenceRepository: videoConferenceRepository,
    );
    cameraCapturer = MockCameraCapturer();
    navigatorObserver = CurrentRouteObserver();
    remoteNotificationsService = MockRemoteNotificationsService();

    when(twilioProgrammableVideoService.connect(any)).thenAnswer(
      (_) async => MockRoom(),
    );
    when(authenticationRepository.getCurrentUserSession()).thenReturn(
      const User('test id', 'test token'),
    );
    when(appRepository.foregroundNotificationsStream).thenAnswer(
      (_) {
        return pushNotifications.stream;
      },
    );
    when(embarkKeyValueDatabase.pushNotificationsInfoBox)
        .thenReturn(pushNotificationsInfoBox);

    when(appRepository.getStoredLocale()).thenAnswer(
      (realInvocation) => const Locale('en'),
    );

    when(selfReportCubit.state).thenReturn(SelfReportState.initial());

    cubit = FakePushNotificationListenerCubit(
      embarkKeyValueDatabase,
      appRepository,
      twilioDialer,
      deviceSetupRepository,
      voIPCallRepository,
      pushNotificationsTokenService,
      deviceInfoRepository,
      biTokenManager,
      sharedPreferencesService,
      authenticationRepository,
      clientSettingsRepository,
      remoteNotificationsService,
    );

    final geo.Position mockPosition = geo.Position(
      latitude: 0.0,
      longitude: 0.0,
      timestamp: DateTime.now(),
      accuracy: 0.0,
      altitude: 0.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );
    when(locationRepository.requestDeviceLocation())
        .thenAnswer((_) async => mockPosition);
    getIt.registerSingleton(authManager);
    getIt.registerSingleton<PushNotificationListenerCubit>(cubit);
    getIt.registerSingleton<BiometricRepository>(biometricRepository);
    getIt.registerSingleton<LocationRepository>(locationRepository);
    // ignore: deprecated_member_use_from_same_package
    getIt.registerSingleton<PermissionHandler>(permissionHandler);
    getIt.registerSingleton<FaceDetector>(faceDetector);
    getIt.registerSingleton<ClientSettingsRepository>(clientSettingsRepository);
    getIt.registerSingleton<ImageCompression>(imageCompression);
    getIt.registerSingleton<SharedPreferencesService>(sharedPreferencesService);
    getIt.registerSingleton(
      CheckinTimestampRepository(
        sharedPreferencesService: sharedPreferencesService,
      ),
    );
    getIt.registerFactory<FaceDetectionUtil>(
      () => faceDetectionUtil,
    );
    getIt.registerSingleton<AppRepository>(
      appRepository,
      instanceName: 'AppRepository',
    );
    getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
      connectivityStream.stream,
    );
    getIt.registerSingleton<EmbarkAuthenticationRepository>(
      authenticationRepository,
    );
    getIt.registerSingleton<DeviceSetupRepository>(
      deviceSetupRepository,
    );
    getIt.registerSingleton<VideoConferenceCubit>(videoConferenceCubit);
    getIt.registerSingletonAsync<CameraCapturer>(
      () async => cameraCapturer,
    );
  });

  tearDown(() async {
    await pushNotifications.close();
    await connectivityStream.close();
    await getIt.reset();
  });

  Widget loadPushNotificationListener() {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<AppSettings>(
          create: (_) => MockAppSettings(),
        ),
        BlocProvider<LoginCubit>(
          create: (_) => loginCubit,
        ),
      ],
      child: Builder(
        builder: (_) => MaterialApp(
          navigatorObservers: [navigatorObserver],
          localizationsDelegates: const [
            ...AppLocalizations.localizationsDelegates,
            ...BiWidgetLocalizations.localizationsDelegates,
          ],
          supportedLocales: const [
            ...AppLocalizations.supportedLocales,
            ...BiWidgetLocalizations.supportedLocales,
          ],
          home: Scaffold(
            body: PushNotificationListener(
              child: Container(),
            ),
          ),
        ),
      ),
    );
  }

  testWidgets(
    'checkIfThereIsSavedAppOpenedNotification is called on initState if device is byod',
    (WidgetTester tester) async {
      when(deviceSetupRepository.lastKnownDeviceType)
          .thenReturn(ResolvedDeviceType.byod);
      await tester.pumpWidget(loadPushNotificationListener());
      expect(cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled, true);
    },
  );

  testWidgets(
    'checkIfThereIsSavedAppOpenedNotification is not called on initState if device is mdm',
    (WidgetTester tester) async {
      when(deviceSetupRepository.lastKnownDeviceType)
          .thenReturn(ResolvedDeviceType.mdm);
      await tester.pumpWidget(loadPushNotificationListener());
      expect(cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled, false);
    },
  );

  testWidgets(
      'checkIfThereIsSavedAppOpenedNotification is called on lifecycle change to resumed or detached if device is byod',
      (WidgetTester tester) async {
    when(deviceSetupRepository.lastKnownDeviceType)
        .thenReturn(ResolvedDeviceType.byod);
    await tester.pumpWidget(loadPushNotificationListener());

    cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled = false;
    tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.detached);
    expect(cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled, true);

    cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled = false;
    tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.resumed);
    expect(cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled, true);
  });

  testWidgets(
      'checkIfThereIsSavedAppOpenedNotification is not called on lifecycle change to resumed or detached if device is mdm',
      (WidgetTester tester) async {
    when(deviceSetupRepository.lastKnownDeviceType)
        .thenReturn(ResolvedDeviceType.mdm);
    await tester.pumpWidget(loadPushNotificationListener());

    cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled = false;
    tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.detached);
    expect(cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled, false);

    cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled = false;
    tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.resumed);
    expect(cubit.wasCheckIfThereIsSavedAppOpenedNotificationCalled, false);
  });

  testWidgets(
      'Displays enrollment failed message when enrollment fails and redirects to enrollment page',
      (WidgetTester tester) async {
    final PushPayload enrollmentPushPayLoad = PushPayload(
      action: PushNotificationType.enrollmentFailed.value,
      title: 'Test enrollment title',
      message: 'Test Enrollment message',
    );

    await tester.pumpWidget(loadPushNotificationListener());

    cubit.emit(
      PushNotificationListenerState.initial(false).copyWith(
        newPushPayLoad: enrollmentPushPayLoad,
      ),
    );

    await tester.pumpAndSettle();
    await tester.pump(const Duration(seconds: 6, milliseconds: 100));

    expect(find.text(l10n.enrollment), findsOneWidget);

    await tester.pump(const Duration(seconds: 3, milliseconds: 100));
    verify(clientSettingsRepository.reloadClientSettings()).called(1);
  });

  void setUpSelfReportPage() {
    getIt.registerSingleton<SelfReportCubit>(selfReportCubit);
  }

  testWidgets(
      'Handles report type push notification correctly and save push payload if token expired when tap on self report',
      (WidgetTester tester) async {
    when(biTokenManager.isTokenExpired()).thenAnswer((_) async => true);
    setUpSelfReportPage();
    final PushPayload reportPushPayload = PushPayload(
      action: PushNotificationType.report.value,
      title: 'Test Report Title',
      message: 'Test Report Message',
    );

    await tester.pumpWidget(loadPushNotificationListener());
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(newPushPayLoad: reportPushPayload),
    );

    await tester.pumpAndSettle();
    await tester.pump();
    expect(find.text(reportPushPayload.snackBarMessage), findsOneWidget);

    await tester.tap(find.text(l10n.selfReport));
    await tester.pump();

    verify(cubit.savePushPayloadIfTokenExpired(reportPushPayload)).called(1);
    verify(
      sharedPreferencesService.saveAsJson(
        PushNotificationListenerCubit.pendingPushPayLoadPrefsKey,
        reportPushPayload.toJson(),
      ),
    ).called(1);
  });

  testWidgets(
      'Handles report type push notification correctly and not saving push payload if token not expired when tap on self report',
      (WidgetTester tester) async {
    setUpSelfReportPage();
    final PushPayload reportPushPayload = PushPayload(
      action: PushNotificationType.report.value,
      title: 'Test Report Title',
      message: 'Test Report Message',
    );

    // Act
    await tester.pumpWidget(loadPushNotificationListener());
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(newPushPayLoad: reportPushPayload),
    );
    await tester.pumpAndSettle();
    await tester.pump();

    // Assert
    expect(find.text(reportPushPayload.snackBarMessage), findsOneWidget);

    await tester.tap(find.text(l10n.selfReport));
    await tester.pump();
    verify(cubit.savePushPayloadIfTokenExpired(reportPushPayload)).called(1);
    verifyNever(
      sharedPreferencesService.saveAsJson(
        PushNotificationListenerCubit.pendingPushPayLoadPrefsKey,
        reportPushPayload.toJson(),
      ),
    );
  });

  testWidgets(
      'Handles report type background push notification correctly by saving push payload if user is logged out when tap on self report',
      (WidgetTester tester) async {
    setUpSelfReportPage();
    final PushPayload reportPushPayload = PushPayload(
      action: PushNotificationType.report.value,
      title: 'Test Report Title',
      message: 'Test Report Message',
    );
    when(authenticationRepository.getCurrentUserSession()).thenReturn(
      null,
    );

    // Act
    await tester.pumpWidget(loadPushNotificationListener());
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(savedPushPayLoad: reportPushPayload),
    );
    await tester.pumpAndSettle();
    await tester.pump();
    verify(
      sharedPreferencesService.saveAsJson(
        PushNotificationListenerCubit.pendingPushPayLoadPrefsKey,
        reportPushPayload.toJson(),
      ),
    ).called(1);
  });
  testWidgets(
      'Handles report type background push notification correctly by navigating to self report page if user is logged in when tap on self report',
      (WidgetTester tester) async {
    when(selfReportCubit.state).thenReturn(
      SelfReportState(
        status: SelfReportStatus.completed,
        stateMachine: SelfReportStateMachine(const []),
      ),
    );
    setUpSelfReportPage();
    final PushPayload reportPushPayload = PushPayload(
      action: PushNotificationType.report.value,
      title: 'Test Report Title',
      message: 'Test Report Message',
    );

    // Act
    await tester.pumpWidget(loadPushNotificationListener());
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(savedPushPayLoad: reportPushPayload),
    );
    await tester.pump();
    await tester.pumpAndSettle(const Duration(seconds: 3));
    expect(find.byType(SelfReportPage), findsOneWidget);
    verifyNever(
      sharedPreferencesService.saveAsJson(
        PushNotificationListenerCubit.pendingPushPayLoadPrefsKey,
        reportPushPayload.toJson(),
      ),
    );
  });

  testWidgets(
      'Shows phone call permissions dialog when required permissions are not granted',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(showRequiredPhoneCallPermissionsDialog: true),
    );

    await tester.pumpAndSettle();

    expect(find.text(l10n.openPhoneAccountSettings), findsOneWidget);
  });

  testWidgets('Shows caller hung up dialog when caller hangs up',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());

    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(showCallerHungUpDialog: true),
    );

    await tester.pumpAndSettle();

    expect(find.text(l10n.call), findsOneWidget);
    expect(find.text(l10n.callerHungUpMessage), findsOneWidget);
  });

  testWidgets('Shows answer phone call dialog when we have Incoming call',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());

    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(showIncomingCallDialog: true),
    );

    await tester.pumpAndSettle();

    expect(find.byIcon(FontAwesomeIcons.solidPhone), findsOneWidget);
    expect(find.text(l10n.phoneCallDialogTitle), findsOneWidget);
  });

  testWidgets(
      'Shows outgoing call dialog when coming from background with call connected and pin validation required',
      (WidgetTester tester) async {
    authState.value = const EmbarkAuthenticationState.validatePin();
    await tester.pumpWidget(loadPushNotificationListener());

    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(showOutgoingCallDialog: true),
    );

    await tester.pumpAndSettle();

    expect(find.text(l10n.callInProgress), findsOneWidget);
  });

  testWidgets(
      'Displays change request approval snackbar when receiving notification',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.notification.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(newPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
  });

  testWidgets(
      'Let the previous snackbar message show to its full duration before displaying new one',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.notification.value,
      title: 'CR  Title',
      message: 'CR  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(newPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);

    // Now simulate a new notification and wait for it to show
    final newNotificationPushPayload = PushPayload(
      action: PushNotificationType.notification.value,
      title: 'New CR  Title',
      message: 'New CR  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(newPushPayLoad: newNotificationPushPayload),
    );

    await tester.pumpAndSettle();

    // Check that we are still showing the old snackbar
    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);

    // Wait for the old snackbar to go away
    await tester.pumpAndSettle(const Duration(seconds: 5));

    // Check that we are showing the new snackbar
    expect(
      find.text(newNotificationPushPayload.snackBarMessage),
      findsOneWidget,
    );
  });

  testWidgets('Displays zone change message when zone is changed ',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.tracking.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(newPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
  });

  void setUpCheckInPage(
    MockAppRepository appRepository,
    MockFaceDetector faceDetector,
    BlinkDetector blinkDetector,
    CameraUtil cameraUtil,
    DeviceSetupRepository deviceSetupRepository,
  ) {
    getIt
      ..registerSingleton<BlinkDetector>(blinkDetector)
      ..registerSingleton(MdmShowAuth());

    getIt.registerFactory<CameraUtil>(() => cameraUtil);
  }

  group('check in', () {
    testWidgets('Displays check in message when enrollment success',
        (WidgetTester tester) async {
      setUpCheckInPage(
        appRepository,
        MockFaceDetector(),
        BlinkDetector(
          faceDetectionService,
        ),
        MockCameraUtil(),
        deviceSetupRepository,
      );
      final PushPayload enrollmentPushPayLoad = PushPayload(
        action: PushNotificationType.checkin.value,
        title: 'Test enrollment title',
        message: 'Test Enrollment message',
      );

      await tester.pumpWidget(
        loadPushNotificationListener(),
      );

      cubit.emit(
        PushNotificationListenerState.initial(false).copyWith(
          newPushPayLoad: enrollmentPushPayLoad,
        ),
      );

      await tester.pumpAndSettle();
      await tester.pump();

      /// expecting to have both 'check-in' banner &
      /// check in page both in scope now
      expect(find.text(l10n.checkIn), findsNWidgets(2));
    });
    testWidgets('Saved push payload does not display notification message',
        (WidgetTester tester) async {
      setUpCheckInPage(
        appRepository,
        MockFaceDetector(),
        BlinkDetector(faceDetectionService),
        MockCameraUtil(),
        deviceSetupRepository,
      );
      final PushPayload enrollmentPushPayLoad = PushPayload(
        action: PushNotificationType.checkin.value,
        title: 'Test enrollment title',
        message: 'Test Enrollment message',
      );

      await tester.pumpWidget(
        loadPushNotificationListener(),
      );

      cubit.emit(
        PushNotificationListenerState.initial(false).copyWith(
          savedPushPayLoad: enrollmentPushPayLoad,
        ),
      );

      await tester.pumpAndSettle();
      await tester.pump();

      /// expecting to have just check in page in scope now
      expect(find.text(l10n.checkIn), findsNWidgets(1));
      // Do not play notification sound
      verifyNever(
        remoteNotificationsService.showNotification(
          any,
          isInAppNotification: true,
        ),
      );
    });
    testWidgets(
        'Displays check in message and does not push screeen if coming from enrollment',
        (WidgetTester tester) async {
      setUpCheckInPage(
        appRepository,
        MockFaceDetector(),
        BlinkDetector(
          faceDetectionService,
        ),
        MockCameraUtil(),
        deviceSetupRepository,
      );
      authState.value = const EmbarkAuthenticationState.enrollment();
      final PushPayload enrollmentPushPayLoad = PushPayload(
        action: PushNotificationType.checkin.value,
        title: 'Test enrollment title',
        message: 'Test Enrollment message',
      );

      await tester.pumpWidget(
        loadPushNotificationListener(),
      );

      cubit.emit(
        PushNotificationListenerState.initial(false).copyWith(
          newPushPayLoad: enrollmentPushPayLoad,
        ),
      );

      await tester.pumpAndSettle();
      await tester.pump();

      expect(find.text(l10n.checkIn), findsNothing);

      authState.value =
          AuthenticationInitial.authenticated(someLoggedInUser, false);
      await tester.pumpAndSettle();

      /// expecting to have both 'check-in' banner, but not page
    });
    testWidgets('Does not display message if the PN is for enrollment success',
        (WidgetTester tester) async {
      setUpCheckInPage(
        appRepository,
        MockFaceDetector(),
        BlinkDetector(
          faceDetectionService,
        ),
        MockCameraUtil(),
        deviceSetupRepository,
      );
      authState.value = const EmbarkAuthenticationState.enrollment();
      final PushPayload enrollmentPushPayLoad = PushPayload(
        action: PushNotificationType.checkin.value,
        title: l10n.enrollmentCompletePNTitle,
        message: 'Test Enrollment message',
      );

      await tester.pumpWidget(
        loadPushNotificationListener(),
      );

      cubit.emit(
        PushNotificationListenerState.initial(false).copyWith(
          newPushPayLoad: enrollmentPushPayLoad,
        ),
      );

      await tester.pumpAndSettle();
      await tester.pump();

      expect(find.text(l10n.checkIn), findsNothing);
      authState.value =
          AuthenticationInitial.authenticated(someLoggedInUser, false);
      await tester.pumpAndSettle();

      /// expecting to have both 'check-in' banner, but not page
      expect(find.text(l10n.checkIn), findsNWidgets(0));
    });
    testWidgets(
        'Does not display message if the PN is for enrollment success in iOS.'
        'PN title for iOS is empty and the message contains the title plus the message',
        (WidgetTester tester) async {
      setUpCheckInPage(
        appRepository,
        MockFaceDetector(),
        BlinkDetector(
          faceDetectionService,
        ),
        MockCameraUtil(),
        deviceSetupRepository,
      );
      authState.value = const EmbarkAuthenticationState.enrollment();
      final PushPayload enrollmentPushPayLoad = PushPayload(
        action: PushNotificationType.checkin.value,
        title: '',
        message: '${l10n.enrollmentCompletePNTitle} Test Enrollment message',
      );

      await tester.pumpWidget(
        loadPushNotificationListener(),
      );

      cubit.emit(
        PushNotificationListenerState.initial(false).copyWith(
          newPushPayLoad: enrollmentPushPayLoad,
        ),
      );

      await tester.pumpAndSettle();
      await tester.pump();

      expect(find.text(l10n.checkIn), findsNothing);
      authState.value =
          AuthenticationInitial.authenticated(someLoggedInUser, false);
      await tester.pumpAndSettle();

      /// expecting to have both 'check-in' banner, but not page
      expect(find.text(l10n.checkIn), findsNWidgets(0));
    });
  });

  void setUpLoginPage() {
    getIt.registerSingleton<LoginCubit>(loginCubit);
    when(loginCubit.state).thenReturn(
      const EmbarkLoginState(status: EmbarkLoginStatus.updateRequired),
    );
  }

  testWidgets(
      'Navigates to login page on expired token notification is emitted',
      (WidgetTester tester) async {
    setUpLoginPage();

    await tester.pumpWidget(loadPushNotificationListener());
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(tokenState: TokenState.expired),
    );

    await tester.pumpAndSettle();
    verify(authenticationRepository.reevaluateAuthenticationStatus()).called(1);
  });

  testWidgets(
      'Displays notification snackbar with dismiss label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.notification.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.dismiss), findsOneWidget);
  });

  testWidgets(
      'Displays check-in notification snackbar with check-in label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);
    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.checkin.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false).copyWith(
        loggedOutPushPayLoad: notificationPushPayload,
      ),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.checkIn), findsOneWidget);
  });

  testWidgets(
      'Displays check-in notification snackbar with blank snack bar action label in logged out state when VM/Smlk svc plan is true',
      (WidgetTester tester) async {
    when(
      sharedPreferencesService.getBool(
        SharedPreferencesKeys.isVWSmlkCommunication.key,
        false,
      ),
    ).thenAnswer((realInvocation) => true);

    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.checkin.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(true)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    final snackBarActionText = find.descendant(
      matching: find.byType(Text),
      of: find.byType(SnackBarAction),
    );

    final actualSnackBarLabelText =
        snackBarActionText.evaluate().single.widget as Text;

    // VW/Smlk svc plan does not display 'Check-In' on the banner message snack
    // bar actionlabel and does not re-direct to the Check-In page, so
    // 'Check-In' text should not be found
    expect(actualSnackBarLabelText.data, equals(''));
    expect(find.text(l10n.checkIn), findsNothing);
  });

  testWidgets(
      'Displays self report notification snackbar with self report label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.report.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.selfReport), findsOneWidget);
  });

  testWidgets(
      'Displays failed enrollment notification snackbar with check-in label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.enrollmentFailed.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.enrollment), findsOneWidget);
  });

  testWidgets(
      'Displays failed enrollment notification snackbar with blank action label in logged out state when VM/Smlk svc plan is true',
      (WidgetTester tester) async {
    when(
      sharedPreferencesService.getBool(
        SharedPreferencesKeys.isVWSmlkCommunication.key,
        false,
      ),
    ).thenAnswer((realInvocation) => true);

    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.enrollmentFailed.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(true)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    final snackBarActionText = find.descendant(
      matching: find.byType(Text),
      of: find.byType(SnackBarAction),
    );

    final actualSnackBarLabelText =
        snackBarActionText.evaluate().single.widget as Text;

    // VW/Smlk svc plan does not display 'Enrollment' on the banner message snack
    // bar actionlabel and does not re-direct to the enrollment page,
    // so 'Enrollment' text should not be found
    expect(actualSnackBarLabelText.data, equals(''));
    expect(find.text(l10n.enrollment), findsNothing);
  });

  testWidgets(
      'Displays tracking notification snackbar with dismiss label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.tracking.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.dismiss), findsOneWidget);
  });

  testWidgets(
      'Displays calendar notification snackbar with view label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.calendar.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.view), findsOneWidget);
  });

  testWidgets(
      'Displays message notification snackbar with view label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.message.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.view), findsOneWidget);
  });

  testWidgets(
      'Displays reminder notification snackbar with view label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.reminder.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.view), findsOneWidget);
  });

  testWidgets(
      'Displays reminder notification snackbar with view label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.reminder.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.view), findsOneWidget);
  });

  testWidgets(
      'Displays media notification snackbar with view label in logged out state',
      (WidgetTester tester) async {
    await tester.pumpWidget(loadPushNotificationListener());
    when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

    final PushPayload notificationPushPayload = PushPayload(
      action: PushNotificationType.media.value,
      title: 'Test notification  Title',
      message: 'Test notification  Message',
    );
    cubit.emit(
      PushNotificationListenerState.initial(false)
          .copyWith(loggedOutPushPayLoad: notificationPushPayload),
    );

    await tester.pumpAndSettle();

    expect(find.text(notificationPushPayload.snackBarMessage), findsOneWidget);
    expect(find.text(l10n.view), findsOneWidget);
  });

  testWidgets(
    'Display message notification snackbar when state is emitted for iOS platform when title is empty',
    (WidgetTester tester) async {
      await tester.pumpWidget(loadPushNotificationListener());
      when(authenticationRepository.getCurrentUserSession()).thenReturn(null);

      final PushPayload notificationPushPayload = PushPayload(
        action: PushNotificationType.notification.value,
        title: 'Test notification  Title',
        message: 'Test notification  Message',
      );
      cubit.emit(
        PushNotificationListenerState.initial(false)
            .copyWith(loggedOutPushPayLoad: notificationPushPayload),
      );

      await tester.pumpAndSettle();
      expect(find.text(notificationPushPayload.message!), findsOneWidget);
    },
    variant: TargetPlatformVariant.only(TargetPlatform.iOS),
  );

  testWidgets(
      'Displays enrollment message with blank snack bar action label and does not re-direct when VM/Smlk svc plan is true',
      (WidgetTester tester) async {
    when(
      sharedPreferencesService.getBool(
        SharedPreferencesKeys.isVWSmlkCommunication.key,
        false,
      ),
    ).thenAnswer((realInvocation) => true);

    final PushPayload enrollmentPushPayLoad = PushPayload(
      action: PushNotificationType.enrollmentFailed.value,
      title: 'Test Title: Enrollment',
      message: 'Test Message: Failed',
    );

    await tester.pumpWidget(loadPushNotificationListener());

    cubit.emit(
      PushNotificationListenerState.initial(true).copyWith(
        newPushPayLoad: enrollmentPushPayLoad,
      ),
    );

    await tester.pumpAndSettle();
    await tester.pump(const Duration(seconds: 6, milliseconds: 100));

    final snackBarActionText = find.descendant(
      matching: find.byType(Text),
      of: find.byType(SnackBarAction),
    );

    final actualSnackBarLabelText =
        snackBarActionText.evaluate().single.widget as Text;

    // VW/Smlk svc plan does not display 'Enrollment' on the banner message snack
    // bar actionlabel and does not re-direct to the enrollment page,
    // so 'Enrollment' text should not be found
    expect(actualSnackBarLabelText.data, equals(''));
    expect(find.text(l10n.enrollment), findsNothing);

    await tester.pump(const Duration(seconds: 3, milliseconds: 100));
    expect(find.byType(FacialEnrollmentPage), findsNothing);
  });

  testWidgets(
      'Displays check-in message with blank snack bar action label and does not re-direct when VM/Smlk svc plan is true',
      (WidgetTester tester) async {
    when(
      sharedPreferencesService.getBool(
        SharedPreferencesKeys.isVWSmlkCommunication.key,
        false,
      ),
    ).thenAnswer((realInvocation) => true);

    setUpCheckInPage(
      appRepository,
      MockFaceDetector(),
      BlinkDetector(
        faceDetectionService,
      ),
      MockCameraUtil(),
      deviceSetupRepository,
    );

    final PushPayload checkInPushPayLoad = PushPayload(
      action: PushNotificationType.checkin.value,
      title: 'Test Title Biometric Check-In',
      message: 'Test Message: Missed',
    );

    await tester.pumpWidget(
      loadPushNotificationListener(),
    );

    cubit.emit(
      PushNotificationListenerState.initial(true).copyWith(
        newPushPayLoad: checkInPushPayLoad,
      ),
    );

    await tester.pumpAndSettle(
      const Duration(seconds: 3, milliseconds: 600),
    );
    await tester.pump();

    final snackBarActionText = find.descendant(
      matching: find.byType(Text),
      of: find.byType(SnackBarAction),
    );

    final actualSnackBarLabelText =
        snackBarActionText.evaluate().single.widget as Text;

    // VW/Smlk svc plan does not display 'Check-In' on the banner message snack
    // bar actionlabel and does not re-direct to the Check-In page, so
    // 'Check-In' text should not be found
    expect(actualSnackBarLabelText.data, equals(''));
    expect(find.text(l10n.checkIn), findsNothing);

    await tester.pump(const Duration(seconds: 3, milliseconds: 100));
    expect(find.byType(CheckInView), findsNothing);
  });
}
