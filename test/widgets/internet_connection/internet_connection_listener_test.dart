import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_font_awesome_pro/bi_flutter_font_awesome_pro.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/internet_connection/internet_connection_listener.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('InternetConnectionListener: ', () {
    late MockAppRepository mockAppRepository;
    late MockConnectivity mockConnectivity;
    late BiStreamController<List<ConnectivityResult>> iCStreamController;

    late AppLocalizations l10n;
    var connectStatus = [ConnectivityResult.none];

    setUp(() async {
      mockAppRepository = MockAppRepository();
      getIt.registerSingleton<AppRepository>(
        mockAppRepository,
        instanceName: 'AppRepository',
      );

      mockConnectivity = MockConnectivity();
      getIt.registerSingleton(
        mockConnectivity,
        instanceName: 'Connectivity',
      );

      iCStreamController =
          BiStreamController<List<ConnectivityResult>>(sync: true);
      iCStreamController.onListen = () async {
        iCStreamController.add(await mockConnectivity.checkConnectivity());
      };

      when(mockConnectivity.checkConnectivity())
          .thenAnswer((_) => Future(() => connectStatus));

      when(mockConnectivity.onConnectivityChanged).thenAnswer(
        (_) => Stream<List<ConnectivityResult>>.value(
          connectStatus,
        ),
      );

      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCStreamController.stream,
      );

      l10n = await AppLocalizations.delegate.load(const Locale('en', 'US'));
    });

    tearDown(() async {
      await iCStreamController.close();
      await getIt.reset();
    });

    testWidgets('Connection Lost dialog appears when disconnected',
        (tester) async {
      await tester.load(
        widget: InternetConnectionListener(
          child: fakeChild(),
        ),
      );

      connectStatus = [ConnectivityResult.none];

      await tester.pump();
      await tester.pump();
      await tester.pumpAndSettle();

      final alertDialog = find.byType(AlertDialog);
      expect(alertDialog, findsOneWidget);

      final alertTextType = find.descendant(
        of: alertDialog,
        matching: find.text(l10n.noInternetConnection),
      );
      final alertText = alertTextType.evaluate().first.widget as Text;

      final alertIconType =
          find.descendant(of: alertDialog, matching: find.byType(FaIcon));
      final alertIcon = alertIconType.evaluate().first.widget as FaIcon;

      expect(alertDialog, findsOneWidget);
      expect(alertText.data, l10n.noInternetConnection);
      expect(alertIcon.icon, FontAwesomeIcons.solidCloudExclamation);
    });

    testWidgets('Connection Lost dialog not shown when connected',
        (tester) async {
      await tester.load(
        widget: InternetConnectionListener(
          child: fakeChild(),
        ),
      );
      connectStatus = [ConnectivityResult.wifi];
      iCStreamController.add(connectStatus);

      await tester.pumpAndSettle();

      final dismissedAlertDialog = find.byType(AlertDialog);
      expect(dismissedAlertDialog, findsNothing);
    });
  });
}

Widget fakeChild() {
  return Container();
}
