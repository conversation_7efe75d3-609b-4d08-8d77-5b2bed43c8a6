import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/call_history/cubit/call_history_cubit.dart';
import 'package:embark/call_history/views/_call_history_page.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';

import '../../unit_test.mocks.dart';
import '../widget_test_helpers/golden_wrapper.dart';

void main() {
  group('CallHistoryPage Golden Test', () {
    late BiStreamController<List<ConnectivityResult>> connectivityStream;
    late MockVoIPCallRepository mockVoIPCallRepository;
    late MockAppRepository appRepository;

    GetVoIPCallHistoryResponse createMockVoIPCallHistoryResponse() {
      final constantDateTime = DateTime(2020, 1, 1, 12, 0).toUtc();

      final List<VoIPCallDetailResponse> mockHistory =
          List.generate(10, (index) {
        return VoIPCallDetailResponse(
          clientId: index,
          callId: 'call_$index',
          clientVoipNumber: 'client_voip_$index',
          outsideNumber: '+11234567890',
          isOutbound: index % 2 == 0,
          statusId: VoIPCallStatus.values[index % VoIPCallStatus.values.length],
          createDate: constantDateTime,
          duration: index * 60,
        );
      });
      return GetVoIPCallHistoryResponse(history: mockHistory);
    }

    setUp(() {
      connectivityStream = BiStreamController.broadcast();
      mockVoIPCallRepository = MockVoIPCallRepository();
      appRepository = MockAppRepository();

      getIt.registerSingleton(connectivityStream.stream);

      when(appRepository.appLifecycleStateStream)
          .thenAnswer((_) => Stream.value(AppLifecycleState.resumed));

      getIt.registerFactory(
        () => CallHistoryCubit(
          voipCallRepository: mockVoIPCallRepository,
        ),
      );
    });

    tearDown(() async {
      await connectivityStream.close();
      await getIt.reset();
    });

    // TODO: after fixing the test so that it runs successfully on local devices
    // we should stop skipping this tst
    testGoldens(
      'call history list renders correctly',
      (widgetTester) async {
        when(mockVoIPCallRepository.getVoIPCallHistory())
            .thenAnswer((_) async => createMockVoIPCallHistoryResponse());

        final builder = DeviceBuilder()
          ..addScenario(
            name: 'with phone number',
            widget: const CallHistoryPage(),
          );

        await widgetTester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

        await screenMatchesGolden(
          widgetTester,
          'call_history_list',
        );
      },
      skip: true,
    );

    testGoldens('call history empty state renders correctly',
        (widgetTester) async {
      when(mockVoIPCallRepository.getVoIPCallHistory()).thenAnswer(
        (_) async => const GetVoIPCallHistoryResponse(history: []),
      );

      final builder = DeviceBuilder()
        ..addScenario(
          name: 'with empty history list',
          widget: const CallHistoryPage(),
        );

      await widgetTester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);

      await screenMatchesGolden(
        widgetTester,
        'call_history_empty_state',
      );
    });
  });
}
