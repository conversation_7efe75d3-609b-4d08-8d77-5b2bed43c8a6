import 'dart:async';

import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/documents/cubit/_document_state.dart';
import 'package:embark/documents/cubit/_documents_repository.dart';
import 'package:embark/documents/cubit/add_document/add_document_cubit.dart';
import 'package:embark/documents/cubit/add_document/add_document_state.dart';
import 'package:embark/documents/views/_documents_page.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/data/transparent_image.dart';
import '../widget_test_helpers/stub_repositories.dart';
import '../widget_test_helpers/text_matcher.dart';
import 'documents_page_object.dart';

AppLocalizations l10n = AppLocalizationsEn();

final courtDocuments = MediaItem(
  id: '1',
  fileType: 'court_documents',
  isAck: false,
  requiresAck: true,
  createDate: DateTime(2022, 2, 2, 10),
);

final paroleDocument = MediaItem(
  id: '4',
  fileType: 'parole',
  isAck: true,
  requiresAck: true,
  createDate: DateTime(2022, 2, 4, 10),
);

void main() {
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late MockDocumentsRepository documentsRepository;
  late MockBiLogger logger;
  late StubBannerMessgeRepository bannerMessgeRepository;
  late StubLocaleStreamRepository localeStreamRepository;
  late AddDocumentCubit addDocumentCubit;
  late MockAppRepository appRepository;
  setUp(() {
    localeStreamRepository = StubLocaleStreamRepository();
    getIt.registerFactory(() => addDocumentCubit);
    getIt.registerSingleton<LocaleStreamRepository>(localeStreamRepository);
    bannerMessgeRepository = StubBannerMessgeRepository();
    getIt.registerSingleton<BannerMessageRepository>(bannerMessgeRepository);
    logger = MockBiLogger();
    documentsRepository = MockDocumentsRepository();
    appRepository = MockAppRepository();
    iCSController = BiStreamController.broadcast(sync: true);
    getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
      iCSController.stream,
    );
    getIt.registerSingleton<BiLogger>(logger);
    getIt.registerSingleton<AppRepository>(
      appRepository,
      instanceName: 'AppRepository',
    );
    getIt.registerSingleton<DocumentsRepository>(documentsRepository);
    addDocumentCubit = AddDocumentCubit(
      getIt.get(),
      getIt.get(),
    )..setTitleAndId('title', 1);
  });
  tearDown(() async {
    await iCSController.close();
    await getIt.reset();
  });
  void givenReturnsTwoDocuments() {
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments, paroleDocument];
      },
    );
  }

  testGoldens('displays the screen with item', (tester) async {
    givenReturnsTwoDocuments();
    await _buildPage(tester);
    expect(documentsPageObject, findsOneWidget);

    await screenMatchesGolden(tester, 'documents_screen');
  });

  group('filters', () {
    final allDropdownMenuFinder =
        documentsPageObject.filterMenu.itemWithKey('All');
    final courtDropdownMenuItemFinder =
        documentsPageObject.filterMenu.itemWithKey(courtDocuments.fileType!);
    final paroleDropdownMenuFinder =
        documentsPageObject.filterMenu.itemWithKey(paroleDocument.fileType!);

    testWidgets('Has filters visible and can filter and can then select all',
        (tester) async {
      givenReturnsTwoDocuments();
      await _buildPage(tester);
      await tester.tap(documentsPageObject.filterMenu);
      await tester.pumpAndSettle();

      expect(documentsPageObject.allCells, findsNWidgets(2));
      expect(
        allDropdownMenuFinder,
        findsOneWidget,
      );

      expect(
        courtDropdownMenuItemFinder,
        TextMatcher(courtDocuments.fileType!),
      );
      await tester.tap(courtDropdownMenuItemFinder);
      await tester.pumpAndSettle();
      expect(documentsPageObject.allCells, findsOneWidget);
      await tester.tap(documentsPageObject.filterMenu);
      await tester.pumpAndSettle();
      await tester.tap(allDropdownMenuFinder);
      await tester.pumpAndSettle();
      expect(documentsPageObject.allCells, findsNWidgets(2));
    });
    testWidgets(
        'getting new data will update available filters even while filtering',
        (tester) async {
      when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
        (realInvocation) async {
          return [courtDocuments];
        },
      );
      await _buildPage(tester);
      await tester.tap(documentsPageObject.filterMenu);
      await tester.pumpAndSettle();
      expect(documentsPageObject.allCells, findsOneWidget);

      expect(
        courtDropdownMenuItemFinder,
        TextMatcher(courtDocuments.fileType!),
      );
      expect(paroleDropdownMenuFinder, findsNothing);
      await tester.tap(courtDropdownMenuItemFinder);
      await tester.pumpAndSettle();
      when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
        (realInvocation) async {
          return [courtDocuments, paroleDocument];
        },
      );
      await _pullToRefresh(tester);
      expect(documentsPageObject.allCells, findsNWidgets(1));

      await tester.tap(documentsPageObject.filterMenu);
      await tester.pumpAndSettle();
      await tester.tap(allDropdownMenuFinder);
      await tester.pumpAndSettle();
      expect(documentsPageObject.allCells, findsNWidgets(2));
      await tester.tap(documentsPageObject.filterMenu);
      await tester.pumpAndSettle();
      expect(paroleDropdownMenuFinder, findsOneWidget);
    });
    testWidgets('When deleting media type that is filtered, go back to all',
        (tester) async {
      givenReturnsTwoDocuments();
      await _buildPage(tester);
      await tester.tap(documentsPageObject.filterMenu);
      await tester.pumpAndSettle();
      await tester.tap(courtDropdownMenuItemFinder);
      await tester.pumpAndSettle();
      expect(documentsPageObject.allCells, findsNWidgets(1));
      expect(documentsPageObject.cellForMedia(courtDocuments), findsOneWidget);

      when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
        (realInvocation) async {
          return [paroleDocument];
        },
      );
      await _pullToRefresh(tester);
      expect(documentsPageObject.cellForMedia(courtDocuments), findsNothing);
      expect(documentsPageObject.cellForMedia(paroleDocument), findsOneWidget);
      expect(find.text('All'), findsOneWidget);
    });
  });
  testWidgets('shows loading screen while loading', (tester) async {
    final completer = Completer<List<MediaItem>>();
    when(documentsRepository.fetchDocumentsMetaData())
        .thenAnswer((_) => completer.future);
    await _buildPage(tester);
    expect(documentsPageObject.loading, findsOneWidget);
    completer.complete([courtDocuments]);
    await tester.pumpAndSettle();
    expect(documentsPageObject.loading, findsNothing);
  });
  testWidgets('triggers refresh when no connectivity', (tester) async {
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments];
      },
    );
    await _buildPage(tester);
    iCSController.sink.add([ConnectivityResult.none]);
    await tester.pump();
    iCSController.sink.add([ConnectivityResult.mobile]);
    await _buildPage(tester);
    verify(documentsRepository.fetchDocumentsMetaData()).called(2);
  });

  testWidgets('user can pull to refresh', (tester) async {
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments];
      },
    );
    await _buildPage(tester);
    expect(documentsPageObject, findsOneWidget);
    final courtCell = documentsPageObject.cellForMedia(courtDocuments);

    expect(courtCell, findsOneWidget);

    final completer = Completer<List<MediaItem>>();
    when(documentsRepository.fetchDocumentsMetaData())
        .thenAnswer((_) => completer.future);
    await _pullToRefresh(tester);

    expect(documentsPageObject.loading, findsOneWidget);
    completer.complete([paroleDocument]);
    await tester.pump();
    expect(courtCell, findsNothing);
    expect(documentsPageObject.loading, findsNothing);
    expect(
      documentsPageObject.cellForMedia(paroleDocument),
      findsOneWidget,
    );
  });
  testWidgets(
      'displays refresh when api returns an error, then user can refresh',
      (tester) async {
    when(documentsRepository.fetchDocumentsMetaData()).thenThrow(
      (realInvocation) async {
        return Exception();
      },
    );
    await _buildPage(tester);
    expect(documentsPageObject, findsOneWidget);
    expect(documentsPageObject.noItemsScreen, findsOneWidget);
    expect(documentsPageObject.noItemsScreen.refresh, findsOneWidget);

    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments];
      },
    );

    await tester.tap(documentsPageObject.noItemsScreen.refresh);
    await tester.pump();
    expect(documentsPageObject.noItemsScreen, findsNothing);
    expect(
      documentsPageObject.cellForMedia(courtDocuments),
      findsOneWidget,
    );
  });
  testWidgets('Should show no documents text if no documents', (tester) async {
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [];
      },
    );
    await _buildPage(tester);
    expect(documentsPageObject.noItemsScreen, findsOneWidget);
  });
  testWidgets('Clicking on card takes user to documents Screen',
      (tester) async {
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments];
      },
    );

    await _buildPage(tester);
    final cell = documentsPageObject.cellForMedia(courtDocuments);
    await tester.tap(cell);
    await tester.pumpAndSettle();
    expect(documentsPageObject.documentDetailsScreen, findsOneWidget);
    expect(documentsPageObject, findsNothing);

    await tester.pageBack();
    await tester.pumpAndSettle();
    expect(documentsPageObject.documentDetailsScreen, findsNothing);
    expect(documentsPageObject, findsOneWidget);
  });
  testWidgets('clicking on ack sends one message', (tester) async {
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments];
      },
    );
    final messages = <BannerMessage>[];
    bannerMessgeRepository.bannerMessageStream.listen((event) {
      messages.add(event);
    });

    await _buildPage(tester);
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments.copyWith(isAck: true)];
      },
    );

    final cell = documentsPageObject.cellForMedia(courtDocuments);
    await tester.tap(cell);
    await tester.pumpAndSettle();
    expect(documentsPageObject.documentDetailsScreen, findsOneWidget);
    expect(documentsPageObject, findsNothing);
    await tester
        .tap(documentsPageObject.documentDetailsScreen.acknowledgeButton);
    await tester.pumpAndSettle();
    expect(messages, hasLength(1));
  });
  group('add document', () {
    testWidgets('if no available media do not show Fab', (tester) async {
      when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
        (realInvocation) async {
          return [courtDocuments];
        },
      );
      when(documentsRepository.fetchAvailableMediaTypesToUpload()).thenAnswer(
        (realInvocation) async => [],
      );
      await _buildPage(tester);
      await tester.pumpAndSettle();
      expect(documentsPageObject, findsOneWidget);
      expect(documentsPageObject.floatingActionButton, findsNothing);
    });
    testWidgets('if error getting media type do not show fab and show error',
        (tester) async {
      when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
        (realInvocation) async {
          return [courtDocuments];
        },
      );
      when(documentsRepository.fetchAvailableMediaTypesToUpload()).thenAnswer(
        (realInvocation) async => throw Exception(),
      );
      BannerMessage? message;
      bannerMessgeRepository.bannerMessageStream.listen((event) {
        message = event;
      });
      await _buildPage(tester);
      await tester.pumpAndSettle();
      expect(documentsPageObject, findsOneWidget);
      expect(documentsPageObject.floatingActionButton, findsNothing);
      expect(message, ErrorBannerMessage(text: l10n.genericError));
    });

    testWidgets(
        'Clicking on FAB takes the user to the add document selection menu',
        (tester) async {
      when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
        (realInvocation) async {
          return [courtDocuments];
        },
      );
      when(documentsRepository.fetchAvailableMediaTypesToUpload()).thenAnswer(
        (realInvocation) async => [
          SmartMediaTypes(name: 'court document', id: 1),
          SmartMediaTypes(name: 'CCA document', id: 55),
        ],
      );
      await _buildPage(tester);
      await tester.tap(documentsPageObject.floatingActionButton);
      await tester.pumpAndSettle();
      final selectGroup =
          documentsPageObject.addDocumentScreen.addDocumentSelectGroup;
      expect(
        selectGroup.addScreenGroupSelection(1),
        findsOneWidget,
      );
      await tester.tap(selectGroup.addScreenGroupSelection(1));
      await tester.pumpAndSettle();

      expect(selectGroup, findsNothing);
      expect(documentsPageObject.addDocumentScreen, findsOneWidget);
      expect(
        documentsPageObject.addDocumentScreen.title,
        TextMatcher('court document'),
      );
      await tester.tap(documentsPageObject.addDocumentScreen.cancelButton);
      await tester.pump();
      await tester.tap(documentsPageObject.addDocumentScreen.cancelModal.yes);
      await tester.pumpAndSettle();
      expect(documentsPageObject.addDocumentScreen, findsNothing);
      expect(documentsPageObject, findsOneWidget);
    });
    testWidgets('screen is refreshed when document is added', (tester) async {
      when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
        (realInvocation) async {
          return [courtDocuments];
        },
      );
      when(documentsRepository.fetchAvailableMediaTypesToUpload()).thenAnswer(
        (realInvocation) async => [
          SmartMediaTypes(name: 'court document', id: 1),
          SmartMediaTypes(name: 'CCA document', id: 55),
        ],
      );
      await _buildPage(tester);
      await tester.tap(documentsPageObject.floatingActionButton);
      await tester.pump();
      final selectGroup =
          documentsPageObject.addDocumentScreen.addDocumentSelectGroup;
      expect(
        selectGroup.addScreenGroupSelection(1),
        findsOneWidget,
      );
      await tester.tap(selectGroup.addScreenGroupSelection(1));
      await tester.pumpAndSettle();

      expect(documentsPageObject.addDocumentScreen, findsOneWidget);
      addDocumentCubit.emit(
        addDocumentCubit.state.copyWith(
          status: AddDocumentStateStatus.picture(allImage: listOfTestImages(2)),
        ),
      );
      verify(documentsRepository.fetchDocumentsMetaData()).called(1);
      unawaited(addDocumentCubit.submit());
      await tester.pump();
      await tester.pump();
      expect(
        documentsPageObject.addDocumentScreen.finishedUploadingScreen,
        findsOneWidget,
      );
      verify(documentsRepository.fetchDocumentsMetaData()).called(1);
      await tester.pump(const Duration(seconds: 4));
      await tester.pump();
      expect(
        documentsPageObject.addDocumentScreen.finishedUploadingScreen,
        findsNothing,
      );
      expect(documentsPageObject.mainPage, findsOneWidget);
    });
  });
  testWidgets(
      'changing locale will cause refresh of data (for localization purposes)',
      (tester) async {
    when(documentsRepository.fetchDocumentsMetaData()).thenAnswer(
      (realInvocation) async {
        return [courtDocuments];
      },
    );
    await _buildPage(tester);
    verify(documentsRepository.fetchDocumentsMetaData()).called(1);
    verify(documentsRepository.fetchAvailableMediaTypesToUpload()).called(1);
    localeStreamRepository.setLocale(const Locale('es'));
    await tester.pump();
    verify(documentsRepository.fetchAvailableMediaTypesToUpload()).called(1);
    verify(documentsRepository.fetchDocumentsMetaData()).called(1);
  });
}

Future<void> _buildPage(WidgetTester tester) async {
  await tester.binding.setSurfaceSize(const Size(350, 600));
  tester.view.physicalSize = const Size(300, 600);
  tester.view.devicePixelRatio = 1;
  tester.platformDispatcher.textScaleFactorTestValue = 1;

  await tester.load(
    widget: const DocumentsPage(),
  );
  await tester.pump();
}

Future<void> _pullToRefresh(WidgetTester tester) async {
  await tester.fling(
    documentsPageObject.allCells.first,
    const Offset(0, 1000),
    1000,
  );
  await tester.pump(const Duration(seconds: 1));
  await tester.pump(const Duration(seconds: 1));
  await tester.pump(const Duration(seconds: 1));
}
