import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/permissions/cubit/permissions_cubit.dart';
import 'package:embark/permissions/permissions.dart';
import 'package:embark/starter.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/stubs/auth_manager.dart';
import '../widget_test_helpers/golden_wrapper.dart';
import 'permissions_page_page_object.dart';

final l10n = AppLocalizationsEn();

void main() {
  group('PermissionsPage', () {
    late BiStreamController<List<ConnectivityResult>> connectivityStream;
    late MockAppRepository appRepository;
    late MockEmbarkAuthenticationRepository embarkAuthenticationRepository;

    setUp(() {
      connectivityStream = BiStreamController.broadcast();
      appRepository = MockAppRepository();
      embarkAuthenticationRepository = MockEmbarkAuthenticationRepository();

      getIt.registerSingleton(connectivityStream.stream);
      getIt.registerFactory(
        () => PermissionsCubit(
          appRepository,
          AuthManagerTestHelper.loggedInManager(
            inState: const EmbarkAuthenticationState.permissions(),
            authenticationRepository: embarkAuthenticationRepository,
          ),
        ),
      );
    });

    tearDown(() async {
      await connectivityStream.close();
      await getIt.reset();
    });

    testWidgets('it acknowledges permission when button pressed',
        (widgetTester) async {
      await widgetTester.load(widget: const PermissionsPage());

      await widgetTester.tap(permissionsPagePageObject.continueButton);
      await widgetTester.pump();

      verify(appRepository.setAcceptedPermissions(true)).called(1);
      verify(embarkAuthenticationRepository.reevaluateAuthenticationStatus())
          .called(1);
    });

    testGoldens('renders correctly', (tester) async {
      final builder = DeviceBuilder()
        ..addScenario(name: 'initial', widget: const PermissionsPage());

      await tester.pumpDeviceBuilder(builder, wrapper: goldenWrapper);
      await screenMatchesGolden(tester, 'permissions_page');
    });

    testGoldens('renders correctly for accessibility', (tester) async {
      await tester
          .pumpWidgetBuilder(PermissionsView(l10n: AppLocalizationsEn()));

      await multiScreenGolden(
        tester,
        'permissions_page_accessibility',
        devices: [
          Device.iphone11.copyWith(textScale: 3.0),
        ],
      );
    });
  });
}
