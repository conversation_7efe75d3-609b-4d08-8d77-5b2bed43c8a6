import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/models/models.dart';
import 'package:embark/self_report/views/responses/_self_report_transportation_response.dart';
import 'package:embark/starter.dart';
import 'package:embark/transportation_change_request/views/views.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:widgets/widgets.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('SelfReportTransportationResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late MockTransportationRepository mockTransportationRepository;
    late Transportation testTransportation;
    late Transportation testPrimaryTransportation;
    late List<TransportationChange> testTransportationChanges;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    setUp(() async {
      testTransportation = Transportation(
        id: 123,
        comment: 'A car',
        type: 'Personal Vehicle',
        typeId: 1,
        isOwner: false,
        isPrimary: false,
      );
      testPrimaryTransportation = Transportation(
        id: 223,
        comment: 'My ride',
        type: 'Primary Personal Vehicle',
        typeId: 2,
        isOwner: false,
        isPrimary: true,
      );

      testTransportationChanges = [
        TransportationChange(
          DataOperationType.updated,
          data: testTransportation,
        ),
        TransportationChange(
          DataOperationType.updated,
          data: testTransportation,
        ),
      ];

      mockSelfReportCubit = MockSelfReportCubit();
      mockTransportationRepository = MockTransportationRepository();
      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<TransportationRepository>(
        mockTransportationRepository,
      );

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(
      () async {
        await getIt.reset();
        await iCSController.close();
      },
    );

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: BlocProvider<SelfReportCubit>(
          create: (_) => mockSelfReportCubit,
          child: const SelfReportTransportationResponse(),
        ),
      );
      await tester.pumpAndSettle();
    }

    testWidgets('renders TransportationSummary', (tester) async {
      await loadWidget(tester);

      expect(
        find.byType(TransportationSummary),
        findsOneWidget,
      );
    });

    testWidgets('renders TransportationSummary with correct data for 2 items',
        (tester) async {
      when(mockTransportationRepository.fetchTransporation()).thenAnswer(
        (_) => Future(
          () => testTransportationChanges,
        ),
      );

      await loadWidget(tester);

      expect(
        find.byType(ListTile),
        findsNWidgets(2),
      );
    });

    testWidgets(
        'renders TransportationSummary with items that are primary and alternate',
        (tester) async {
      when(mockTransportationRepository.fetchTransporation()).thenAnswer(
        (_) => Future(
          () => [
            TransportationChange(
              DataOperationType.updated,
              data: testTransportation,
            ),
            TransportationChange(
              DataOperationType.updated,
              data: testPrimaryTransportation,
            ),
          ],
        ),
      );

      await loadWidget(tester);

      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      expect(find.byIcon(Icons.delete), findsOneWidget);
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);
      await tester.tap(find.text('Cancel'));
      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });

    testWidgets('delete with no other changes works', (tester) async {
      when(mockTransportationRepository.fetchTransporation()).thenAnswer(
        (_) => Future(
          () => testTransportationChanges,
        ),
      );

      await loadWidget(tester);
      await tester
          .tap(find.byKey(const Key('BiFormListViewItemDeleteButton0')));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Yes'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Continue'));
      await tester.pumpAndSettle();

      verify(mockSelfReportCubit.saveTransportation(any)).called(1);
    });
  });
}
