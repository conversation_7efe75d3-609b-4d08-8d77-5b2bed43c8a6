import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/personal_contact_change_request/views/views.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/models/models.dart';
import 'package:embark/self_report/views/responses/_self_report_personal_contact_response.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';
import 'package:widgets/widgets.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('SelfReportPersonalContactResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late MockPersonalContactsRepository mockPersonalContactsRepository;
    late PersonalContact testPersonalContact;
    late List<PersonalContactChange> testPersonalContactChanges;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    setUp(() async {
      testPersonalContact = PersonalContact(
        id: 123,
        firstName: 'John',
        lastName: 'Doe',
        isActive: true,
        relationshipId: 1,
      );

      testPersonalContactChanges = [
        PersonalContactChange(
          DataOperationType.updated,
          data: testPersonalContact,
        ),
        PersonalContactChange(
          DataOperationType.updated,
          data: testPersonalContact,
        ),
      ];

      mockSelfReportCubit = MockSelfReportCubit();
      mockPersonalContactsRepository = MockPersonalContactsRepository();
      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<PersonalContactsRepository>(
        mockPersonalContactsRepository,
      );
      getIt.registerSingleton<UrlLauncherPlatform>(
        MockUrlLauncherPlatform(),
      );

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(() async {
      await getIt.reset();
      await iCSController.close();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.mdm,
          child: BlocProvider<SelfReportCubit>(
            create: (_) => mockSelfReportCubit,
            child: const SelfReportPersonalContactResponse(),
          ),
        ),
      );
      await tester.pumpAndSettle();
    }

    testWidgets('renders PersonalContactSummary', (tester) async {
      await loadWidget(tester);

      expect(
        find.byType(PersonalContactSummary),
        findsOneWidget,
      );
    });

    testWidgets('renders PersonalContactSummary with correct data for 2 items',
        (tester) async {
      when(mockPersonalContactsRepository.fetchPersonalContacts()).thenAnswer(
        (_) async => Future(
          () => testPersonalContactChanges,
        ),
      );

      await loadWidget(tester);

      expect(
        find.byType(ListTile),
        findsNWidgets(2),
      );
    });

    testWidgets('renders PersonalContactSummary with items that can be deleted',
        (tester) async {
      when(mockPersonalContactsRepository.fetchPersonalContacts()).thenAnswer(
        (_) async => Future(
          () => testPersonalContactChanges,
        ),
      );

      await loadWidget(tester);

      expect(find.byIcon(Icons.delete), findsNWidgets(2));
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);
      await tester.tap(find.text('Cancel'));
      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });

    testWidgets('delete with no other changes works', (tester) async {
      when(mockPersonalContactsRepository.fetchPersonalContacts()).thenAnswer(
        (_) async => Future(
          () => testPersonalContactChanges,
        ),
      );
      when(mockPersonalContactsRepository.saveLocally(any, any)).thenAnswer(
        (_) async => Future(
          () => null,
        ),
      );

      await loadWidget(tester);

      await tester
          .tap(find.byKey(const Key('BiFormListViewItemDeleteButton0')));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Yes'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Continue'));
      await tester.pumpAndSettle();

      verify(mockSelfReportCubit.savePersonalContacts(any)).called(1);
    });
  });
}
