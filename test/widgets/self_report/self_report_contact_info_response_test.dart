import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/contact_info_change_request/cubit/_contact_info_change_request_cubit.dart';
import 'package:embark/contact_info_change_request/cubit/_contact_info_change_request_state.dart';
import 'package:embark/contact_info_change_request/views/_contact_info_change_request_form.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/views/responses/_self_report_contact_info_response.dart';
import 'package:embark/starter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('SelfReportContactInfoResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late MockContactInfoChangeRequestCubit mockContactInfoChangeRequestCubit;

    final rules = ContactInfoRules(
      // populate the rules with typical values
      countryCodes: [IdWithName(id: 1, name: 'United States')],
      emailMaxLength: 50,
      emailPattern: '',
      phoneMaxLength: 10,
      phonePattern: '',
      primaryPhones: [
        IdWithName(id: 0, name: 'None'),
        IdWithName(id: 1, name: 'Home'),
        IdWithName(id: 2, name: 'Work'),
        IdWithName(id: 3, name: 'Mobile'),
      ],
      requiredFields: [],
    );

    late BiStreamController<ContactInfoChangeRequestState>
        contactInfoChangeCubitStream;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    setUp(() async {
      mockSelfReportCubit = MockSelfReportCubit();
      mockContactInfoChangeRequestCubit = MockContactInfoChangeRequestCubit();
      contactInfoChangeCubitStream = BiStreamController.broadcast();

      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<ContactInfoChangeRequestCubit>(
        mockContactInfoChangeRequestCubit,
      );

      getIt.registerSingleton<UrlLauncherPlatform>(
        MockUrlLauncherPlatform(),
      );

      var currentState = ContactInfoChangeRequestState.initial();

      when(mockContactInfoChangeRequestCubit.stream)
          .thenAnswer((_) => contactInfoChangeCubitStream.stream);

      when(mockContactInfoChangeRequestCubit.emit(any)).thenAnswer(
        (realInvocation) {
          currentState = realInvocation.positionalArguments.first
              as ContactInfoChangeRequestState;
          contactInfoChangeCubitStream.add(currentState);
        },
      );

      when(mockContactInfoChangeRequestCubit.state)
          .thenAnswer((realInvocation) => currentState);

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(() async {
      await getIt.reset();
      await contactInfoChangeCubitStream.close();
      await iCSController.close();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: BlocProvider<SelfReportCubit>(
          create: (_) => mockSelfReportCubit,
          child: const SelfReportContactInfoResponse(),
        ),
      );

      mockContactInfoChangeRequestCubit
          .emit(ContactInfoChangeRequestState.loaded(rules, null));

      await tester.pump();
    }

    Future<void> tapButton(
      WidgetTester tester, {
      required Finder finder,
    }) async {
      // grab the last scrollable widget so that we know we are scolling the most
      // inner view (form fields): https://stackoverflow.com/a/73881623
      final scrollable = find.byType(Scrollable).first;

      final buttonFinder = find.descendant(
        of: find.byType(ListView),
        matching: finder,
      );
      await tester.scrollUntilVisible(
        buttonFinder,
        30,
        scrollable: scrollable,
      );
      await tester.pump();
      await tester.tap(buttonFinder);

      // run tap animation
      await tester.pump();
    }

    Future<void> tapContinue(WidgetTester tester) async => tapButton(
          tester,
          finder: find.byType(BiPositiveButton),
        );

    Future<void> tapCancel(WidgetTester tester) async => tapButton(
          tester,
          finder: find.byType(BiNegativeButton),
        );

    testWidgets('renders correct form', (tester) async {
      await loadWidget(tester);

      expect(
        find.descendant(
          of: find.byType(SelfReportContactInfoResponse),
          matching: find.byType(ContactInfoChangeRequestForm),
        ),
        findsOneWidget,
      );
    });

    testWidgets('finds all widgets', (tester) async {
      await loadWidget(
        tester,
      );

      // get list of all text control widgets
      final allTextControls = find.byType(BiTextField);
      expect(allTextControls, findsNWidgets(4));

      // get the dropdown widget
      final dropdownControl = find.byType(BiDropdownMenuFormField<IdWithName>);
      await tester.pump();

      expect(dropdownControl, findsOneWidget);

      // find the list of items in the dropdown control
      final dropdownControlList = find.byType(BiDropdownMenu<IdWithName>);

      // find the text of the items in  the dropdown list
      expect(
        find.descendant(
          of: dropdownControlList,
          matching: find.text('Primary Phone Number'),
          skipOffstage: false,
        ),
        findsOneWidget,
      );

      final entries = tester
          .widget<BiDropdownMenu<IdWithName>>(dropdownControlList)
          .entries
          .map((e) => e.label)
          .toList();

      expect(
        listEquals(
          entries,
          [
            'Home',
            'Work',
            'Mobile',
          ],
        ),
        isTrue,
      );

      // get list of all text control label text values
      final allTextControlText = tester
          .widgetList<BiTextField>(allTextControls)
          .map((w) => w.controlLabelText)
          .toList();

      // compare the profile labels to the expected English values
      expect(
        listEquals(
          allTextControlText,
          [
            'Mobile Number',
            'Home Number',
            'Work Number',
            'Email',
          ],
        ),
        isTrue,
      );
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);

      await tapCancel(tester);

      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });

    testWidgets('continues form correctly when no inputs changed',
        (tester) async {
      await loadWidget(tester);

      await tapContinue(tester);

      verify(
        mockSelfReportCubit.saveContactInfo(
          ContactInfo.empty().copyWith(email1: null),
        ),
      ).called(1);
    });

    testWidgets('continues form correctly when inputs changed', (tester) async {
      await loadWidget(tester);

      const inputTextMobile = '1234567890';

      await tester.enterText(find.byType(TextFormField).first, inputTextMobile);

      // let the text input settle and the keyboard dismiss
      await tester.pump();

      await tapContinue(tester);

      final expectedForm = ContactInfo.empty().copyWith(
        // the value we expected to change
        mobilePhone1: inputTextMobile,
        mobilePhone1Code: 1,
      );

      verify(mockSelfReportCubit.saveContactInfo(expectedForm)).called(1);
    });
  });
}
