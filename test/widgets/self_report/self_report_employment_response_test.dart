import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/employment_change_request/views/views.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/self_report/models/models.dart';
import 'package:embark/self_report/views/responses/responses.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:widgets/widgets.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('SelfReportEmploymentResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late MockEmploymentRepository mockEmploymentRepository;

    late Employment testEmployment;
    late Employment testNotEditableEmployment;
    late List<EmploymentChange> testEmploymentChanges;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    setUp(() async {
      testEmployment = Employment(
        id: 123,
        name: 'Company A',
      );

      testNotEditableEmployment = Employment(
        id: 308,
        name: 'Company X',
        address: EmploymentAddress(
          country: 'USA',
        ),
      );

      testEmploymentChanges = [
        EmploymentChange(
          DataOperationType.updated,
          data: testEmployment,
        ),
        EmploymentChange(
          DataOperationType.updated,
          data: testEmployment,
        ),
      ];

      mockSelfReportCubit = MockSelfReportCubit();
      mockEmploymentRepository = MockEmploymentRepository();
      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<EmploymentRepository>(
        mockEmploymentRepository,
      );

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(() async {
      await getIt.reset();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.mdm,
          child: BlocProvider<SelfReportCubit>(
            create: (_) => mockSelfReportCubit,
            child: const SelfReportEmploymentResponse(),
          ),
        ),
      );
      await tester.pumpAndSettle();
      await iCSController.close();
    }

    testWidgets('renders EmploymentSummary', (tester) async {
      await loadWidget(tester);

      expect(
        find.byType(EmploymentSummary),
        findsOneWidget,
      );
    });

    testWidgets('renders EmploymentSummary with correct data for 2 items',
        (tester) async {
      when(mockEmploymentRepository.fetchEmployment()).thenAnswer(
        (_) => Future(
          () => testEmploymentChanges,
        ),
      );

      await loadWidget(tester);

      expect(
        find.byType(ListTile),
        findsNWidgets(2),
      );
    });

    testWidgets('renders EmploymentSummary with item which cannot be edited',
        (tester) async {
      when(mockEmploymentRepository.fetchEmployment()).thenAnswer(
        (_) => Future(
          () => [
            EmploymentChange(
              DataOperationType.updated,
              data: testNotEditableEmployment,
            ),
          ],
        ),
      );

      await loadWidget(tester);

      expect(find.byType(BIListViewTile), findsOneWidget);
      expect(find.byType(BIListViewTrailingIconButton), findsNothing);
    });

    testWidgets('renders EmploymentSummary with item which can be edited',
        (tester) async {
      when(mockEmploymentRepository.fetchEmployment()).thenAnswer(
        (_) => Future(
          () => [
            EmploymentChange(
              DataOperationType.updated,
              data: testEmployment,
            ),
          ],
        ),
      );

      await loadWidget(tester);

      expect(find.byType(BIListViewTile), findsOneWidget);
      expect(find.byType(BIListViewTrailingIconButton), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);
      await tester.tap(find.text('Cancel'));
      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });
  });
}
