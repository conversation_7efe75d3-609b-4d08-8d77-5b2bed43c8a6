import 'dart:async';

import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/src/models/address_api/_address_response.dart';
import 'package:bi_flutter_smlk_api/src/models/biometrics_api/post_self_report_answer_request_with_photo.dart';
import 'package:bi_flutter_smlk_api/src/models/biometrics_api/self_report_question.dart';
import 'package:bi_flutter_smlk_api/src/models/community_provider_api/_community_referral.dart';
import 'package:bi_flutter_smlk_api/src/models/contact_info_api/_contact_info.dart';
import 'package:bi_flutter_smlk_api/src/models/transportation_api/_transportation.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/authentication/auth_steps/showing_dashboard_step.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/dashboard/cubit/cubit.dart';
import 'package:embark/dashboard/dashboard.dart';
import 'package:embark/open_text_change_request/models/_open_text_request.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_state.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/self_report/cubit/state_machine/_self_report_state_machine.dart';
import 'package:embark/self_report/cubit/state_machine/_self_report_state_machine_state.dart';
import 'package:embark/self_report/models/models.dart';
import 'package:embark/self_report/self_report.dart';
import 'package:embark/self_report/views/_self_report_answer_segmented_button.dart';
import 'package:embark/self_report/views/_self_report_view.dart';
import 'package:embark/services/services.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:widgets/widgets.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bi_permission_request_repository.dart';
import '../../unit_test_helpers/stubs/auth_manager.dart';

void main() {
  group('verify state change', () {
    late _FakeSelfReportCubit cubit;
    late NavigatorState navigator;
    late MockDashboardCubit dashboardCubit;
    late MockPushNotificationListenerCubit pushNotificationListenerCubit;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    Future<void> navigateToSelfReportViewWithInitialRoute(
      WidgetTester tester,
    ) =>
        tester.loadRouterTest((context) {
          navigator = Navigator.of(context);
          navigator.push(
            MaterialPageRoute<void>(
              builder: (context) => const Placeholder(),
            ),
          );
          navigator.push(
            MaterialPageRoute<void>(
              builder: (context) => BlocProvider<SelfReportCubit>.value(
                value: cubit,
                child: const SelfReportView(),
              ),
            ),
          );
        });

    setUp(() {
      cubit = _FakeSelfReportCubit(SelfReportState.initial());
      dashboardCubit = MockDashboardCubit();
      pushNotificationListenerCubit = MockPushNotificationListenerCubit();
      getIt.registerSingleton<DashboardCubit>(dashboardCubit);
      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton(AuthHandler());
      getIt.registerSingleton(
        AuthManagerTestHelper.loggedInManager(
          authenticationRepository: MockEmbarkAuthenticationRepository(),
        ),
      );
      getIt.registerSingleton<PushNotificationListenerCubit>(
        pushNotificationListenerCubit,
      );
      getIt.registerSingleton<VideoConferenceRepository>(
        MockVideoConferenceRepository(),
      );
      getIt.registerSingleton(ShowingDashboardStep());

      when(dashboardCubit.state).thenReturn(const DashboardState());

      when(pushNotificationListenerCubit.startingDashBoardTabId).thenReturn(-1);
      when(pushNotificationListenerCubit.state).thenReturn(
        PushNotificationListenerState.initial(false),
      );
    });

    tearDown(() {
      iCSController.close();
      getIt.reset();
      dashboardCubit.close();
      pushNotificationListenerCubit.close();
    });

    testWidgets('self report view', (tester) async {
      cubit.emit(cubit.state.copyWith(status: SelfReportStatus.completed));
      await navigateToSelfReportViewWithInitialRoute(tester);
      await tester.pump();
      expect(find.byType(BiEmbarkStatusView), findsOneWidget);
      navigator.pop();
      cubit.emit(
        cubit.state.copyWith(status: SelfReportStatus.completedTimerFinished),
      );
      await tester.pumpAndSettle();
      expect(find.byType(TextButton), findsOneWidget);
    });

    testWidgets('navigates back with popPage if canPop is true',
        (WidgetTester tester) async {
      cubit.emit(cubit.state.copyWith(status: SelfReportStatus.completed));
      await navigateToSelfReportViewWithInitialRoute(tester);
      await tester.pump();
      expect(find.byType(BiEmbarkStatusView), findsOneWidget);
      expect(find.byType(SelfReportView), findsOneWidget);

      cubit.emit(
        cubit.state.copyWith(status: SelfReportStatus.completedTimerFinished),
      );
      await tester.pumpAndSettle();

      expect(find.byType(SelfReportView), findsNothing);
      expect(find.byType(Placeholder), findsOneWidget);
    });

    testWidgets('enters dashboard if canPop is false', (tester) async {
      await tester.runAsync(() async {
        final clientSettingsRepository = MockClientSettingsRepository();
        final clientSettingsValue = ValueNotifier(null);
        when(clientSettingsRepository.clientSettingsListener)
            .thenReturn(clientSettingsValue);

        getIt.registerSingleton<BiPermissionRequestRepository>(
          FakeBiPermissionRequestRepository(
            bluetoothApi: MockBiBluetoothApi(),
          ),
        );
        getIt.registerSingleton<ClientSettingsRepository>(
          clientSettingsRepository,
        );
        getIt.registerSingleton<EmbarkPermissionPreferences>(
          MockEmbarkPermissionPreferences(),
        );

        await tester.pumpWidget(
          MultiBlocProvider(
            providers: [
              BlocProvider<SelfReportCubit>.value(value: cubit),
              BlocProvider<PushNotificationListenerCubit>.value(
                value: pushNotificationListenerCubit,
              ),
              BlocProvider<SmartBandSettingsCubit>(
                create: (_) => MockSmartBandSettingsCubit(),
              ),
            ],
            child: const MaterialApp(
              localizationsDelegates: [
                ...AppLocalizations.localizationsDelegates,
                ...BiWidgetLocalizations.localizationsDelegates,
              ],
              supportedLocales: [
                ...AppLocalizations.supportedLocales,
                ...BiWidgetLocalizations.supportedLocales,
              ],
              home: SelfReportView(),
            ),
          ),
        );

        cubit.emit(
          cubit.state.copyWith(status: SelfReportStatus.completedTimerFinished),
        );
        await tester.pumpAndSettle();
        expect(find.byType(DashboardPage), findsOneWidget);
        verify(dashboardCubit.init()).called(1);
      });
    });

    testWidgets('displays the title of the loaded content when state is loaded',
        (WidgetTester tester) async {
      cubit.emit(
        cubit.state.copyWith(
          status: SelfReportStatus.loaded,
          stateMachine: SelfReportStateMachine(
            [
              SelfReportStateMachineState.initial(
                const SelfReportQuestion(
                  contextId: 0,
                  text: 'Loaded Content Title',
                ),
                false,
              ),
            ],
          ),
        ),
      );

      await navigateToSelfReportViewWithInitialRoute(tester);
      await tester.pumpAndSettle();

      expect(find.text('Loaded Content Title'), findsOneWidget);
    });
  });
}

class _FakeSelfReportCubit extends Cubit<SelfReportState>
    implements SelfReportCubit {
  _FakeSelfReportCubit(super.initialState);

  @override
  void checkInSuccess(PostSelfReportAnswerRequestWithPhoto result) {}

  @override
  void editQuestion(SelfReportQuestion question) {}

  @override
  Stream<String?> get fatalErrorMessageStream => throw UnimplementedError();

  @override
  Future<void> fetchSelfReportQuestions() {
    throw UnimplementedError();
  }

  @override
  Future<void> init({
    required AppLocalizations l10n,
    bool loadQuestions = true,
  }) {
    throw UnimplementedError();
  }

  @override
  void onCancel() {}

  @override
  void onContinue() {}

  @override
  void reset() {}

  @override
  Future<void> saveAddress(
    AddressResponse? address, {
    bool submitChangesLive = false,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<void> saveCommunityReferral(
    List<CommunityReferralChange> changes, {
    bool submitChangesLive = false,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<void> saveContactInfo(
    ContactInfo? contactInfo, {
    bool submitChangesLive = false,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<void> saveEmployment(
    List<EmploymentChange> changes, {
    bool submitChangesLive = false,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<void> saveOpenText(OpenTextRequest? openText, Answer? answer) {
    throw UnimplementedError();
  }

  @override
  Future<void> savePersonalContacts(
    List<PersonalContactChange> changes, {
    bool submitChangesLive = false,
  }) {
    throw UnimplementedError();
  }

  @override
  Future<void> saveTransportation(
    List<TransportationChange> changes, {
    bool submitChangesLive = false,
  }) {
    throw UnimplementedError();
  }

  @override
  void setAnswer(Answer answer) {}

  @override
  void setFatalError(Object error, [StackTrace? stack]) {}

  @override
  List<StreamController<dynamic>> get streamControllers =>
      throw UnimplementedError();

  @override
  Future<void> submitSelfReport() {
    throw UnimplementedError();
  }

  @override
  List<StreamSubscription<dynamic>> get subscriptions =>
      throw UnimplementedError();

  @override
  void processCommunityReferralChanges(
    List<CommunityReferralChange> fields,
    List<CommunityReferral> initialCommunityReferrals,
  ) {
    throw UnimplementedError();
  }

  @override
  Transportation updateTransportation(
    Transportation transportation,
    String type,
    int typeId,
    String? state,
  ) {
    return transportation.copyWith(
      type: type,
      typeId: typeId,
      state: state ?? transportation.state,
    );
  }
}
