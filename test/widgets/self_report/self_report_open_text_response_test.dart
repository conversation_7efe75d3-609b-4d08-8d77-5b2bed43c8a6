import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/open_text_change_request/cubit/_open_text_change_request_cubit.dart';
import 'package:embark/open_text_change_request/cubit/_open_text_change_request_state.dart';
import 'package:embark/open_text_change_request/models/_open_text_request.dart';
import 'package:embark/open_text_change_request/views/_open_text_change_request_form.dart';
import 'package:embark/self_report/cubit/_self_report_cubit.dart';
import 'package:embark/self_report/cubit/_self_report_state.dart';
import 'package:embark/self_report/views/responses/_self_report_open_text_response.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('SelfReportOpenTextResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late BiStreamController<SelfReportState> selfReportCubitStream;
    late MockOpenTextChangeRequestCubit mockOpenTextChangeRequestCubit;
    late BiStreamController<OpenTextChangeRequestState>
        openTextChangeCubitStream;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    final openTextRequestForTest = OpenTextRequest(
      questionId: 1000,
      questionText: 'Test Question to Display',
      additionalComments: 'Additonal comments for test',
    );

    setUp(() async {
      mockSelfReportCubit = MockSelfReportCubit();
      selfReportCubitStream = BiStreamController.broadcast();

      mockOpenTextChangeRequestCubit = MockOpenTextChangeRequestCubit();
      openTextChangeCubitStream = BiStreamController.broadcast();

      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<OpenTextChangeRequestCubit>(
        mockOpenTextChangeRequestCubit,
      );

      var selfReportState = SelfReportState.initial();
      when(mockSelfReportCubit.stream)
          .thenAnswer((_) => selfReportCubitStream.stream);
      when(mockSelfReportCubit.emit(any)).thenAnswer((realInvocation) {
        selfReportState =
            realInvocation.positionalArguments.first as SelfReportState;
        selfReportCubitStream.add(selfReportState);
      });
      when(mockSelfReportCubit.state)
          .thenAnswer((realInvocation) => selfReportState);

      var currentState = OpenTextChangeRequestState.initial();

      when(mockOpenTextChangeRequestCubit.stream)
          .thenAnswer((_) => openTextChangeCubitStream.stream);
      when(mockOpenTextChangeRequestCubit.emit(any))
          .thenAnswer((realInvocation) {
        currentState = realInvocation.positionalArguments.first
            as OpenTextChangeRequestState;
        openTextChangeCubitStream.add(currentState);
      });
      when(mockOpenTextChangeRequestCubit.state)
          .thenAnswer((realInvocation) => currentState);

      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(() async {
      await getIt.reset();
      await openTextChangeCubitStream.close();
      await selfReportCubitStream.close();
      await iCSController.close();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: BlocProvider<SelfReportCubit>(
          create: (_) => mockSelfReportCubit,
          child: const SelfReportOpenTextResponse(),
        ),
      );

      mockOpenTextChangeRequestCubit
          .emit(OpenTextChangeRequestState.loaded(openTextRequestForTest));

      await tester.pump();
    }

    Future<void> tapButton(
      WidgetTester tester, {
      required Finder finder,
    }) async {
      // grab the last scrollable widget so that we know we are scolling the most
      // inner view (form fields): https://stackoverflow.com/a/73881623
      final scrollable = find.byType(Scrollable).first;

      final buttonFinder = find.descendant(
        of: find.byType(ListView),
        matching: finder,
      );
      await tester.scrollUntilVisible(
        buttonFinder,
        30,
        scrollable: scrollable,
      );
      await tester.pump();
      await tester.tap(buttonFinder);

      // run tap animation
      await tester.pump();
    }

    Future<void> tapContinue(WidgetTester tester) async => tapButton(
          tester,
          finder: find.byType(BiPositiveButton),
        );

    Future<void> tapCancel(WidgetTester tester) async => tapButton(
          tester,
          finder: find.byType(BiNegativeButton),
        );

    testGoldens('renders correct form', (tester) async {
      await tester.setupScreenSize(const Size(300, 600));

      await loadWidget(tester);

      expect(
        find.descendant(
          of: find.byType(SelfReportOpenTextResponse),
          matching: find.byType(OpenTextChangeRequestForm),
        ),
        findsOneWidget,
      );
      await screenMatchesGolden(tester, 'self_report');
    });

    testWidgets('finds expected widgets', (tester) async {
      await loadWidget(
        tester,
      );

      // get the question text widget
      final allTextWidgets = find.byType(Text);

      // get the text list for text widgets
      final allText =
          tester.widgetList<Text>(allTextWidgets).map((l) => l.data).toList();

      // Expect question text
      expect(allText[0], openTextRequestForTest.questionText);

      // Control label test
      final openText = find.byType(BiTextField);

      const expectedControlLabelText = 'Response';

      final controlText = tester
          .widgetList<BiTextField>(openText)
          .map((w) => w.controlLabelText)
          .toList();
      expect(controlText.first, expectedControlLabelText);

      // Open text test
      final openTextInitial = tester
          .widgetList<BiTextField>(openText)
          .map((w) => w.initialValue)
          .toList();

      expect(openTextInitial.first, openTextRequestForTest.additionalComments);

      final formField =
          find.byType(TextField).evaluate().first.widget as TextField;
      expect(formField.maxLength, 490);
    });

    testWidgets('continues form correctly when no inputs changed',
        (tester) async {
      await loadWidget(tester);

      await tapContinue(tester);

      verify(
        mockSelfReportCubit.saveOpenText(
          openTextRequestForTest,
          null,
        ),
      ).called(1);
    });

    testWidgets('continues form correctly when inputs changed', (tester) async {
      await loadWidget(tester);

      final inputOpenTextChanged =
          '${openTextRequestForTest.additionalComments} changed';

      final openText = find.byType(BiTextField);

      await tester.enterText(openText, inputOpenTextChanged);

      // let the text input settle and the keyboard dismiss
      await tester.pump();

      await tapContinue(tester);

      verify(
        mockSelfReportCubit.saveOpenText(
          openTextRequestForTest.copyWith(
            additionalComments: inputOpenTextChanged,
          ),
          null,
        ),
      ).called(1);
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);

      await tapCancel(tester);

      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });
  });
}
