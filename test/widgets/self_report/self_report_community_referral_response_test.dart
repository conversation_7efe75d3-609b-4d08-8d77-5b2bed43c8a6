import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/community_refferal/views/views.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/self_report/cubit/cubit.dart';
import 'package:embark/self_report/models/models.dart';
import 'package:embark/self_report/views/responses/responses.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:widgets/widgets.dart';

import '../../unit_test.mocks.dart';
import '../../tester_extensions.dart';

void main() {
  group('SelfReportCommunityReferralResponse', () {
    late MockSelfReportCubit mockSelfReportCubit;
    late MockCommunityReferralRepository mockCommunityReferralRepository;
    late CommunityReferral testCommunityReferral;
    late List<CommunityReferralChange> testCommunityReferralChanges;
    late BiStreamController<List<ConnectivityResult>> iCSController;

    setUp(() async {
      mockSelfReportCubit = MockSelfReportCubit();
      mockCommunityReferralRepository = MockCommunityReferralRepository();
      getIt.registerSingleton<SelfReportCubit>(mockSelfReportCubit);
      getIt.registerSingleton<CommunityReferralRepository>(
        mockCommunityReferralRepository,
      );

      testCommunityReferral = CommunityReferral(
        id: 123,
        name: 'Test referral 1',
        referralStatus: '1',
      );

      testCommunityReferralChanges = [
        CommunityReferralChange(
          DataOperationType.updated,
          data: testCommunityReferral,
        ),
        CommunityReferralChange(
          DataOperationType.updated,
          data: testCommunityReferral.copyWith(
            referralStatus: '3',
            name: 'Test referral 2',
          ),
        ),
      ];
      iCSController = BiStreamController.broadcast(sync: true);
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
    });

    tearDown(() async {
      await getIt.reset();
      await iCSController.close();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: BlocProvider<SelfReportCubit>(
          create: (_) => mockSelfReportCubit,
          child: const SelfReportCommunityReferralResponse(),
        ),
      );
      await tester.pumpAndSettle();
    }

    testWidgets('renders CommunityReferralSummary', (tester) async {
      await loadWidget(tester);

      expect(
        find.byType(CommunityReferralSummary),
        findsOneWidget,
      );
    });

    testWidgets('cancels form correctly', (tester) async {
      await loadWidget(tester);
      await tester.tap(find.text('Cancel'));
      verifyNever(mockSelfReportCubit.onContinue());
      verify(mockSelfReportCubit.onCancel()).called(1);
    });
    testWidgets('Continue form correctly', (tester) async {
      when(
        mockSelfReportCubit.processCommunityReferralChanges(
          any,
          any,
        ),
      ).thenAnswer((_) {});
      await loadWidget(tester);
      await tester.tap(find.text('Continue'));
      verify(
        mockSelfReportCubit.processCommunityReferralChanges(
          any,
          any,
        ),
      ).called(1);
    });
    testWidgets(
      'renders CommunityReferralSummary with correct data for 2 items',
      (tester) async {
        when(mockCommunityReferralRepository.fetchCommunityReferrals())
            .thenAnswer(
          (_) => Future(
            () => testCommunityReferralChanges,
          ),
        );

        await loadWidget(tester);

        expect(
          find.byType(ListTile),
          findsNWidgets(2),
        );
      },
    );
  });
}
