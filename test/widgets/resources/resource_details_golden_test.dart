import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:embark/repositories/_community_referral_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/resources/views/resources_details_page.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:provider/provider.dart';

import '../../unit_test.mocks.dart';

final testResourceItem = CommunityProvider(
  id: 1,
  name: '<PERSON>',
  address: '123 St',
  businessHours: '9-10',
  comment: 'DE14448: On Resource Page - View Website button is cutting off',
  email: 'dev.bi.com',
  fax: '+1 **********',
  phone2: '+1 **********',
  phone: '+1 **********',
  types: [IdWithName(id: 1, name: 'type')],
  website: 'bi.com',
);

void main() {
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late MockCommunityReferralRepository mockResouresRepo;
  late MockAppRepository appRepository;

  group('ResourcesDetailsPage', () {
    setUp(() {
      iCSController = BiStreamController.broadcast(sync: true);
      appRepository = MockAppRepository();
      mockResouresRepo = MockCommunityReferralRepository();
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<CommunityReferralRepository>(
        mockResouresRepo,
      );
      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );
    });

    tearDown(() async {
      await iCSController.close();
      await getIt.reset();
    });

    testGoldens('accessibility', (tester) async {
      await tester.pumpWidgetBuilder(
        Builder(
          builder: (_) => MaterialApp(
            localizationsDelegates: const [
              ...AppLocalizations.localizationsDelegates,
              ...BiWidgetLocalizations.localizationsDelegates,
            ],
            supportedLocales: const [
              ...AppLocalizations.supportedLocales,
              ...BiWidgetLocalizations.supportedLocales,
            ],
            home: Scaffold(
              body: Provider.value(
                value: ResolvedDeviceType.byod,
                child: ResourcesDetailsPage(
                  resource: testResourceItem,
                ),
              ),
            ),
          ),
        ),
      );
      await tester.pump();

      await multiScreenGolden(
        tester,
        'resources_details_page',
        devices: [Device.iphone11.copyWith(textScale: 3.0)],
      );
    });
  });
}
