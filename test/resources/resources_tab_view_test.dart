import 'dart:async';

import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/resources/views/resources_page.dart';
import 'package:embark/ui_helpers/ui_lookup_keys.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';

import '../tester_extensions.dart';
import '../unit_test.mocks.dart';
import '../widgets/widget_test_helpers/text_matcher.dart';
import 'resources_cubit_test.dart';

void main() {
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late MockCommunityReferralRepository mockResouresRepo;
  late MockAppRepository appRepository;
  late StreamController<Locale> localeStream;

  group('ResourcesTabView', () {
    setUp(() {
      iCSController = BiStreamController.broadcast(sync: true);
      mockResouresRepo = MockCommunityReferralRepository();
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<CommunityReferralRepository>(
        mockResouresRepo,
      );
      localeStream = StreamController.broadcast();
      appRepository = MockAppRepository();
      when(appRepository.localeStream).thenAnswer((_) => localeStream.stream);
      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );
      getIt.registerSingleton<UrlLauncherPlatform>(
        MockUrlLauncherPlatform(),
      );
    });

    tearDown(() async {
      await localeStream.close();
      await iCSController.close();
      await getIt.reset();
    });

    Future<void> loadWidget(WidgetTester tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.mdm,
          child: const RootRestorationScope(
            restorationId: 'resources',
            child: ResourcesPage(),
          ),
        ),
      );
    }

    testWidgets('shows loading state ', (tester) async {
      final completer = Completer<GetCommunityProvidersResponse>();

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => completer.future);
      await loadWidget(tester);
      await tester.pump();
      expect(find.byKey(UiLookupKeys.resourcesLoadingStateKey), findsOneWidget);
    });

    testWidgets('shows loaded state ', (tester) async {
      when(mockResouresRepo.fetchCommunityProviders()).thenAnswer(
        (realInvocation) => Future.value(
          GetCommunityProvidersResponse(
            communityProviders: [fakeProvider],
          ),
        ),
      );
      await loadWidget(tester);
      await tester.pump();
      expect(find.byKey(UiLookupKeys.resourcesLoadedStateKey), findsOneWidget);
      expect(find.byType(ListView), findsOneWidget);
    });

    testWidgets('shows empty state ', (tester) async {
      when(mockResouresRepo.fetchCommunityProviders()).thenAnswer(
        (realInvocation) => Future.value(
          const GetCommunityProvidersResponse(
            communityProviders: [],
          ),
        ),
      );
      await loadWidget(tester);
      await tester.pump();
      expect(find.byKey(UiLookupKeys.resourcesEmptyStateKey), findsOneWidget);
    });

    testWidgets('renders loading view when pulled to refresh ', (tester) async {
      final completer = Completer<GetCommunityProvidersResponse>();

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => completer.future);

      await loadWidget(tester);
      await tester.pump();
      await tester.fling(
        find.byType(ListView).first,
        const Offset(0, -500),
        10,
        warnIfMissed: false,
      );
      await tester.pump(const Duration(seconds: 1));

      expect(find.byKey(UiLookupKeys.resourcesLoadingStateKey), findsOneWidget);
    });

    testWidgets('finds resources dropdown when page has items', (tester) async {
      final completer =
          GetCommunityProvidersResponse(communityProviders: [fakeProvider]);

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      expect(find.byKey(UiLookupKeys.resourcesDropdownButton), findsOneWidget);
    });

    testWidgets('dropdown contains "all" item', (tester) async {
      final completer =
          GetCommunityProvidersResponse(communityProviders: [fakeProvider]);

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      final dropdown = find.byKey(UiLookupKeys.resourcesDropdownButton);
      await tester.tap(dropdown);
      await tester.pumpAndSettle();

      /// selected option is 'All' & 1 option of all
      expect(find.text('All'), findsNWidgets(2));
    });

    testWidgets('tap on dropdown all returns all results', (tester) async {
      final completer = GetCommunityProvidersResponse(
        communityProviders: [fakeProvider, fakeProvider2, fakeProvider3],
      );

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      final dropdown = find.byKey(UiLookupKeys.resourcesDropdownButton);
      await tester.tap(dropdown);
      await tester.pumpAndSettle();
      await tester.tap(find.text('All').last);
      await tester.pumpAndSettle();

      expect(find.byType(ListTile), findsNWidgets(3));
    });

    testWidgets('tap on dropdown all returns all results after filtering',
        (tester) async {
      final completer = GetCommunityProvidersResponse(
        communityProviders: [fakeProvider, fakeProvider2, fakeProvider3],
      );

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      final dropdown = find.byKey(UiLookupKeys.resourcesDropdownButton);

      /// pick a filter
      await tester.tap(dropdown);
      await tester.pumpAndSettle();
      final type = fakeProvider.types![0].name;
      await tester.tap(find.text(type).hitTestable());
      await tester.pump();
      await tester.pumpAndSettle();
      expect(find.byType(ListTile), findsNWidgets(1));

      /// pick 'all' filter
      await tester.tap(dropdown);
      await tester.pumpAndSettle();
      await tester.tap(find.text('All').hitTestable());
      await tester.pumpAndSettle();

      expect(find.byType(ListTile), findsNWidgets(3));
    });
    testWidgets(
        'changing locale will cause refresh of data (for localization purposes)',
        (tester) async {
      when(mockResouresRepo.fetchCommunityProviders()).thenAnswer(
        (realInvocation) async {
          return Future.value(
            GetCommunityProvidersResponse(
              communityProviders: [fakeProvider],
            ),
          );
        },
      );
      await loadWidget(tester);
      await tester.pump();
      verify(mockResouresRepo.fetchCommunityProviders()).called(1);
      localeStream.sink.add(const Locale('es'));
      await tester.pump();
      verify(mockResouresRepo.fetchCommunityProviders()).called(1);
    });
    testWidgets('filtered list retains state across loads ', (tester) async {
      final completer = GetCommunityProvidersResponse(
        communityProviders: [fakeProvider, fakeProvider2],
      );

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      /// tap dropdown button
      final dropdown = find.byKey(UiLookupKeys.resourcesDropdownButton);
      await tester.tap(dropdown);
      await tester.pumpAndSettle();

      /// tap a dropdown option
      await tester.tap(find.text('Fake2').first);
      await tester.pump();

      /// reload the page
      await loadWidget(tester);
      await tester.pumpAndSettle();

      /// One widget is the selected dropdown & the second is the matching list item
      expect(find.text('Fake2'), findsNWidgets(2));
    });

    testWidgets('resources dropdown items will be in alphbetical order',
        (tester) async {
      final completer = GetCommunityProvidersResponse(
        communityProviders: [
          fakeProvider.copyWith(
            id: 4,
            name: 'Charlie 1',
            types: [
              IdWithName(id: 4, name: 'Charlie 1'),
            ],
          ),
          fakeProvider.copyWith(
            id: 2,
            name: 'Alpha 1',
            types: [
              IdWithName(id: 2, name: 'Alpha 1'),
            ],
          ),
          fakeProvider.copyWith(
            id: 3,
            name: 'Beta 1',
            types: [
              IdWithName(id: 3, name: 'Beta 1'),
            ],
          ),
        ],
      );

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      final dropdown = find.byKey(UiLookupKeys.resourcesDropdownButton);
      await tester.tap(dropdown);
      await tester.pumpAndSettle();
      final textOfDropdown =
          find.descendant(of: dropdown, matching: find.byType(Text));

      /// last item in the list will be 'Charlie 1'
      expect(
        textOfDropdown.last,
        findsText('Charlie 1'),
      );
    });
    testWidgets("'all' filter is applied across reloads", (tester) async {
      final completer = GetCommunityProvidersResponse(
        communityProviders: [fakeProvider, fakeProvider2],
      );

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      /// tap dropdown button
      final dropdown = find.byKey(UiLookupKeys.resourcesDropdownButton);
      await tester.tap(dropdown);
      await tester.pumpAndSettle();

      /// tap a dropdown option
      await tester.tap(find.text(idWithName2.name).first);
      await tester.pumpAndSettle();

      /// re-open dropdown
      await tester.tap(dropdown);
      await tester.pumpAndSettle();

      /// tap 'all' option
      await tester.tap(find.text('All').hitTestable());
      await tester.pump();

      /// reload the page
      await tester.restartAndRestore();
      await tester.pumpAndSettle();

      /// All items are found
      expect(find.text('All'), findsNWidgets(1));

      /// name and type the same
      expect(find.text(idWithName.name), findsNWidgets(1));

      /// name and type the same
      expect(find.text(idWithName2.name), findsNWidgets(1));
    });

    testWidgets(
        'renders "all" when filtered, switch to "all", selection, then pulled to refresh ',
        (tester) async {
      final completer = GetCommunityProvidersResponse(
        communityProviders: [fakeProvider, fakeProvider2, fakeProvider3],
      );

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      await loadWidget(tester);
      await tester.pump();

      /// tap dropdown button
      final dropdown = find.byKey(UiLookupKeys.resourcesDropdownButton);
      await tester.tap(dropdown);
      await tester.pumpAndSettle();

      /// tap a dropdown option
      await tester.tap(find.text(idWithName2.name).first);
      await tester.pumpAndSettle();

      /// re-open dropdown
      await tester.tap(dropdown);
      await tester.pumpAndSettle();

      /// tap 'all' option
      await tester.tap(find.text('All').hitTestable());
      await tester.pump();

      await tester.load(widget: const ResourcesPage());
      await tester.pump();
      await tester.fling(
        find.byType(ListView).first,
        const Offset(0, -500),
        10,
        warnIfMissed: false,
      );
      await tester.pump(const Duration(seconds: 1));

      expect(find.byType(ListTile), findsNWidgets(3));
    });

    testWidgets('shows formatted phone number', (tester) async {
      final completer = GetCommunityProvidersResponse(
        communityProviders: [
          fakeProvider.copyWith(
            phone: '+1 **********',
          ),
        ],
      );

      when(mockResouresRepo.fetchCommunityProviders())
          .thenAnswer((realInvocation) => Future.value(completer));

      const formattedPhoneExpected = '************';

      await loadWidget(tester);
      await tester.pump();

      expect(find.text(formattedPhoneExpected), findsOneWidget);
    });
  });
}
