import 'dart:async';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/embark_scaffold/views/_embark_scaffold.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:embark/repositories/_community_referral_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/resources/views/resources_details_page.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../tester_extensions.dart';
import '../unit_test.mocks.dart';

final testResourceItem = CommunityProvider(
  id: 1,
  name: '<PERSON>',
  address: '123 St',
  businessHours: '',
  comment: '',
  email: '',
  fax: '*************',
  phone2: '+1 **********',
  phone: '+1 **********',
  types: [IdWithName(id: 1, name: 'type')],
  website: '',
);

void main() {
  late BiStreamController<List<ConnectivityResult>> iCSController;
  late MockCommunityReferralRepository mockResouresRepo;
  late MockAppRepository appRepository;

  group('ResourcesTabView', () {
    setUp(() {
      iCSController = BiStreamController.broadcast(sync: true);
      appRepository = MockAppRepository();
      mockResouresRepo = MockCommunityReferralRepository();
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        iCSController.stream,
      );
      getIt.registerSingleton<CommunityReferralRepository>(
        mockResouresRepo,
      );
      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );
    });

    tearDown(() async {
      await iCSController.close();
      await getIt.reset();
    });

    testWidgets('shows scaffold', (tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.byod,
          child: ResourcesDetailsPage(
            resource: testResourceItem,
          ),
        ),
      );
      await tester.pump();
      expect(find.byType(EmbarkScaffold), findsOneWidget);
    });
    testWidgets('shows map icon on byod', (tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.byod,
          child: ResourcesDetailsPage(
            resource: testResourceItem,
          ),
        ),
      );
      await tester.pump();
      expect(find.byIcon(Icons.pin_drop), findsOneWidget);
    });
    testWidgets('hides map icon on mdm', (tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.mdm,
          child: ResourcesDetailsPage(
            resource: testResourceItem,
          ),
        ),
      );
      await tester.pump();
      expect(find.byType(ResourceLineItem), findsWidgets);
      expect(find.byIcon(Icons.pin_drop), findsNothing);
    });

    testWidgets('shows formatted phone numbers', (tester) async {
      await tester.load(
        widget: Provider.value(
          value: ResolvedDeviceType.byod,
          child: ResourcesDetailsPage(
            resource: testResourceItem,
          ),
        ),
      );
      await tester.pump();

      const formattedPhoneExpected = '************';
      const formattedPhone2Expected = '************';
      const formattedFaxExpected = '************';

      expect(find.text(formattedPhoneExpected), findsOneWidget);
      expect(find.text(formattedPhone2Expected), findsOneWidget);
      expect(find.text(formattedFaxExpected), findsOneWidget);
    });
  });
}
