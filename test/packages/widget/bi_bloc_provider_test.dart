import 'dart:async';

import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart' as login_widgets;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:helpers/helpers.dart';
import 'package:widgets/widgets.dart';

import '../../unit_test.dart';
import '../../tester_extensions.dart';

enum _ErrorType {
  none,
  fatalError,
  throwError,
}

class _TestState extends Equatable {
  final int value;
  const _TestState(this.value);

  factory _TestState.zero() => const _TestState(0);
  factory _TestState.ten() => const _TestState(10);

  @override
  List<Object?> get props => [value];
}

// ignore: deprecated_member_use, deprecated_member_use_from_same_package
class _TestCubit extends BiEmbarkCubit<_TestState> {
  _TestCubit(super.initialState);
  FutureOr<void> initFuture = () {};
  int timesInitCalled = 0;
  Future<void> init({
    _ErrorType errorType = _ErrorType.none,
    Object? error,
  }) async {
    timesInitCalled++;
    await initFuture;
    if (error == null || errorType == _ErrorType.none) return;
    //ignore:missing_enum_constant_in_switch
    switch (errorType) {
      case _ErrorType.fatalError:
        setFatalError(error);
        break;
      case _ErrorType.throwError:
        throw error;
      case _ErrorType.none:
      // TODO: Handle this case.
    }
  }
}

void main() {
  late _TestCubit cubit;
  late AppLocalizations l10n;
  late Stream<List<ConnectivityResult>> connectionStatus;
  late BiStreamController<List<ConnectivityResult>> iCSStreamController;
  setUp(() async {
    cubit = _TestCubit(_TestState.zero());
    l10n = await loadEnglishTranslations();
    iCSStreamController =
        BiStreamController<List<ConnectivityResult>>.broadcast();
    connectionStatus = iCSStreamController.stream;
  });

  tearDown(() async {
    if (cubit.isClosed) return;
    await cubit.close();
    await iCSStreamController.close();
  });

  void validateErrorViewShown(WidgetTester tester, String errorText) {
    final viewFinder = find.byType(BiEmbarkNoItemsView);
    final backgroundFinder = find.byType(BiEmbarkBackground);
    final buttonAndHeaderFinder = find.descendant(
      of: backgroundFinder,
      matching: find.byType(login_widgets.BiEmptyListButton),
    );

    // ensure parent viewws are here.
    expect(viewFinder, findsOneWidget);
    expect(backgroundFinder, findsOneWidget);
    expect(buttonAndHeaderFinder, findsOneWidget);

    // grab the rendered widgets for assertions
    final background = tester.widget<BiEmbarkBackground>(backgroundFinder);
    final buttonAndHeader = tester.widget<login_widgets.BiEmptyListButton>(
      buttonAndHeaderFinder,
    );

    // ensure values are appropriately passed.
    expect(background.addChildBelowImage, isTrue);
    expect(buttonAndHeader.headerText, equals(errorText));
    expect(buttonAndHeader.text, equals(l10n.refresh));
  }

  group('BiBlocProvider', () {
    testWidgets('calls init', (tester) async {
      var initialized = false;
      await tester.load(
        // ignore: deprecated_member_use_from_same_package
        widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
          create: (context) => cubit,
          init: (cubit) async => initialized = true,
          internetConnectivityStream: connectionStatus,
          child: Container(),
        ),
      );

      expect(initialized, isTrue);
    });
    testWidgets(
      'does not call init again if it is currently running',
      (tester) async {
        final completer = Completer<void>();
        cubit.initFuture = completer.future;
        await tester.load(
          widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
            create: (context) => cubit,
            init: (localCubit) async {
              await cubit.init();
            },
            internetConnectivityStream: connectionStatus,
            child: Container(),
          ),
        );

        iCSStreamController.add([ConnectivityResult.ethernet]);
        await tester.pump();
        expect(cubit.timesInitCalled, 1);
        completer.complete();
        await tester.pump();
        iCSStreamController.add([ConnectivityResult.mobile]);
        await tester.idle();

        expect(cubit.timesInitCalled, 2);
      },
    );
    testWidgets('calls init with created cubit', (tester) async {
      var isSameCubit = false;
      await tester.load(
        // ignore: deprecated_member_use
        widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
          create: (context) => cubit,
          init: (localCubit) async => isSameCubit = cubit == localCubit,
          internetConnectivityStream: connectionStatus,
          child: Container(),
        ),
      );

      expect(isSameCubit, isTrue);
    });
    testWidgets('renders child if init succeeds', (tester) async {
      await tester.load(
        // ignore: deprecated_member_use
        widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
          create: (context) => cubit,
          init: (localCubit) async => localCubit.init(),
          internetConnectivityStream: connectionStatus,
          child: const Text('test'),
        ),
      );

      expect(find.text('test'), findsOneWidget);
    });

    testWidgets('renders error screen if init exception is thrown',
        (tester) async {
      await tester.load(
        // ignore: deprecated_member_use
        widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
          create: (context) => cubit,
          init: (localCubit) async => localCubit.init(
            errorType: _ErrorType.throwError,
            error: FlutterError('error'),
          ),
          internetConnectivityStream: connectionStatus,
          child: const Text('test'),
        ),
      );
      // pump so error can render
      await tester.pump();

      expect(find.text('test'), findsNothing);

      validateErrorViewShown(tester, 'error');
    });
    testWidgets(
        'renders error screen if cubit.setFatalError is used during init',
        (tester) async {
      await tester.load(
        // ignore: deprecated_member_use
        widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
          create: (context) => cubit,
          init: (localCubit) async => localCubit.init(
            errorType: _ErrorType.fatalError,
            error: 'error',
          ),
          internetConnectivityStream: connectionStatus,
          child: const Text('test'),
        ),
      );

      // pump so error can render
      await tester.pump();

      expect(find.text('test'), findsNothing);

      validateErrorViewShown(tester, 'error');
    });

    testWidgets(
        'renders error screen if cubit.setFatalError is used outside init',
        (tester) async {
      await tester.load(
        // ignore: deprecated_member_use
        widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
          create: (context) => cubit,
          init: (localCubit) async => localCubit.init(),
          internetConnectivityStream: connectionStatus,
          child: const Text('test'),
        ),
      );

      expect(find.text('test'), findsOneWidget);

      cubit.setFatalError('error');

      // render error screen
      await tester.pump();

      validateErrorViewShown(tester, 'error');
    });

    testWidgets('retry button calls init after adding initial state',
        (tester) async {
      await tester.load(
        // ignore: deprecated_member_use
        widget: BiEmbarkBlocProvider<_TestCubit, _TestState>(
          create: (context) => cubit,
          init: (localCubit) async => localCubit.init(),
          internetConnectivityStream: connectionStatus,
          child: const Text('test'),
        ),
      );

      // update the state so we can check state was reset after error button press
      cubit.emit(_TestState.ten());

      // trigger the error screen
      cubit.setFatalError('error');
      await tester.pump();

      // now tap the retry button
      await tester.tap(find.text(l10n.refresh));
      // allow tap callback to run
      await tester.pump();

      // update the UI to reflect child render
      await tester.pump();

      // state should be reset to zero
      expect(cubit.state, equals(_TestState.zero()));

      // error view should be gone, and the text control re-rendered.
      expect(find.text('test'), findsOneWidget);
    });
  });
}
