import 'package:bi_flutter_app_insights_logging/bi_flutter_app_insights_logging.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/app.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/authentication/auth_steps/showing_dashboard_step.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/call_maker/cubit/call_maker_cubit.dart';
import 'package:embark/device_setup/cubit/_device_setup_cubit.dart';
import 'package:embark/device_setup/cubit/_device_setup_state.dart';
import 'package:embark/internet_connection/internet_connection_listener.dart';
import 'package:embark/navigation_observers/navigation_observers.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/repositories/_app_repository.dart';
import 'package:embark/repositories/_biometric_repository.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/_embark_authentication_repository.dart';
import 'package:embark/repositories/_location_repository.dart';
import 'package:embark/services/_permission_handler.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import 'tester_extensions.dart';
import 'unit_test.mocks.dart';

void main() {
  late MockAppSettings appSettings;
  late MockAppRepository appRepository;
  late MockArrivedOnDashboardObserver arrivedOnDashboardObserver;
  late MockInternetConnectionListener internetConnectionListener;
  late MockDeviceSetupCubit deviceSetupCubit;
  late MockPushNotificationListenerCubit pushNotificationListenerCubit;
  late MockSharedPreferencesService sharedPreferencesService;
  late MockEmbarkAuthenticationRepository embarkAuthenticationRepository;
  late MockAuthManager authManager;
  late MockCallMakerCubit callMakerCubit;
  late MockEmbarkAuthenticationBloc embarkAuthenticationBloc;
  late MockDeviceSetupRepository deviceSetupRepository;
  late BiStreamController<List<ConnectivityResult>> connectionChanges;
  late MockBiTokenManager mockBiTokenManager;
  late MockPermissionHandler permissionHandler;
  late MockClientSettingsRepository clientSettingsRepository;
  late MockBiBluetoothApi bluetoothApi;
  late MockLocationRepository locationRepository;
  late MockBiLogger logger;
  late MockBiometricRepository biometricRepository;

  group('app view', () {
    setUp(() {
      appSettings = MockAppSettings();
      appRepository = MockAppRepository();
      arrivedOnDashboardObserver = MockArrivedOnDashboardObserver();
      internetConnectionListener = MockInternetConnectionListener();
      deviceSetupCubit = MockDeviceSetupCubit();
      pushNotificationListenerCubit = MockPushNotificationListenerCubit();
      sharedPreferencesService = MockSharedPreferencesService();
      embarkAuthenticationRepository = MockEmbarkAuthenticationRepository();
      permissionHandler = MockPermissionHandler();
      authManager = MockAuthManager();
      callMakerCubit = MockCallMakerCubit();
      embarkAuthenticationBloc = MockEmbarkAuthenticationBloc();
      deviceSetupRepository = MockDeviceSetupRepository();
      mockBiTokenManager = MockBiTokenManager();
      clientSettingsRepository = MockClientSettingsRepository();
      bluetoothApi = MockBiBluetoothApi();
      locationRepository = MockLocationRepository();
      logger = MockBiLogger();
      biometricRepository = MockBiometricRepository();

      connectionChanges =
          BiStreamController<List<ConnectivityResult>>(sync: true);

      // ignore: deprecated_member_use_from_same_package
      getIt.registerSingleton<PermissionHandler>(permissionHandler);
      getIt.registerSingleton<AppSettings>(appSettings);
      getIt.registerSingleton<AppRepository>(
        appRepository,
        instanceName: 'AppRepository',
      );
      getIt.registerSingleton<ArrivedOnDashboardObserver>(
        arrivedOnDashboardObserver,
      );
      getIt.registerSingleton(CurrentRouteObserver());
      getIt.registerSingleton<InternetConnectionListener>(
        internetConnectionListener,
      );
      getIt.registerSingleton<Stream<List<ConnectivityResult>>>(
        connectionChanges.stream,
      );
      getIt.registerSingleton<DeviceSetupCubit>(deviceSetupCubit);
      getIt.registerSingleton<PushNotificationListenerCubit>(
        pushNotificationListenerCubit,
      );
      getIt.registerSingleton<SharedPreferencesService>(
        sharedPreferencesService,
      );
      getIt.registerSingleton<EmbarkAuthenticationRepository>(
        embarkAuthenticationRepository,
      );
      getIt.registerSingleton<BiTokenManager>(
        mockBiTokenManager,
      );
      getIt.registerSingleton(ShowingDashboardStep());
      getIt.registerSingleton<AuthManager>(authManager);
      getIt.registerSingleton<CallMakerCubit>(callMakerCubit);
      getIt.registerSingleton<EmbarkAuthenticationBloc>(
        embarkAuthenticationBloc,
      );
      getIt.registerSingleton<DeviceSetupRepository>(deviceSetupRepository);
      getIt.registerSingleton(
        BiPermissionRequestRepository(
          bluetoothApi: bluetoothApi,
        ),
      );
      getIt.registerSingleton<LocationRepository>(locationRepository);
      getIt.registerSingleton<BiLogger>(logger);
      getIt.registerSingleton<BiometricRepository>(biometricRepository);
      when(appRepository.getStoredLocale())
          .thenAnswer((_) => const Locale('en_US'));
      when(deviceSetupCubit.state).thenReturn(
        const DeviceSetupState(
          status: DeviceSetupStatus.deviceReady,
          resolvedDeviceType: ResolvedDeviceType.byod,
        ),
      );
      when(clientSettingsRepository.clientSettingsListener)
          .thenAnswer((_) => ValueNotifier<GetClientSettingsResponse?>(null));
    });
    tearDown(() {
      connectionChanges.close();
      getIt.reset();
    });

    testWidgets('AppView loads correctly', (tester) async {
      await tester.load(
        widget: App(),
      );
      expect(find.byType(MaterialApp), findsOneWidget);
    });
  });
}
