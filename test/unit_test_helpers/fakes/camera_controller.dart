import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../data/transparent_image.dart';

class FakeCameraController extends CameraController {
  FakeCameraController(
    super.description, [
    super.resolutionPreset = ResolutionPreset.low,
  ]);

  @override
  Widget buildPreview() {
    return Container(
      color: Colors.orange,
      key: const Key('preview'),
    );
  }

  @override
  Future<double> getExposureOffsetStepSize() {
    throw UnimplementedError();
  }

  @override
  Future<double> getMaxExposureOffset() {
    throw UnimplementedError();
  }

  @override
  Future<double> getMaxZoomLevel() {
    throw UnimplementedError();
  }

  @override
  Future<double> getMinExposureOffset() {
    throw UnimplementedError();
  }

  @override
  Future<double> getMinZoomLevel() {
    return Future.value(1.0);
  }

  bool initCalled = false;

  Future<void> defaultInitialize() async {
    initCalled = true;
    final defaultCameraValue = CameraValue(
      isInitialized: true,
      isRecordingVideo: false,
      isTakingPicture: false,
      isStreamingImages: false,
      isRecordingPaused: false,
      flashMode: FlashMode.always,
      exposureMode: ExposureMode.auto,
      focusMode: FocusMode.auto,
      exposurePointSupported: false,
      focusPointSupported: false,
      previewSize: const Size(300, 300),
      deviceOrientation: DeviceOrientation.portraitUp,
      description: description,
    );
    value = defaultCameraValue;
  }

  late Future<void> Function() initializeCall = defaultInitialize;

  @override
  Future<void> initialize() async {
    if (disposeCalled) {
      throw CameraException(
        'Disposed CameraController',
        'initialize was called on a disposed CameraController',
      );
    }
    await initializeCall();
    if (disposeCalled) {
      throw CameraException(
        'Disposed CameraController',
        'initialize was called on a disposed CameraController',
      );
    }
  }

  bool disposeCalled = false;
  @override
  Future<void> dispose() {
    disposeCalled = true;
    return super.dispose();
  }

  @override
  Future<void> lockCaptureOrientation([DeviceOrientation? orientation]) async {
    value = value.copyWith(
      lockedCaptureOrientation: Optional.fromNullable(orientation),
    );
  }

  @override
  Future<void> pausePreview() {
    return Future.value();
  }

  @override
  Future<void> pauseVideoRecording() {
    throw UnimplementedError();
  }

  @override
  Future<void> prepareForVideoRecording() {
    throw UnimplementedError();
  }

  @override
  ResolutionPreset get resolutionPreset => throw UnimplementedError();

  @override
  Future<void> resumePreview() {
    throw UnimplementedError();
  }

  @override
  Future<void> resumeVideoRecording() {
    throw UnimplementedError();
  }

  @override
  Future<void> setDescription(CameraDescription description) {
    throw UnimplementedError();
  }

  @override
  Future<void> setExposureMode(ExposureMode mode) {
    throw UnimplementedError();
  }

  @override
  Future<double> setExposureOffset(double offset) {
    throw UnimplementedError();
  }

  @override
  Future<void> setExposurePoint(Offset? point) {
    throw UnimplementedError();
  }

  @override
  Future<void> setFlashMode(FlashMode mode) async {
    value = value.copyWith(flashMode: mode);
  }

  @override
  Future<void> setFocusMode(FocusMode mode) {
    throw UnimplementedError();
  }

  @override
  Future<void> setFocusPoint(Offset? point) {
    throw UnimplementedError();
  }

  @override
  Future<void> setZoomLevel(double val) {
    return Future.value();
  }

  @override
  Future<void> startImageStream(onLatestImageAvailable onAvailable) {
    return Future.value();
  }

  @override
  Future<void> startVideoRecording({onLatestImageAvailable? onAvailable}) {
    throw UnimplementedError();
  }

  @override
  Future<void> stopImageStream() {
    return Future.value();
  }

  @override
  Future<XFile> stopVideoRecording() {
    throw UnimplementedError();
  }

  late Future<XFile> Function() pictureData =
      () async => XFile.fromData(transparentImageUint8);
  var numberOfTimesTakePictureCalled = 0;
  @override
  Future<XFile> takePicture() async {
    numberOfTimesTakePictureCalled++;
    value = value.copyWith(isTakingPicture: true);
    final picture = await pictureData();
    value = value.copyWith(isTakingPicture: false);
    return picture;
  }

  @override
  Future<void> unlockCaptureOrientation() async {
    value = value.copyWith(
      lockedCaptureOrientation: const Optional<DeviceOrientation>.absent(),
    );
  }
}
